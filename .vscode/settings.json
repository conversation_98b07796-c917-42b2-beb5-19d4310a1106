{
  "editor.tabSize": 2,
  "editor.detectIndentation": false,
  "jest.autoRun": {
    "watch": false,
    "onSave": "test-file"
  },
  "search.exclude": {
    "package-lock.json": true
  },
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "editor.formatOnSave": false,
  "editor.codeActionsOnSave": [
    "source.addMissingImports",
    "source.fixAll.eslint"
  ],
  // Multiple language settings for json and jsonc files
  "[json][jsonc]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
}
