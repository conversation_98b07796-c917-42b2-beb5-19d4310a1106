const http = require('http');
const httpProxy = require('http-proxy');

const proxy = httpProxy.createProxyServer({});

proxy.on('proxyRes', function (proxyRes, req, res) {
  // 删除所有现有的 CORS 相关头
  delete proxyRes.headers['access-control-allow-origin'];
  delete proxyRes.headers['access-control-allow-credentials'];
  delete proxyRes.headers['access-control-allow-methods'];
  delete proxyRes.headers['access-control-allow-headers'];

  // 设置新的 CORS 头
  proxyRes.headers['Access-Control-Allow-Origin'] = 'http://localhost:3000';
  proxyRes.headers['Access-Control-Allow-Credentials'] = 'true';
  proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS';
  proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, X-Original-Cookie, Authorization';

  // 如果是预检请求，立即结束响应
  if (req.method === 'OPTIONS') {
    res.writeHead(200, proxyRes.headers);
    res.end();
  }
});

const server = http.createServer(function(req, res) {
  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': 'http://localhost:3000',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Original-Cookie, Authorization'
    });
    res.end();
    return;
  }

  // 将原始 cookie 传递给目标服务器
  const cookie = req.headers['x-original-cookie'];
  if (cookie) {
    req.headers['Cookie'] = cookie;
  }

  proxy.web(req, res, {
    target: 'https://api.myinvestpilot.com',
    changeOrigin: true
  });
});

console.log("listening on port 8080")
server.listen(8080);
