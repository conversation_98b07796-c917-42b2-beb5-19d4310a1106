<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变色 -->
  <defs>
    <linearGradient id="neutral-gradient" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="40%" style="stop-color:#4a90e2; stop-opacity:1" /> <!-- 蓝色 -->
      <stop offset="60%" style="stop-color:#f5a623; stop-opacity:1" /> <!-- 黄色 -->
    </linearGradient>
  </defs>

  <!-- 绘制白色背景正方形 -->
  <rect width="200" height="200" fill="white" />

  <!-- 使用中性色调的渐变圆形 -->
  <circle cx="100" cy="100" r="90" fill="url(#neutral-gradient)" />

  <!-- 在圆上绘制从左下到右上的白色曲线 -->
  <path d="M8,100 C30,70 70,130 100,120 C130,110 160,90 190,70" stroke="white" stroke-width="3" fill="none"/>

  <text x="50%" y="85%" fill="white" font-family="Arial" font-size="20" text-anchor="middle" dominant-baseline="central">
    AI
  </text>
</svg>
