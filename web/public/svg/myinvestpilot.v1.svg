<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义红色和绿色填充的渐变色 -->
  <defs>
    <linearGradient id="up-gradient" x1="0%" y1="100%" x2="0%" y2="0%">
      <stop offset="0%" style="stop-color:#cc5595; stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cc5595; stop-opacity:1" />
    </linearGradient>
    <linearGradient id="down-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#33aa76; stop-opacity:1" />
      <stop offset="100%" style="stop-color:#33aa76; stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 绘制白色背景正方形 -->
  <rect width="200" height="200" fill="white" />

  <!-- 使用填充色#cc5595的圆覆盖边缘 -->
  <circle cx="100" cy="100" r="90" fill="#cc5595" />

  <!-- 使用剪切路径，只显示圆形内部的曲线 -->
  <g clip-path="url(#clip-circle)">
    <!-- 上半部分用红色填充 -->
    <path d="M0,0 Q50,100 200,50 V0 Z" fill="url(#up-gradient)"/>
    <!-- 下半部分用绿色填充 -->
    <path d="M0,200 Q50,100 200,50 V200 Z" fill="url(#down-gradient)"/>
    <!-- 在曲线上绘制白色线条 -->
    <path d="M10,160 Q30,110 70,130 Q110,150 150,100 Q170,80 190,60" stroke="white" stroke-width="3" fill="none"/>
  </g>

  <!-- 定义圆形剪切路径 -->
  <defs>
    <clipPath id="clip-circle">
      <circle cx="100" cy="100" r="90" />
    </clipPath>
  </defs>
</svg>
