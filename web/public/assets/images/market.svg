<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 444 192">
  <defs>
    <style>
      @keyframes pulse {
        0% { transform: scale(1); opacity: 0.7; }
        50% { transform: scale(1.1); opacity: 1; }
        100% { transform: scale(1); opacity: 0.7; }
      }
      .future-market { animation: pulse 3s infinite; }
    </style>
  </defs>
  
  <rect width="444" height="192" fill="#f0f0f0"/>
  
  <!-- A股 -->
  <g transform="translate(20, 96)">
    <path d="M0,0 Q26,-15 52,0 T104,0" fill="none" stroke="#ff3333" stroke-width="2">
      <animate attributeName="d" dur="4s" repeatCount="indefinite"
        values="M0,0 Q26,-15 52,0 T104,0;
                M0,0 Q26,15 52,0 T104,0;
                M0,0 Q26,-15 52,0 T104,0" />
    </path>
    <text x="52" y="30" text-anchor="middle" font-family="Arial" font-size="14" fill="#333">A股</text>
  </g>
  
  <!-- 美股 -->
  <g transform="translate(170, 96)">
    <path d="M0,20 C26,10 52,30 78,0 S104,10 130,-10" fill="none" stroke="#4CAF50" stroke-width="2">
      <animate attributeName="d" dur="4s" repeatCount="indefinite"
        values="M0,20 C26,10 52,30 78,0 S104,10 130,-10;
                M0,15 C26,25 52,5 78,15 S104,-5 130,-15;
                M0,20 C26,10 52,30 78,0 S104,10 130,-10" />
    </path>
    <text x="65" y="40" text-anchor="middle" font-family="Arial" font-size="14" fill="#333">美股</text>
  </g>
  
  <!-- 加密币 -->
  <g transform="translate(320, 96)">
    <path d="M0,0 C22,-20 43,20 65,-10 S87,20 108,-15" fill="none" stroke="#f7931a" stroke-width="2">
      <animate attributeName="d" dur="4s" repeatCount="indefinite"
        values="M0,0 C22,-20 43,20 65,-10 S87,20 108,-15;
                M0,10 C22,-10 43,30 65,0 S87,10 108,-25;
                M0,0 C22,-20 43,20 65,-10 S87,20 108,-15" />
    </path>
    <text x="54" y="35" text-anchor="middle" font-family="Arial" font-size="14" fill="#333">加密币</text>
  </g>
  
  <!-- 未来市场 -->
  <circle cx="222" cy="20" r="8" fill="#999" class="future-market"/>
  <circle cx="422" cy="70" r="10" fill="#777" class="future-market"/>
  <circle cx="22" cy="170" r="9" fill="#888" class="future-market"/>
  
  <text x="222" y="180" text-anchor="middle" font-family="Arial" font-size="16" fill="#666" font-weight="bold">更多市场，敬请期待</text>
</svg>