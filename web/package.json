{"name": "invest-alchemy-web", "version": "0.0.1", "scripts": {"dev": "NODE_ENV=development next dev", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "export": "next export", "build-prod": "run-s clean build export", "cf-build": "npx @cloudflare/next-on-pages@1", "clean": "rimraf .next out", "lint": "next lint", "build-types": "tsc --noEmit --pretty"}, "dependencies": {"@heroicons/react": "^2.1.5", "@types/react-datepicker": "^7.0.0", "ajv": "^8.17.1", "antd": "^5.26.0", "classnames": "^2.3.1", "echarts": "^5.3.3", "echarts-for-react": "^3.0.2", "framer-motion": "^11.5.4", "json-edit-react": "^1.27.1", "lucide-react": "^0.441.0", "mermaid": "^10.9.0", "next": "^13.4.19", "next-pwa": "^5.6.0", "next-seo": "^4.29.0", "react": "^18.2.0", "react-datepicker": "^7.3.0", "react-dom": "^18.2.0", "react-loader-spinner": "^5.3.4", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "sql.js": "^1.8.0", "styled-jsx-plugin-postcss": "^4.0.1"}, "devDependencies": {"@next/bundle-analyzer": "^12.0.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^17.0.13", "@types/react": "^18.2.0", "@types/sql.js": "^1.4.3", "@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "autoprefixer": "^10.4.2", "cross-env": "^7.0.3", "cssnano": "^5.0.16", "eslint": "^8.7.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^16.1.0", "eslint-config-next": "^12.0.9", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-unused-imports": "^2.0.0", "lint-staged": "^12.3.2", "npm-run-all": "^4.1.5", "postcss": "^8.4.5", "prettier": "^2.5.1", "rimraf": "^3.0.2", "tailwindcss": "^3.0.17", "typescript": "^5.0.0"}, "license": "ISC"}