# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Service worker files
public/sw.js
public/sw.js.map
public/workbox-*.js
public/workbox-*.js.map
public/fallback-*.js

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next
/out

# production
/build

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# dotenv local files
.env*.local

# local folder
local

# vercel
.vercel
