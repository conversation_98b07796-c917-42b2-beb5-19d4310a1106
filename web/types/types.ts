export interface PerformanceData {
  currentDrawdown: number;
  maxDrawdown: number;
  cagr: number;
  xirr: number | null;
  sharpeRatio: number;
  volatility?: number;
  totalTrades: number;
  profitTrades: number;
  lossTrades: number;
  winRate: number;
  profitFactor: number;
  sqn: number;
  vwr: number;
  calmar: number;
  runningDays: number;
  maxDrawdownDurationDays: number;
}

export interface BenchmarkData {
  cagr: number;
  maxDrawdown: number;
  sharpeRatio: number;
  volatility?: number;
  overallReturn: number;
  initialCapital: number;
  finalValue: number;
}

export interface ChartData {
  dates: string[];
  netValues: number[];
  ZZ500: number[];
  HS300: number[];
  CYB: number[];
  HSI: number[];
  SPX: number[];
  IXIC: number[];
  GDAXI: number[];
  N225: number[];
  KS11: number[];
  AS51: number[];
  SENSEX: number[];
  annualReturn15: number[];
}

export interface IPost {
  id: string;
  title: string;
  description: string;
  image?: string;
  date?: number;
}

export interface IPostResponse {
  slug?: string;
  error?: string;
  cover_image: string;
  meta: string;
  extra: string;
  post_date: number;
  title: string;
  markdown: string;
  html: string;
  type: string;
}

export interface Strategy {
  name: string;
  description?: string;
  params: Record<string, string | number> | string;
}

export interface PortfolioSymbol {
  symbol: string;
  name: string;
  type: string;
  market: string;
  country: string;
  currency: string;
}

export interface Portfolio {
  id?: string;
  name: string;
  code: string;
  description: string;
  strategy?: Strategy;
  capital_strategy?: Strategy;
  strategy_definition?: {
    trade_strategy: any;
    capital_strategy: Strategy;
    market_indicators?: {
      indicators: Array<{ code: string }>;
      transformers: Array<{
        name: string;
        type: string;
        params: Record<string, any>;
      }>;
    };
  };
  symbols: (string | PortfolioSymbol)[];
  start_date: string;
  end_date?: string;
  currency: string;
  market: string;
  commission: number;
  update_time: string;
  is_official?: boolean;
}

export interface Profile {
  email: string;
  premium_end_date: number;
  upsert_at: number;
}

export interface PublicPortfolioInfo {
  code: string;
  name: string;
  description: string;
  currency: string;
  market: string;
  start_date: string;
  is_subscribable: boolean;
  is_deleted: boolean;
}

export interface MarketGroup {
  market: string;
  official_portfolios: Portfolio[];
}

export interface StrategyGroup {
  strategy_id: string;
  strategy_name: string;
  strategy_description: string;
  official_portfolios: Portfolio[];
}

export interface StrategyParameter {
  default: number | string;
  description: string;
  gt?: number;
  le?: number;
  enum?: string[];
}

export interface CapitalStrategy {
  strategy_description: string;
  strategy_id: string;
  strategy_name: string;
  strategy_parameters: {
    [key: string]: StrategyParameter;
  };
}

export type GroupedPortfolios = MarketGroup[] | StrategyGroup[];

export interface PortfoliosProps {
  portfolios: MarketGroup[];
}

export interface StrategyProps {
  strategies: StrategyGroup[];
}

export type CreatePortfolioRequest = Omit<
  Portfolio,
  'id' | 'code' | 'is_official' | 'end_date'
>;
export type UpdatePortfolioRequest = Partial<CreatePortfolioRequest>;

// New types for strategy DSL generation
export interface StrategyDSLResponse {
  success: boolean;
  tradeStrategy?: any; // Strategy definition structure
  metadata?: {
    promptVersion: string;
    model: string;
    tokensUsed: number;
  };
}

export interface StrategyGenerationRequest {
  userInput: string;
}

// Schema type for JSON validation
export interface JSONSchema {
  type: string;
  properties?: Record<string, any>;
  required?: string[];
  [key: string]: any;
}

// Template interface
export interface StrategyTemplate {
  name: string;
  description: string;
  dslJson: any;
}
