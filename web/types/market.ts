export type MarketZone =
  | '极低'
  | '较低'
  | '低'
  | '中性'
  | '高'
  | '较高'
  | '极高';

export type MarketRiskLevel =
  | '极低'
  | '较低'
  | '低'
  | '中性'
  | '高'
  | '较高'
  | '极高';

export type MarketType = 'cn' | 'us' | 'crypto';

// 基础指标数据类型
export interface IndicatorData {
  latest?: number;
  latest_pe?: number;
  latest_percentile?: number;
  zone: MarketZone;
  risk_level: MarketRiskLevel;
  suggestion: string;
  stats: Record<string, number>;
  date?: string;
}

// 股债比数据类型
export interface EquityBondRatioData {
  date: string;
  current_levels: {
    earnings_yield: number;
    cn_bond_yield: number;
    us_bond_yield: number;
  };
  china_perspective: {
    ratio: number;
    percentile: number;
    stats: Record<string, number>;
  };
  us_perspective: {
    ratio: number;
    percentile: number;
    stats: Record<string, number>;
  };
  analysis: {
    zone: MarketZone;
    risk_level: MarketRiskLevel;
    suggestion: string;
  };
}

// A股市场数据结构
export interface CNMarketData {
  last_update: string;
  market: 'CN';
  indicators: {
    sbrd: IndicatorData;
    pe_percentile: IndicatorData;
    equity_bond_ratio: EquityBondRatioData;
  };
  risk_assessment: {
    score: number;
    level: MarketRiskLevel;
    summary: string;
  };
  ai_analysis: {
    role: string;
    content: string;
    refusal: null | string;
  };
}

// 美股宏观指标类型
export interface MacroIndicator {
  current: number;
  percentile: number;
  stats: {
    min: number;
    max: number;
    mean: number;
    median: number;
    std: number;
  };
  trend: {
    changes: {
      '1m': number;
      '3m': number;
      '6m': number;
      '12m': number;
    };
    trend: string;
    momentum: string;
    latest_changes: number[];
  };
}

export interface MacroIndicators {
  date: string;
  indicators: {
    bond_yield: MacroIndicator;
    unemployment: MacroIndicator;
    inflation: MacroIndicator;
    pce: MacroIndicator;
  };
  risk_assessment: {
    score: number;
    level: MarketRiskLevel;
    summary: string;
  };
}

// 美股市场数据结构
export interface USMarketData {
  last_update: string;
  market: 'US';
  indicators: {
    macro: MacroIndicators;
  };
  ai_analysis: {
    role: string;
    content: string;
    refusal: null | string;
  };
}

export interface CryptoIndicator extends MacroIndicator {
  risk_level: MarketRiskLevel;
}

export interface CryptoIndicators {
  date: string;
  indicators: {
    mvrv: CryptoIndicator;
    nvt: CryptoIndicator;
    fear_greed: CryptoIndicator;
    volatility: CryptoIndicator;
    active_addresses: CryptoIndicator;
  };
  risk_assessment: {
    score: number;
    level: MarketRiskLevel;
    summary: string;
  };
}

export interface CryptoMarketData {
  last_update: string;
  market: 'CRYPTO';
  indicators: {
    crypto: CryptoIndicators;
  };
  ai_analysis: {
    role: string;
    content: string;
    refusal: null | string;
  };
}

// 联合类型表示所有可能的市场数据类型
export type MarketData = CNMarketData | USMarketData | CryptoMarketData;
