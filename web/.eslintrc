{
  // Configuration for JavaScript files
  "extends": [
    "airbnb-base",
    "next/core-web-vitals",
    "plugin:prettier/recommended"
  ],
  "rules": {
    "prettier/prettier": "off",
    "no-console": "off"
  },
  "overrides": [
    // Configuration for TypeScript files
    {
      "files": ["**/*.ts", "**/*.tsx"],
      "plugins": ["@typescript-eslint", "unused-imports"],
      "extends": [
        "airbnb-typescript",
        "next/core-web-vitals",
        "plugin:prettier/recommended"
      ],
      "parserOptions": {
        "project": "./tsconfig.json"
      },
      "rules": {
        "prettier/prettier": "off",
        "no-console": "off",
        "react/no-unescaped-entities": "off",
        "react/destructuring-assignment": "off", // Vscode doesn't support automatically destructuring, it's a pain to add a new variable
        "jsx-a11y/anchor-is-valid": "off", // Next.js use his own internal link system
        "react/require-default-props": "off", // Allow non-defined react props as undefined
        "react/jsx-props-no-spreading": "off", // _app.tsx uses spread operator and also, react-hook-form
        "@next/next/no-img-element": "off", // We currently not using next/image because it isn't supported with SSG mode
        "import/order": "off", // Too strict for development
        "import/extensions": "off", // Allow imports without file extensions for TypeScript
        "@typescript-eslint/comma-dangle": "off", // Avoid conflict rule between Eslint and Prettier
        "import/prefer-default-export": "off", // Named export is easier to refactor automatically
        "class-methods-use-this": "off", // _document.tsx use render method without `this` keyword
        "@typescript-eslint/no-unused-vars": "off",
        "@typescript-eslint/no-shadow": "off", // Allow variable shadowing in development
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
          "error",
          { "argsIgnorePattern": "^_" }
        ]
      }
    }
  ]
}
