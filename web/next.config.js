/* eslint-disable import/no-extraneous-dependencies */
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const withPWA = require('next-pwa')({
  dest: 'public',
});

// Combine multiple Next.js plugins
const nextConfig = {
  poweredByHeader: false,
  trailingSlash: true,
  basePath: '',
  // The starter code load resources from `public` folder with `router.basePath` in React components.
  // So, the source code is "basePath-ready".
  // You can remove `basePath` if you don't need it.
  reactStrictMode: true,
  
  // Add redirects configuration
  async redirects() {
    return [
      {
        source: '/strategy',
        destination: '/portfolios',
        permanent: true, // Returns 308 status code (permanent redirect)
      },
    ];
  },
  
  // Webpack configuration
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // eslint-disable-next-line no-param-reassign
      config.resolve.fallback.fs = false;
    }
    return config;
  },
};

// Apply plugins
module.exports = withBundleAnalyzer(withPWA(nextConfig));
