import { GetServerSideProps } from 'next';

import { Portfolios } from '../../templates/Portfolios';
import { ApiError, getOfficialPortfolios } from '../../utils/api';
import { StrategyGroup } from '../../../types/types';

// Updated Props interface that uses StrategyGroup
interface PortfoliosProps {
  portfolios: StrategyGroup[];
}

export const runtime = 'experimental-edge';

const PortfoliosPage: React.FC<PortfoliosProps> = ({ portfolios }) => {
  return <Portfolios portfolios={portfolios} />;
};

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  try {
    // 从API获取官方组合，按策略分组
    const portfolios = await getOfficialPortfolios('strategy', req.headers.cookie);

    return {
      props: {
        portfolios,
      },
    };
  } catch (err) {
    if (err instanceof ApiError) {
      console.error(
        `API Error: ${err.status} ${err.statusText} - ${err.message}`
      );
      // 你可以根据错误类型进行不同的处理
      if (err.status === 401) {
        // 处理未授权错误
        return {
          redirect: {
            destination: '/login',
            permanent: false,
          },
        };
      }
    } else {
      console.error('Unexpected error:', err);
    }
    return { props: { portfolios: [] } };
  }
};

export default PortfoliosPage;
