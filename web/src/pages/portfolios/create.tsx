import React from 'react';
import { useRouter } from 'next/router';

import PrimitiveStrategyForm from '../../components/PrimitiveStrategyForm';
import { Meta } from '../../layout/Meta';
import { Section } from '../../layout/Section';
import { Footer } from '../../templates/Footer';
import { Header } from '../../templates/Header';
import { AppConfig } from '../../utils/AppConfig';

const CreatePortfolioPage: React.FC = () => {
  const router = useRouter();
  const { mode, code } = router.query;

  const getPageTitle = () => {
    switch (mode) {
      case 'update':
        return '更新原语策略投资组合';
      default:
        return '创建原语策略组合';
    }
  };

  return (
    <div className="antialiased text-gray-600">
      <Meta
        title={`${getPageTitle()} - ${AppConfig.title}`}
        description={AppConfig.description}
      />
      <Header />
      <Section title={getPageTitle()}>
        <PrimitiveStrategyForm
          mode={(mode as 'create' | 'update') || 'create'}
          initialCode={code as string}
        />
      </Section>
      <Footer />
    </div>
  );
};

export default CreatePortfolioPage;
