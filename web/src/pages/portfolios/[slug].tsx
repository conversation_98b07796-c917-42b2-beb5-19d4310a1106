import React, { useState, useEffect, useRef } from 'react';

import { EChartsOption } from 'echarts-for-react';
import { useRouter } from 'next/router';
import { InfinitySpin } from 'react-loader-spinner';
import initSqlJs, { SqlJsStatic } from 'sql.js';

import {
  ChartData,
  PerformanceData,
  PublicPortfolioInfo,
  BenchmarkData,
} from '../../../types/types';
import AnnualReturns from '../../components/AnnualReturns';
import PortfolioChart from '../../components/PortfolioChart';
import PortfolioCockpit from '../../components/PortfolioCockpit';
import FAQSection from '../../components/PortfolioFAQSection';
import PortfolioTabs from '../../components/PortfolioTabs';
import PortfolioTimeMachine from '../../components/PortfolioTimeMachine';
import PortfolioTradeSignalsButton from '../../components/PortfolioTradeSignalsButton';
import ReturnAttribution, { getMarketType } from '../../components/ReturnAttribution';
import RiskDisclaimer from '../../components/RiskDisclaimer';
import RiskLevelBanner from '../../components/RiskLevelBanner';
import { Meta } from '../../layout/Meta';
import { Footer } from '../../templates/Footer';
import { Header } from '../../templates/Header';
import {
  getPortfolioSubscriptionStatus,
  getPublicPortfolioInfo,
  subscribePortfolio,
  timeFlyPortfolio,
} from '../../utils/api';
import { AppConfig } from '../../utils/AppConfig';
import { generateChartOptions } from '../../utils/chartOptionsHelper';
import { getDbUrl } from '../../utils/db';
import { trackEvent } from '../../utils/trackEvent';

const pageLimit = 100;

const loadSqlJs = (() => {
  let sqlPromise: Promise<SqlJsStatic> | null = null;

  return () => {
    if (!sqlPromise) {
      sqlPromise = initSqlJs({
        locateFile: (file) =>
          `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`,
      });
    }
    return sqlPromise;
  };
})();

const loadDatabase = async (dbURL: string, retries = 3) => {
  const SQL = await loadSqlJs();
  const fetchWithRetry = async (
    url: string,
    retriesLeft: number
  ): Promise<ArrayBuffer> => {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.arrayBuffer();
    } catch (error) {
      if (retriesLeft > 0) {
        return fetchWithRetry(url, retriesLeft - 1);
      }
      throw error;
    }
  };

  const buf = await fetchWithRetry(dbURL, retries);
  const db = new SQL.Database(new Uint8Array(buf));
  return db;
};

const execSafely = (sqlDB: any, sql: string): any[] => {
  try {
    const results = sqlDB.exec(sql);
    return results.length > 0 ? results[0].values : [];
  } catch (error) {
    console.error(`Error executing SQL: ${sql}`, error);
    return [];
  }
};

const Portfolio: React.FC = () => {
  const router = useRouter();
  const { slug } = router.query;

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<any>(null);
  const [options, setOptions] = useState<EChartsOption>({});
  const [portfolioCode, setPortfolioCode] = useState<string>();
  const [portfolio, setPortfolio] = useState<PublicPortfolioInfo | null>(null);
  const [netValue, setNetValue] = useState<string>();
  const [latestTradeDate, setLatestTradeDate] = useState<string>();
  const [firstFundingDate, setFirstFundingDate] = useState<string>();
  const [performance, setPerformance] = useState<PerformanceData>();
  const [currentTab, setCurrentTab] = useState<string>('transaction');
  const [transactions, setTransactions] = useState<string[][]>();
  const [holdings, setHoldings] = useState<string[][]>();
  const [fundings, setFundings] = useState<string[][]>();
  const sqlDB: any = useRef();
  const [transactionsPageIndex, setTransactionsPageIndex] = useState<number>(1);
  const [transactionsPageNum, setTransactionsPageNum] = useState<number>(0);
  const [holdingsPageIndex, setHoldingsPageIndex] = useState<number>(1);
  const [holdingsPageNum, setHoldingsPageNum] = useState<number>(0);
  const [fundingsPageIndex, setFundingsPageIndex] = useState<number>(1);
  const [fundingsPageNum, setFundingsPageNum] = useState<number>(0);

  const [timeMachineYear, setTimeMachineYear] = useState<string | null>(null);
  const [isTimeFlying, setIsTimeFlying] = useState(false);

  const [chartKey, setChartKey] = useState(0);

  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriptionError, setSubscriptionError] = useState(false);

  const [yearRange, setYearRange] = useState<string[]>([]);

  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(true);

  const [isDeleted, setIsDeleted] = useState<boolean>(false);

  const [annualReturns, setAnnualReturns] = useState<string>();

  const [benchmark, setBenchmark] = useState<BenchmarkData | undefined>();

  useEffect(() => {
    if (yearRange.length === 0 && firstFundingDate) {
      const startYearString = firstFundingDate.split('-')[0];
      if (startYearString) {
        const startYear = parseInt(startYearString, 10) + 1;
        const currentYear = new Date().getFullYear();
        const range = Array.from(
          { length: currentYear - startYear + 1 },
          (_, i) => (startYear + i).toString()
        );
        setYearRange(range);
      }
    }
  }, [firstFundingDate, yearRange]);

  const handleTimeFly = async (year: string) => {
    if (!portfolio || !portfolio.code) return;

    setIsTimeFlying(true);
    setTimeMachineYear(year);
    await trackEvent('UserTimeFlyPortfolio', {});

    const pollAPI = async () => {
      try {
        await timeFlyPortfolio(portfolio.code, year);
        const newPortfolioCode = `${portfolio.code}~${year}`;
        setPortfolioCode(newPortfolioCode);
        setIsTimeFlying(false);
      } catch (e) {
        setTimeout(pollAPI, 30000); // retry every 30s
      }
    };
    pollAPI();
  };

  const setTransactionsData = (pageNum: number) => {
    try {
      const result = sqlDB.current.exec(
        `SELECT date, code, name, type, ROUND(size,2), ROUND(price,2), ROUND(amount,2) 
         FROM trade_records 
         ORDER BY date DESC 
         LIMIT ${pageLimit} 
         OFFSET ${pageLimit * pageNum - pageLimit}`
      );
      const portfolioTransactions = result[0]?.values || [];
      setTransactions(portfolioTransactions);
    } catch (e) {
      console.error('Error fetching transactions:', e);
      setTransactions([]);
    }
  };

  const setHoldingsData = (pageNum: number) => {
    try {
      const result = sqlDB.current.exec(
        `SELECT date, symbol, ROUND(position_size,2), ROUND(close_price,2), ROUND(value,2) 
         FROM position_records 
         ORDER BY date DESC 
         LIMIT ${pageLimit} 
         OFFSET ${pageLimit * pageNum - pageLimit}`
      );
      const portfolioHoldings = result[0]?.values || [];
      setHoldings(portfolioHoldings);
    } catch (e) {
      console.error('Error fetching holdings:', e);
      setHoldings([]);
    }
  };

  const setFundingsData = (pageNum: number) => {
    try {
      const result = sqlDB.current.exec(
        `SELECT date, trade_type, ROUND(change_amount,2), ROUND(available_cash,2), ROUND(total_assets,2) 
         FROM capital_records 
         ORDER BY date DESC 
         LIMIT ${pageLimit} 
         OFFSET ${pageLimit * pageNum - pageLimit}`
      );
      const portfolioFundings = result[0]?.values || [];
      setFundings(portfolioFundings);
    } catch (e) {
      console.error('Error fetching fundings:', e);
      setFundings([]);
    }
  };

  const handleSubscription = async () => {
    if (!portfolio?.code) {
      return;
    }

    try {
      // Always allow unsubscribe, only prevent new subscriptions for deleted portfolios
      if (isSubscribed || !isDeleted) {
        const status = isSubscribed ? 'inactive' : 'active';
        await subscribePortfolio(portfolio.code, status);
        setIsSubscribed(!isSubscribed);

        if (isSubscribed) {
          await trackEvent('UnsubscribePortfolioNotification', {
            portfolio: portfolio.code,
          });
        } else {
          await trackEvent('SubscribePortfolioNotification', {
            portfolio: portfolio.code,
          });
        }
      } else {
        setError('Cannot subscribe to a deleted portfolio');
      }
    } catch (err) {
      setError(
        `Failed to send subscription request: ${(err as Error).message}`
      );
      await trackEvent('SubscribePortfolioNotificationFailed', {
        portfolio: portfolio.code,
      });
    }
  };

  useEffect(() => {
    const fetchPortfolioInfo = async () => {
      if (typeof slug !== 'string') return;

      try {
        const initialPortfolioInfo = await getPublicPortfolioInfo(slug);
        setPortfolio(initialPortfolioInfo);
        setPortfolioCode(initialPortfolioInfo.code);
        setIsDeleted(initialPortfolioInfo.is_deleted);
      } catch (err) {
        setError('Failed to fetch portfolio info');
        setLoading(false);
      }
    };

    if (slug) {
      fetchPortfolioInfo();
    }
  }, [slug]);

  useEffect(() => {
    if (!portfolioCode) return;

    const initPortfolioChartData = () => {
      const netValueResult = execSafely(
        sqlDB.current,
        'SELECT strftime("%Y-%m-%d",date), ROUND(net_value,2) FROM net_values ORDER BY date DESC LIMIT 1'
      );
      if (netValueResult.length > 0) {
        setLatestTradeDate(netValueResult[0][0]);
        setNetValue(netValueResult[0][1]);
      }

      const firstTradeDate = execSafely(
        sqlDB.current,
        'SELECT strftime("%Y-%m-%d",date) FROM capital_records ORDER BY date ASC LIMIT 1'
      );
      if (firstTradeDate.length > 0) {
        setFirstFundingDate(firstTradeDate[0][0]);
      }

      const hasXirrColumn =
        execSafely(
          sqlDB.current,
          "SELECT COUNT(*) FROM pragma_table_info('portfolio_status') WHERE name='xirr'"
        )[0]?.[0] > 0;

      const hasMaxDrawdownDurationColumn =
        execSafely(
          sqlDB.current,
          "SELECT COUNT(*) FROM pragma_table_info('portfolio_status') WHERE name='max_drawdown_duration_days'"
        )[0]?.[0] > 0;

      const hasVolatilityColumn =
        execSafely(
          sqlDB.current,
          "SELECT COUNT(*) FROM pragma_table_info('portfolio_status') WHERE name='volatility'"
        )[0]?.[0] > 0;

      const baseQuery = `
        SELECT 
          ROUND(current_drawdown,4), 
          ROUND(max_drawdown,4), 
          ROUND(cagr,4), 
          ROUND(sharpe_ratio,4), 
          total_trades, 
          profit_trades, 
          loss_trades, 
          ROUND(win_rate,4), 
          ROUND(profit_loss_ratio,4), 
          ROUND(sqn,4), 
          ROUND(vwr,4), 
          ROUND(calmar,4), 
          running_days, 
          annual_returns
          ${hasXirrColumn ? ', ROUND(xirr,4) as xirr' : ', 0 as xirr'}
          ${
            hasMaxDrawdownDurationColumn
              ? ', max_drawdown_duration_days'
              : ', 0 as max_drawdown_duration_days'
          }
          ${hasVolatilityColumn ? ', ROUND(volatility,4) as volatility' : ', 0 as volatility'}
        FROM portfolio_status 
        ORDER BY date DESC 
        LIMIT 1
      `;

      const portfolioPerformance = execSafely(sqlDB.current, baseQuery);
      if (portfolioPerformance.length > 0) {
        const [
          currentDrawdown,
          maxDrawdown,
          cagr,
          sharpeRatio,
          totalTrades,
          profitTrades,
          lossTrades,
          winRate,
          profitFactor,
          sqn,
          vwr,
          calmar,
          runningDays,
          annualReturnList,
          xirr,
          maxDrawdownDurationDays,
          volatility,
        ] = portfolioPerformance[0]; // It must align with the baseQuery fields position one by one

        setPerformance({
          currentDrawdown: Number(currentDrawdown) || 0,
          maxDrawdown: Number(maxDrawdown) || 0,
          cagr: Number(cagr) || 0,
          xirr: xirr ? Number(xirr) : null,
          sharpeRatio: Number(sharpeRatio) || 0,
          volatility: volatility ? Number(volatility) : undefined,
          totalTrades: Number(totalTrades) || 0,
          profitTrades: Number(profitTrades) || 0,
          lossTrades: Number(lossTrades) || 0,
          winRate: Number(winRate) || 0,
          profitFactor: Number(profitFactor) || 0,
          sqn: Number(sqn) || 0,
          vwr: Number(vwr) || 0,
          calmar: Number(calmar) || 0,
          runningDays: Number(runningDays) || 0,
          maxDrawdownDurationDays: Number(maxDrawdownDurationDays) || 0,
        });

        setAnnualReturns(annualReturnList);
      }

      // 获取等比买入持有基准数据
      const hasBenchmarkVolatilityColumn =
        execSafely(
          sqlDB.current,
          "SELECT COUNT(*) FROM pragma_table_info('equal_weight_benchmark') WHERE name='volatility'"
        )[0]?.[0] > 0;

      const benchmarkQuery = `
        SELECT 
          cagr, 
          max_drawdown, 
          sharpe_ratio, 
          overall_return, 
          initial_capital, 
          final_value
          ${hasBenchmarkVolatilityColumn ? ', ROUND(volatility,4) as volatility' : ', 0 as volatility'}
        FROM equal_weight_benchmark 
        ORDER BY date DESC 
        LIMIT 1
      `;

      const benchmarkResult = execSafely(sqlDB.current, benchmarkQuery);
      if (benchmarkResult.length > 0) {
        const [cagr, maxDrawdown, sharpeRatio, overallReturn, initialCapital, finalValue, benchmarkVolatility] = benchmarkResult[0];
        setBenchmark({
          cagr: Number(cagr) || 0,
          maxDrawdown: Number(maxDrawdown) || 0,
          sharpeRatio: Number(sharpeRatio) || 0,
          volatility: benchmarkVolatility ? Number(benchmarkVolatility) : undefined,
          overallReturn: Number(overallReturn) || 0,
          initialCapital: Number(initialCapital) || 0,
          finalValue: Number(finalValue) || 0,
        });
      }

      const transactionsCount = execSafely(
        sqlDB.current,
        'SELECT COUNT(*) FROM trade_records'
      );
      if (transactionsCount.length > 0) {
        setTransactionsPageNum(
          Math.ceil(parseInt(transactionsCount[0][0], 10) / pageLimit)
        );
      }

      const holdingsCount = execSafely(
        sqlDB.current,
        'SELECT COUNT(*) FROM position_records'
      );
      if (holdingsCount.length > 0) {
        setHoldingsPageNum(
          Math.ceil(parseInt(holdingsCount[0][0], 10) / pageLimit)
        );
      }

      const fundingsCount = execSafely(
        sqlDB.current,
        'SELECT COUNT(*) FROM capital_records'
      );
      if (fundingsCount.length > 0) {
        setFundingsPageNum(
          Math.ceil(parseInt(fundingsCount[0][0], 10) / pageLimit)
        );
      }
    };

    const initPortfolioChartOption = () => {
      const getDataSafely = (sql: string): number[] => {
        const results = execSafely(
          sqlDB.current,
          `SELECT ROUND(${sql}, 2) FROM benchmark_index ORDER BY date ASC`
        );
        return results.map((row) => Number(row[0]));
      };

      const getDates = (): string[] => {
        const results = execSafely(
          sqlDB.current,
          'SELECT strftime("%Y-%m-%d",date) FROM benchmark_index ORDER BY date ASC'
        );
        return results.map((row) => String(row[0]));
      };

      const chartData: ChartData = {
        dates: getDates(),
        netValues: getDataSafely('net_value'),
        ZZ500: getDataSafely('ZZ500'),
        HS300: getDataSafely('HS300'),
        CYB: getDataSafely('CYB'),
        HSI: getDataSafely('HSI'),
        SPX: getDataSafely('SPX'),
        IXIC: getDataSafely('IXIC'),
        GDAXI: getDataSafely('GDAXI'),
        N225: getDataSafely('N225'),
        KS11: getDataSafely('KS11'),
        AS51: getDataSafely('AS51'),
        SENSEX: getDataSafely('SENSEX'),
        annualReturn15: getDataSafely('`15pct_annual_return`'),
      };

      // 检查是否所有数据都为空
      const isAllDataEmpty = Object.values(chartData).every(
        (arr) => arr.length === 0
      );

      if (isAllDataEmpty) {
        console.warn('All chart data is empty. Chart will not be rendered.');
        setOptions({});
        return;
      }

      // 如果日期数据为空，但其他数据不为空，创建一个默认的日期数组
      if (chartData.dates.length === 0 && chartData.netValues.length > 0) {
        const dataLength = chartData.netValues.length;
        const today = new Date();
        chartData.dates = Array.from({ length: dataLength }, (_, i) => {
          const date = new Date(today);
          date.setDate(today.getDate() - (dataLength - i - 1));
          return date.toISOString().split('T')[0] || '';
        });
      }

      try {
        console.log('Chart data before generating options:', chartData);
        const optionsData = generateChartOptions(chartData);
        console.log('Generated chart options:', optionsData);
        setOptions(optionsData);
        setChartKey((prevKey) => prevKey + 1);
      } catch (e) {
        console.error('Error generating chart options:', e);
        setError('Failed to generate chart options. Please try again later.');
      }
    };

    (async () => {
      try {
        if (!portfolioCode) return;
        const dbURL = await getDbUrl(portfolioCode);
        loadDatabase(dbURL).then((db) => {
          sqlDB.current = db;
          initPortfolioChartData();
          initPortfolioChartOption();
          setLoading(false);
        });
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    })();
  }, [portfolioCode]);

  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        if (portfolio && portfolio.code) {
          const isActive = await getPortfolioSubscriptionStatus(portfolio.code);
          setIsSubscribed(isActive);
          setIsLoggedIn(true);
        }
      } catch (err) {
        setIsLoggedIn(false);
        setIsSubscribed(false);
        setSubscriptionError(true);
      }
    };
    checkLoginStatus();
  }, [portfolio]);

  return (
    <div className="antialiased text-gray-600">
      <Header />
      <Meta
        title={`${portfolio?.name ?? 'loading'} - ${AppConfig.title}`}
        description={`${portfolio?.description ?? 'loading'} - ${
          AppConfig.description
        }`}
      />
      
      <RiskDisclaimer 
        title="投资分析工具"
        description="仅供研究参考，不构成投资建议"
      />

      <div className="flex flex-col items-center justify-center mb-10">
        {loading && <InfinitySpin width="200" color="#EAB308" />}
        <pre className="error">{(error || '').toString()}</pre>

        {portfolio && (
          <>
            <div className="m-6 text-center">
              <h1 className="text-3xl font-bold text-main">{portfolio.name}</h1>
              {isDeleted && (
                <div className="text-red-500 font-bold mt-2 mb-2">
                  此组合已停止（删除）
                </div>
              )}
              <div className="text-l text-gray-500 pb-4 pt-4">
                {portfolio.description}
              </div>
              {portfolio.is_subscribable && (
                <button
                  onClick={handleSubscription}
                  disabled={
                    !isLoggedIn ||
                    subscriptionError ||
                    (isDeleted && !isSubscribed)
                  }
                  className={`px-4 py-2 rounded-md text-white font-bold ${
                    isSubscribed ? 'bg-red-500' : 'bg-green-500'
                  } ${
                    !isLoggedIn ||
                    subscriptionError ||
                    (isDeleted && !isSubscribed)
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }`}
                >
                  {isSubscribed ? '取消订阅' : '订阅交易提醒邮件'}
                </button>
              )}
            </div>

            <RiskLevelBanner performance={performance} />

            <PortfolioCockpit
              netValue={netValue}
              performance={performance}
              benchmark={benchmark}
              firstFundingDate={firstFundingDate}
              latestTradeDate={latestTradeDate}
            />

            <PortfolioTimeMachine
              yearRange={yearRange}
              handleTimeFly={handleTimeFly}
              isLoggedIn={isLoggedIn}
            />

            <AnnualReturns annualReturns={annualReturns} />
            
            <div className="sm:w-9/12 w-11/12 my-6">
              <ReturnAttribution 
                sqlDB={sqlDB.current} 
                execSafely={execSafely}
                marketType={getMarketType(portfolio.market)}
              />
            </div>

            <PortfolioTradeSignalsButton
              portfolioCode={portfolio.code}
              isLoggedIn={isLoggedIn}
            />

            <div className="sm:w-9/12 w-11/12">
              {!loading && (
                <PortfolioChart
                  chartKey={chartKey}
                  options={options}
                  onChartReady={() => {
                    setTransactionsData(1);
                    setHoldingsData(1);
                    setFundingsData(1);
                  }}
                />
              )}
            </div>

            <PortfolioTabs
              currentTab={currentTab}
              setCurrentTab={setCurrentTab}
              isLoggedIn={isLoggedIn}
              transactions={transactions}
              holdings={holdings}
              fundings={fundings}
              transactionsPageIndex={transactionsPageIndex}
              setTransactionsPageIndex={setTransactionsPageIndex}
              transactionsPageNum={transactionsPageNum}
              holdingsPageIndex={holdingsPageIndex}
              setHoldingsPageIndex={setHoldingsPageIndex}
              holdingsPageNum={holdingsPageNum}
              fundingsPageIndex={fundingsPageIndex}
              setFundingsPageIndex={setFundingsPageIndex}
              fundingsPageNum={fundingsPageNum}
              setTransactionsData={setTransactionsData}
              setHoldingsData={setHoldingsData}
              setFundingsData={setFundingsData}
            />
          </>
        )}

        {isTimeFlying && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="text-white text-center">
              <h2 className="text-3xl mb-4">时光跃迁中...</h2>
              <p className="mb-8">正在前往 {timeMachineYear} 年</p>
              <div className="w-64 h-64 relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse"></div>
                <div className="absolute inset-4 bg-gradient-to-r from-blue-500 to-green-500 rounded-full animate-ping"></div>
                <div className="absolute inset-8 bg-gradient-to-r from-yellow-500 to-red-500 rounded-full animate-spin"></div>
              </div>
            </div>
          </div>
        )}
        <FAQSection />
      </div>
      <Footer />
    </div>
  );
};

export default Portfolio;
