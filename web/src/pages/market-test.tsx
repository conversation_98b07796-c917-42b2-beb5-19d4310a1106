import React from 'react';
import { StrategyVisualizer } from '../components/StrategyVisualizer';

// 测试market indicators的策略
const testMarketStrategy = {
  "market_indicators": {
    "indicators": [
      {
        "code": "VIX"
      }
    ],
    "transformers": [
      {
        "name": "vix_raw",
        "type": "IdentityTransformer",
        "params": {
          "indicator": "VIX",
          "field": "Close"
        }
      },
      {
        "name": "vix_percentile",
        "type": "PercentileRankTransformer",
        "params": {
          "indicator": "VIX",
          "lookback": 252,
          "field": "Close"
        }
      }
    ]
  },
  "trade_strategy": {
    "indicators": [
      {
        "id": "constant_75",
        "type": "Constant",
        "params": {
          "value": 75
        }
      }
    ],
    "signals": [
      {
        "id": "market_volatility_low",
        "type": "LessThan",
        "inputs": [
          {
            "market": "VIX",
            "transformer": "vix_percentile"
          },
          {
            "ref": "constant_75"
          }
        ]
      }
    ],
    "outputs": {
      "buy_signal": "market_volatility_low",
      "indicators": [
        {
          "id": "constant_75",
          "output_name": "threshold"
        }
      ],
      "market_indicators": [
        {
          "market": "VIX",
          "transformer": "vix_raw",
          "output_name": "vix_raw"
        },
        {
          "market": "VIX",
          "transformer": "vix_percentile",
          "output_name": "vix_percentile"
        }
      ]
    }
  }
};

export default function MarketTestPage() {
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold text-center mb-8">
          Market Indicators 可视化测试
        </h1>
        <div className="bg-white rounded-lg shadow-lg p-6">
          <StrategyVisualizer 
            strategyDsl={testMarketStrategy}
            mode="readonly"
          />
        </div>
      </div>
    </div>
  );
}
