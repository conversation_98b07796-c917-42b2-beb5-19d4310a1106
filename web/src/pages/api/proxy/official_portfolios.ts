export const runtime = 'edge';

export default async function handler(req: Request) {
  const url = new URL(req.url);
  const groupBy = url.searchParams.get('groupBy');
  const targetUrl = `https://api.i365.tech/official_portfolios?groupBy=${groupBy}`;

  try {
    const response = await fetch(targetUrl);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error("Proxy error:", error);
    return new Response(JSON.stringify({
      error: "Failed to fetch official portfolios",
      details: error instanceof Error ? error.message : "Unknown error",
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
