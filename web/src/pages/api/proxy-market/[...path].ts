export const runtime = 'edge';

export default async function handler(req: Request) {
  const url = new URL(req.url);
  const pathSegments = url.pathname.split('/').filter(Boolean);

  // 移除 API 路由前缀
  const relevantSegments = pathSegments.slice(
    pathSegments.indexOf('proxy-market') + 1
  );

  // 确保 path 至少有两个段 (market 和 filename)
  if (relevantSegments.length < 2) {
    return new Response(JSON.stringify({ error: 'Invalid path' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const [market, ...fileSegments] = relevantSegments;
  const fileName = fileSegments.join('/');
  const dataUrl = `https://media.i365.tech/myinvestpilot/markets/${market}/${fileName}`;

  try {
    const response = await fetch(dataUrl);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const contentType =
      response.headers.get('Content-Type') || 'application/json';
    const data = await response.arrayBuffer();

    return new Response(data, {
      status: 200,
      headers: { 'Content-Type': contentType },
    });
  } catch (error) {
    return new Response(
      JSON.stringify({ error: 'Failed to fetch market data' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}
