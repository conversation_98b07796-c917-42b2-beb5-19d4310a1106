export const runtime = 'edge';

export default async function handler(req: Request) {
  const url = new URL(req.url);
  const pathSegments = url.pathname.split('/').filter(Boolean);

  // 移除 API 路由前缀（如果有的话）
  const relevantSegments = pathSegments.slice(
    pathSegments.indexOf('proxy-db') + 1
  );

  // 确保 path 至少有一个段
  if (relevantSegments.length < 1) {
    return new Response(JSON.stringify({ error: 'Invalid path' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // 构建数据库 URL
  const portfolioCode = relevantSegments[0];
  const dbFileName = `${portfolioCode}_portfolio.db`;
  const dbUrl = `https://media.i365.tech/myinvestpilot/portfolios/${portfolioCode}/${dbFileName}`;

  try {
    const response = await fetch(dbUrl);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const buffer = await response.arrayBuffer();

    return new Response(buffer, {
      status: 200,
      headers: { 'Content-Type': 'application/octet-stream' },
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Failed to fetch DB' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
