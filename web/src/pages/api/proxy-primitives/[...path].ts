export const runtime = 'edge';

export default async function handler(req: Request) {
  const url = new URL(req.url);
  const pathSegments = url.pathname.split('/').filter(Boolean);
  
  // Remove API route prefix to get the actual path
  const relevantSegments = pathSegments.slice(
    pathSegments.indexOf('proxy-primitives') + 1
  );
  const path = relevantSegments.join('/');
  const targetUrl = `https://media.i365.tech/myinvestpilot/${path}`;

  console.log('Proxy primitives request:', {
    path,
    targetUrl,
    method: req.method,
  });

  try {
    const response = await fetch(targetUrl);
    console.log('Target response status:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Get the raw response content
    const text = await response.text();
    console.log('Response text length:', text.length);
    console.log('Response text preview:', text.substring(0, 200));

    // Set correct response headers
    return new Response(text, {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Proxy primitives error:', error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch primitives manifest',
      details: error instanceof Error ? error.message : 'Unknown error',
    }), {
      status: 500,
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}
