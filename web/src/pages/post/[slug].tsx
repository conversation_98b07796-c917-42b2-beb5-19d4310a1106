import { GetServerSideProps } from 'next';
import ReactMarkdown from 'react-markdown';
import gfm from 'remark-gfm';

import { IPostResponse } from '../../../types/types';
import VideoPlayer from '../../components/VideoPlayer';
import { Meta } from '../../layout/Meta';
import { Section } from '../../layout/Section';
import { Footer } from '../../templates/Footer';
import { Header } from '../../templates/Header';
import { AppConfig } from '../../utils/AppConfig';
import { getPost } from '../../utils/getPosts';

export const runtime = 'experimental-edge';

const PostPage = ({ post }: { post: IPostResponse }) => {
  const renderContent = () => {
    // Render markdown content and video player if post type is video
    return (
      <div>
        <ReactMarkdown
          className="prose dark:prose-dark max-w-none"
          remarkPlugins={[gfm]}
        >
          {post.markdown}
        </ReactMarkdown>
        {post.type === 'video' && (
          <VideoPlayer
            src={`https://api.myinvestpilot.com/posts/${post.slug}/video`}
          />
        )}
      </div>
    );
  };

  return (
    <div className="antialiased text-gray-600">
      <Meta title={AppConfig.title} description={AppConfig.description} />
      <Header />
      {post.error && (
        <Section>
          <hr className="h-px my-8 bg-gray-200 border-0 dark:bg-gray-200 text-center"></hr>
          <div className="flex flex-col mb-10 text-center">
            <p>{post.error}</p>
          </div>
        </Section>
      )}

      <Section>
        <div id="nice" className="flex flex-col mb-10">
          {renderContent()}
        </div>
      </Section>
      <Footer />
    </div>
  );
};

export default PostPage;

export const getServerSideProps: GetServerSideProps = async ({
  params,
  req,
}) => {
  const cookies = req.headers.cookie || '';
  try {
    const post = await getPost(params?.slug as string, cookies);
    return { props: { post } };
  } catch (e) {
    return {
      props: {
        post: {
          slug: params?.slug,
          error: `您没有权限使用此系统，请添加微信 improve365_cn 获取支持！`,
          cover_image: '',
          meta: '',
          extra: '',
          post_date: 0,
          title: '',
          markdown: '',
          html: '',
          type: '',
        },
      },
    };
  }
};
