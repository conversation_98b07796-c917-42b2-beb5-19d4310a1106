import React, { useEffect, useState } from 'react';
import { jsonToFlow } from '../utils/strategyVisualizer';

const sampleStrategy = {
  indicators: [
    {
      id: "ema_20",
      type: "EMA",
      params: { period: 20 },
      inputs: {}
    },
    {
      id: "ema_50", 
      type: "EMA",
      params: { period: 50 },
      inputs: {}
    },
    {
      id: "rsi_14",
      type: "RSI",
      params: { period: 14 },
      inputs: {}
    }
  ],
  signals: [
    {
      id: "golden_cross",
      type: "CrossAbove",
      params: {},
      inputs: {
        fast: "ema_20",
        slow: "ema_50"
      }
    },
    {
      id: "rsi_oversold",
      type: "Below",
      params: {
        threshold: 30
      },
      inputs: {
        indicator: "rsi_14"
      }
    }
  ]
};

export default function ConversionTest() {
  const [result, setResult] = useState<any>(null);

  useEffect(() => {
    console.log('Testing jsonToFlow with:', sampleStrategy);
    try {
      const converted = jsonToFlow(sampleStrategy);
      console.log('Conversion result:', converted);
      setResult(converted);
    } catch (error) {
      console.error('Conversion error:', error);
      setResult({ error: error.message });
    }
  }, []);

  return (
    <div style={{ padding: '20px' }}>
      <h1>Conversion Test</h1>
      <h2>Input Strategy:</h2>
      <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
        {JSON.stringify(sampleStrategy, null, 2)}
      </pre>
      
      <h2>Conversion Result:</h2>
      <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
        {result ? JSON.stringify(result, null, 2) : 'Loading...'}
      </pre>
    </div>
  );
}
