import React from 'react';
import { StrategyVisualizer } from '../components/StrategyVisualizer';

// 示例策略数据
const sampleStrategy = {
  trade_strategy: {
    indicators: [
      {
        id: "ema_20",
        type: "EMA",
        params: {
          period: 20
        },
        inputs: {}
      },
      {
        id: "ema_50", 
        type: "EMA",
        params: {
          period: 50
        },
        inputs: {}
      },
      {
        id: "rsi_14",
        type: "RSI",
        params: {
          period: 14
        },
        inputs: {}
      }
    ],
    signals: [
      {
        id: "golden_cross",
        type: "CrossAbove",
        params: {},
        inputs: {
          fast: "ema_20",
          slow: "ema_50"
        }
      },
      {
        id: "rsi_oversold",
        type: "Below",
        params: {
          threshold: 30
        },
        inputs: {
          indicator: "rsi_14"
        }
      }
    ]
  }
};

export default function StrategyTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            策略可视化测试页面
          </h1>
          <p className="text-gray-600">
            测试 StrategyVisualizer 组件的功能，包括JSON编辑器和React Flow可视化
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg" style={{ height: '700px' }}>
          <StrategyVisualizer
            strategyDsl={sampleStrategy}
            mode="edit"
            onStrategyChange={(strategy) => {
              console.log('策略已更新:', strategy);
            }}
          />
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">测试说明</h3>
          <ul className="text-blue-800 space-y-1">
            <li>• 左侧是Monaco编辑器，可以编辑策略JSON</li>
            <li>• 右侧是React Flow画布，显示策略的可视化图形</li>
            <li>• 编辑JSON时，右侧图形会实时更新</li>
            <li>• 示例策略包含3个指标（EMA20、EMA50、RSI14）和2个信号</li>
            <li>• 原语清单会自动从代理加载，避免CORS问题</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
