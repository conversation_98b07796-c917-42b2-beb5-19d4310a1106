import React, { useEffect } from 'react';
import { jsonToFlow, validateTradeStrategy } from '../utils/strategyVisualizer';

// 简化的测试数据
const testStrategy = {
  "indicators": [
    {
      "id": "ma_indicator",
      "type": "SMA",
      "params": {
        "period": 250,
        "column": "Close"
      }
    },
    {
      "id": "constant_75",
      "type": "Constant",
      "params": {
        "value": 75
      }
    }
  ],
  "signals": [
    {
      "id": "price_gt_ma",
      "type": "GreaterThan",
      "inputs": [
        {
          "column": "Close"
        },
        {
          "ref": "ma_indicator"
        }
      ]
    },
    {
      "id": "buy_signal",
      "type": "And",
      "inputs": [
        {
          "ref": "price_gt_ma"
        },
        {
          "ref": "constant_75"
        }
      ]
    }
  ]
};

export default function DebugConversionPage() {
  const [conversionResult, setConversionResult] = React.useState<any>(null);

  useEffect(() => {
    console.log('🧪 开始调试转换过程');
    console.log('📊 测试策略数据:', testStrategy);

    // 验证策略
    const isValid = validateTradeStrategy(testStrategy);
    console.log('✅ 策略验证结果:', isValid);

    if (isValid) {
      // 执行转换
      const result = jsonToFlow(testStrategy);
      console.log('🎯 转换结果:', result);
      console.log('📦 节点数量:', result.nodes.length);
      console.log('🔗 边数量:', result.edges.length);

      // 详细输出每个节点
      result.nodes.forEach((node, index) => {
        console.log(`📍 节点 ${index + 1}:`, {
          id: node.id,
          type: node.data.type,
          originalType: node.data.originalType,
          className: node.className,
          style: node.style,
          position: node.position
        });
      });

      // 详细输出每条边
      result.edges.forEach((edge, index) => {
        console.log(`🔗 边 ${index + 1}:`, {
          id: edge.id,
          source: edge.source,
          target: edge.target,
          label: edge.label
        });
      });

      // 保存结果用于显示
      setConversionResult(result);
    } else {
      console.error('❌ 策略验证失败');
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">
          转换调试页面
        </h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">测试策略数据</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(testStrategy, null, 2)}
          </pre>
        </div>
        
        {/* 转换结果预览 */}
        {conversionResult && (
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">转换结果预览</h2>

            {/* 节点预览 */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">节点 ({conversionResult.nodes.length})</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {conversionResult.nodes.map((node: any, index: number) => (
                  <div
                    key={node.id}
                    className="border rounded-lg p-4"
                    style={{ borderColor: node.style?.border?.split(' ')[2] || '#ccc' }}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <div
                        className="w-6 h-6 rounded flex-shrink-0"
                        style={{
                          backgroundColor: node.style?.background || '#gray',
                          border: node.style?.border || '2px solid #ccc'
                        }}
                      />
                      <div>
                        <div className="font-medium">{node.id}</div>
                        <div className="text-sm text-gray-500">{node.data.originalType}</div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      类型: {node.className || 'default'}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 边预览 */}
            <div>
              <h3 className="text-lg font-medium mb-3">连接边 ({conversionResult.edges.length})</h3>
              {conversionResult.edges.length === 0 ? (
                <div className="text-red-500 font-medium">⚠️ 没有创建任何连接边！</div>
              ) : (
                <div className="space-y-2">
                  {conversionResult.edges.map((edge: any, index: number) => (
                    <div key={edge.id} className="flex items-center space-x-2 text-sm">
                      <span className="font-medium">{edge.source}</span>
                      <span className="text-gray-400">→</span>
                      <span className="font-medium">{edge.target}</span>
                      {edge.label && (
                        <span className="text-gray-500">({edge.label})</span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        <div className="bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            调试说明
          </h3>
          <ul className="text-blue-800 space-y-1">
            <li>• 打开浏览器控制台查看详细的转换过程日志</li>
            <li>• 这个页面专门用于调试 jsonToFlow 转换函数</li>
            <li>• 测试数据包含2个指标和2个信号，应该生成4个节点和2条边</li>
            <li>• 检查验证函数是否正确处理数组格式的 inputs</li>
            <li>• 🟢 绿色节点 = 输入节点（不依赖其他节点）</li>
            <li>• 🔵 蓝色节点 = 输出节点（最终结果）</li>
            <li>• ⚫ 灰色节点 = 中间节点（处理步骤）</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
