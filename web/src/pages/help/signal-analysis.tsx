import React, { useState } from 'react';

import Link from 'next/link';

import { Footer } from '../../templates/Footer';
import { Header } from '../../templates/Header';

export default function SignalAnalysisHelp() {
  const [copiedQuery, setCopiedQuery] = useState<string | null>(null);

  const copyToClipboard = async (text: string, queryId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedQuery(queryId);
      setTimeout(() => setCopiedQuery(null), 2000);
    } catch (err) {
      // Failed to copy text
    }
  };
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🔍 信号分析查询手册
          </h1>

          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-gray-600 mb-6">
              本手册包含了用于投资组合信号分析的专业 SQL
              查询。这些查询可以帮助您诊断策略信号的质量和合理性。
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-blue-800 mb-1">
                    💡 使用提示
                  </h3>
                  <p className="text-blue-600 text-sm">
                    点击每个查询右上角的 &quot;📋 复制 SQL&quot;
                    按钮，即可复制完整的查询语句到剪贴板
                  </p>
                </div>
                <button
                  onClick={() =>
                    copyToClipboard(
                      `-- 1. 信号数据健康检查
WITH health_metrics AS (
    SELECT
        COUNT(*) as total_records,
        COUNT(DISTINCT symbol) as symbol_count,
        COUNT(DISTINCT date) as date_count,
        MIN(date) as start_date,
        MAX(date) as end_date,
        COUNT(CASE WHEN signal NOT IN ('B','S','H','E') OR signal IS NULL THEN 1 END) as invalid_signals
    FROM trade_signals
)
SELECT
    '数据规模' as check_item,
    total_records || ' records, ' || symbol_count || ' symbols' as result,
    CASE WHEN total_records > 0 THEN '✅ 正常' ELSE '❌ 无数据' END as status
FROM health_metrics
UNION ALL
SELECT
    '时间范围',
    start_date || ' to ' || end_date || ' (' || date_count || ' days)',
    CASE WHEN date_count > 0 THEN '✅ 正常' ELSE '❌ 无数据' END
FROM health_metrics
UNION ALL
SELECT
    '信号有效性',
    CASE WHEN invalid_signals = 0 THEN 'All signals are valid (B/S/H/E)'
         ELSE invalid_signals || ' invalid signals found' END,
    CASE WHEN invalid_signals = 0 THEN '✅ 正常' ELSE '❌ 发现无效信号' END
FROM health_metrics;

-- 2. 信号切换逻辑验证
WITH signal_transitions AS (
    SELECT
        date,
        symbol,
        signal as current_signal,
        LAG(signal) OVER (PARTITION BY symbol ORDER BY date) as prev_signal
    FROM trade_signals
),
transition_analysis AS (
    SELECT
        prev_signal || ' → ' || current_signal as transition,
        COUNT(*) as count,
        CASE
            -- 异常切换
            WHEN prev_signal = 'H' AND current_signal = 'E' THEN '❌ 异常: 持有直接空仓(应经过卖出)'
            WHEN prev_signal = 'B' AND current_signal = 'S' THEN '❌ 异常: 买入直接卖出'
            WHEN prev_signal = 'E' AND current_signal = 'S' THEN '❌ 异常: 空仓时卖出'
            WHEN prev_signal = 'S' AND current_signal = 'B' THEN '❌ 异常: 卖出直接买入'
            -- 正常切换
            WHEN prev_signal = 'E' AND current_signal = 'B' THEN '✅ 正常: 空仓买入'
            WHEN prev_signal = 'B' AND current_signal = 'H' THEN '✅ 正常: 买入后持有'
            WHEN prev_signal = 'H' AND current_signal = 'S' THEN '✅ 正常: 持有后卖出'
            WHEN prev_signal = 'S' AND current_signal = 'E' THEN '✅ 正常: 卖出后空仓'
            WHEN prev_signal = 'H' AND current_signal = 'B' THEN '✅ 正常: 定投加仓'
            WHEN prev_signal = current_signal THEN '⚪ 无变化: 状态保持'
            ELSE '❓ 其他: ' || prev_signal || ' → ' || current_signal
        END as logic_check
    FROM signal_transitions
    WHERE prev_signal IS NOT NULL
    GROUP BY prev_signal, current_signal
)
SELECT
    transition,
    count,
    ROUND(count * 100.0 / (SELECT SUM(count) FROM transition_analysis), 2) as percentage,
    logic_check
FROM transition_analysis
WHERE count > 0
ORDER BY
    CASE WHEN logic_check LIKE '❌%' THEN 1
         WHEN logic_check LIKE '❓%' THEN 2
         WHEN logic_check LIKE '✅%' THEN 3
         ELSE 4 END,
    count DESC
LIMIT 50;

-- 3. 信号分布和策略特征分析
WITH signal_stats AS (
    SELECT
        signal,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM trade_signals), 2) as percentage
    FROM trade_signals
    GROUP BY signal
),
activity_summary AS (
    SELECT
        SUM(CASE WHEN signal IN ('B', 'S') THEN count ELSE 0 END) as active_count,
        SUM(CASE WHEN signal IN ('H', 'E') THEN count ELSE 0 END) as passive_count,
        SUM(count) as total_count
    FROM signal_stats
)
SELECT
    signal || ' (' ||
    CASE
        WHEN signal = 'B' THEN 'Buy'
        WHEN signal = 'S' THEN 'Sell'
        WHEN signal = 'H' THEN 'Hold'
        WHEN signal = 'E' THEN 'Empty'
        ELSE 'Unknown'
    END || ')' as signal_type,
    count,
    percentage || '%' as percentage_str,
    CASE
        WHEN signal IN ('B', 'S') THEN '🔥 Active Trading'
        WHEN signal IN ('H', 'E') THEN '💤 Passive Holding'
        ELSE '❓ Unknown'
    END as activity_style
FROM signal_stats
UNION ALL
SELECT
    '--- 策略风格评估 ---',
    NULL,
    ROUND(active_count * 100.0 / total_count, 2) || '% Active, ' ||
    ROUND(passive_count * 100.0 / total_count, 2) || '% Passive',
    CASE
        WHEN active_count * 100.0 / total_count > 10 THEN '🔥 激进型策略'
        WHEN active_count * 100.0 / total_count > 2 THEN '⚖️ 平衡型策略'
        ELSE '💤 保守型策略'
    END
FROM activity_summary
ORDER BY count DESC NULLS LAST;

-- 4. 最近信号状态查询
SELECT
    date,
    symbol,
    signal,
    CASE
        WHEN signal = 'B' THEN '🟢 Buy'
        WHEN signal = 'S' THEN '🔴 Sell'
        WHEN signal = 'H' THEN '🟡 Hold'
        WHEN signal = 'E' THEN '⚪ Empty'
        ELSE '❓ Unknown'
    END as signal_status
FROM trade_signals
WHERE date >= (SELECT date(MAX(date), '-7 days') FROM trade_signals)
ORDER BY date DESC, symbol
LIMIT 30;`,
                      'all-queries'
                    )
                  }
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors flex items-center gap-2"
                >
                  {copiedQuery === 'all-queries' ? (
                    <>
                      <span>✓</span>
                      已复制全部
                    </>
                  ) : (
                    <>
                      <span>📋</span>
                      复制全部查询
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* 数据说明 */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                📋 数据说明
              </h2>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <ul className="list-disc list-inside space-y-2">
                  <li>
                    <strong>主数据表</strong>:{' '}
                    <code className="bg-gray-100 px-2 py-1 rounded">
                      trade_signals
                    </code>{' '}
                    - 包含所有交易信号数据
                  </li>
                  <li>
                    <strong>信号类型</strong>: B(买入) | S(卖出) | H(持有) |
                    E(空仓)
                  </li>
                  <li>
                    <strong>数据字段</strong>: date(日期) | symbol(股票代码) |
                    signal(信号类型)
                  </li>
                </ul>
              </div>
            </section>

            {/* 查询1: 信号健康检查 */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                🏥 信号健康检查
              </h2>
              <p className="text-gray-600 mb-4">
                快速诊断数据质量，检查数据完整性、记录数量、时间范围和信号有效性。
              </p>
              <div className="relative bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <button
                  onClick={() =>
                    copyToClipboard(
                      `-- 信号数据健康检查
WITH health_metrics AS (
    SELECT
        COUNT(*) as total_records,
        COUNT(DISTINCT symbol) as symbol_count,
        COUNT(DISTINCT date) as date_count,
        MIN(date) as start_date,
        MAX(date) as end_date,
        COUNT(CASE WHEN signal NOT IN ('B','S','H','E') OR signal IS NULL THEN 1 END) as invalid_signals
    FROM trade_signals
)
SELECT
    '数据规模' as check_item,
    total_records || ' records, ' || symbol_count || ' symbols' as result,
    CASE WHEN total_records > 0 THEN '✅ 正常' ELSE '❌ 无数据' END as status
FROM health_metrics
UNION ALL
SELECT
    '时间范围',
    start_date || ' to ' || end_date || ' (' || date_count || ' days)',
    CASE WHEN date_count > 0 THEN '✅ 正常' ELSE '❌ 无数据' END
FROM health_metrics
UNION ALL
SELECT
    '信号有效性',
    CASE WHEN invalid_signals = 0 THEN 'All signals are valid (B/S/H/E)'
         ELSE invalid_signals || ' invalid signals found' END,
    CASE WHEN invalid_signals = 0 THEN '✅ 正常' ELSE '❌ 发现无效信号' END
FROM health_metrics;`,
                      'health-check'
                    )
                  }
                  className="absolute top-3 right-3 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md transition-colors flex items-center gap-1"
                >
                  {copiedQuery === 'health-check' ? (
                    <>
                      <span>✓</span>
                      已复制
                    </>
                  ) : (
                    <>
                      <span>📋</span>
                      复制 SQL
                    </>
                  )}
                </button>
                <pre className="text-green-400 text-sm">
                  {`-- 信号数据健康检查
WITH health_metrics AS (
    SELECT
        COUNT(*) as total_records,
        COUNT(DISTINCT symbol) as symbol_count,
        COUNT(DISTINCT date) as date_count,
        MIN(date) as start_date,
        MAX(date) as end_date,
        COUNT(CASE WHEN signal NOT IN ('B','S','H','E') OR signal IS NULL THEN 1 END) as invalid_signals
    FROM trade_signals
)
SELECT
    '数据规模' as check_item,
    total_records || ' records, ' || symbol_count || ' symbols' as result,
    CASE WHEN total_records > 0 THEN '✅ 正常' ELSE '❌ 无数据' END as status
FROM health_metrics
UNION ALL
SELECT
    '时间范围',
    start_date || ' to ' || end_date || ' (' || date_count || ' days)',
    CASE WHEN date_count > 0 THEN '✅ 正常' ELSE '❌ 无数据' END
FROM health_metrics
UNION ALL
SELECT
    '信号有效性',
    CASE WHEN invalid_signals = 0 THEN 'All signals are valid (B/S/H/E)'
         ELSE invalid_signals || ' invalid signals found' END,
    CASE WHEN invalid_signals = 0 THEN '✅ 正常' ELSE '❌ 发现无效信号' END
FROM health_metrics;`}
                </pre>
              </div>
            </section>

            {/* 查询2: 信号切换逻辑检查 */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                🔄 信号切换逻辑检查
              </h2>
              <p className="text-gray-600 mb-4">
                检测不符合交易逻辑的信号切换，识别异常的买卖时机。
              </p>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                <h4 className="font-semibold text-yellow-800 mb-2">
                  正常切换逻辑:
                </h4>
                <ul className="list-disc list-inside text-yellow-700 space-y-1">
                  <li>标准流程: E → B → H → S → E</li>
                  <li>定投场景: H → B (继续加仓)</li>
                  <li>必须卖出: H → E 必须经过 S</li>
                </ul>
              </div>
              <div className="relative bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <button
                  onClick={() =>
                    copyToClipboard(
                      `-- 信号切换逻辑验证
WITH signal_transitions AS (
    SELECT
        date,
        symbol,
        signal as current_signal,
        LAG(signal) OVER (PARTITION BY symbol ORDER BY date) as prev_signal
    FROM trade_signals
),
transition_analysis AS (
    SELECT
        prev_signal || ' → ' || current_signal as transition,
        COUNT(*) as count,
        CASE
            -- 异常切换
            WHEN prev_signal = 'H' AND current_signal = 'E' THEN '❌ 异常: 持有直接空仓(应经过卖出)'
            WHEN prev_signal = 'B' AND current_signal = 'S' THEN '❌ 异常: 买入直接卖出'
            WHEN prev_signal = 'E' AND current_signal = 'S' THEN '❌ 异常: 空仓时卖出'
            WHEN prev_signal = 'S' AND current_signal = 'B' THEN '❌ 异常: 卖出直接买入'
            -- 正常切换
            WHEN prev_signal = 'E' AND current_signal = 'B' THEN '✅ 正常: 空仓买入'
            WHEN prev_signal = 'B' AND current_signal = 'H' THEN '✅ 正常: 买入后持有'
            WHEN prev_signal = 'H' AND current_signal = 'S' THEN '✅ 正常: 持有后卖出'
            WHEN prev_signal = 'S' AND current_signal = 'E' THEN '✅ 正常: 卖出后空仓'
            WHEN prev_signal = 'H' AND current_signal = 'B' THEN '✅ 正常: 定投加仓'
            WHEN prev_signal = current_signal THEN '⚪ 无变化: 状态保持'
            ELSE '❓ 其他: ' || prev_signal || ' → ' || current_signal
        END as logic_check
    FROM signal_transitions
    WHERE prev_signal IS NOT NULL
    GROUP BY prev_signal, current_signal
)
SELECT
    transition,
    count,
    ROUND(count * 100.0 / (SELECT SUM(count) FROM transition_analysis), 2) as percentage,
    logic_check
FROM transition_analysis
WHERE count > 0
ORDER BY
    CASE WHEN logic_check LIKE '❌%' THEN 1
         WHEN logic_check LIKE '❓%' THEN 2
         WHEN logic_check LIKE '✅%' THEN 3
         ELSE 4 END,
    count DESC
LIMIT 50;`,
                      'transition-logic'
                    )
                  }
                  className="absolute top-3 right-3 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md transition-colors flex items-center gap-1"
                >
                  {copiedQuery === 'transition-logic' ? (
                    <>
                      <span>✓</span>
                      已复制
                    </>
                  ) : (
                    <>
                      <span>📋</span>
                      复制 SQL
                    </>
                  )}
                </button>
                <pre className="text-green-400 text-sm">
                  {`-- 信号切换逻辑验证
WITH signal_transitions AS (
    SELECT
        date,
        symbol,
        signal as current_signal,
        LAG(signal) OVER (PARTITION BY symbol ORDER BY date) as prev_signal
    FROM trade_signals
),
transition_analysis AS (
    SELECT
        prev_signal || ' → ' || current_signal as transition,
        COUNT(*) as count,
        CASE
            -- 异常切换
            WHEN prev_signal = 'H' AND current_signal = 'E' THEN '❌ 异常: 持有直接空仓(应经过卖出)'
            WHEN prev_signal = 'B' AND current_signal = 'S' THEN '❌ 异常: 买入直接卖出'
            WHEN prev_signal = 'E' AND current_signal = 'S' THEN '❌ 异常: 空仓时卖出'
            WHEN prev_signal = 'S' AND current_signal = 'B' THEN '❌ 异常: 卖出直接买入'
            -- 正常切换
            WHEN prev_signal = 'E' AND current_signal = 'B' THEN '✅ 正常: 空仓买入'
            WHEN prev_signal = 'B' AND current_signal = 'H' THEN '✅ 正常: 买入后持有'
            WHEN prev_signal = 'H' AND current_signal = 'S' THEN '✅ 正常: 持有后卖出'
            WHEN prev_signal = 'S' AND current_signal = 'E' THEN '✅ 正常: 卖出后空仓'
            WHEN prev_signal = 'H' AND current_signal = 'B' THEN '✅ 正常: 定投加仓'
            WHEN prev_signal = current_signal THEN '⚪ 无变化: 状态保持'
            ELSE '❓ 其他: ' || prev_signal || ' → ' || current_signal
        END as logic_check
    FROM signal_transitions
    WHERE prev_signal IS NOT NULL
    GROUP BY prev_signal, current_signal
)
SELECT
    transition,
    count,
    ROUND(count * 100.0 / (SELECT SUM(count) FROM transition_analysis), 2) as percentage,
    logic_check
FROM transition_analysis
WHERE count > 0
ORDER BY
    CASE WHEN logic_check LIKE '❌%' THEN 1
         WHEN logic_check LIKE '❓%' THEN 2
         WHEN logic_check LIKE '✅%' THEN 3
         ELSE 4 END,
    count DESC
LIMIT 50;`}
                </pre>
              </div>
            </section>

            {/* 查询3: 信号分布分析 */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                📊 信号分布分析
              </h2>
              <p className="text-gray-600 mb-4">
                分析策略的交易特征和风格，了解主动交易与被动持仓的比例。
              </p>
              <div className="relative bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <button
                  onClick={() =>
                    copyToClipboard(
                      `-- 信号分布和策略特征分析
WITH signal_stats AS (
    SELECT
        signal,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM trade_signals), 2) as percentage
    FROM trade_signals
    GROUP BY signal
),
activity_summary AS (
    SELECT
        SUM(CASE WHEN signal IN ('B', 'S') THEN count ELSE 0 END) as active_count,
        SUM(CASE WHEN signal IN ('H', 'E') THEN count ELSE 0 END) as passive_count,
        SUM(count) as total_count
    FROM signal_stats
)
SELECT
    signal || ' (' ||
    CASE
        WHEN signal = 'B' THEN 'Buy'
        WHEN signal = 'S' THEN 'Sell'
        WHEN signal = 'H' THEN 'Hold'
        WHEN signal = 'E' THEN 'Empty'
        ELSE 'Unknown'
    END || ')' as signal_type,
    count,
    percentage || '%' as percentage_str,
    CASE
        WHEN signal IN ('B', 'S') THEN '🔥 Active Trading'
        WHEN signal IN ('H', 'E') THEN '💤 Passive Holding'
        ELSE '❓ Unknown'
    END as activity_style
FROM signal_stats
UNION ALL
SELECT
    '--- 策略风格评估 ---',
    NULL,
    ROUND(active_count * 100.0 / total_count, 2) || '% Active, ' ||
    ROUND(passive_count * 100.0 / total_count, 2) || '% Passive',
    CASE
        WHEN active_count * 100.0 / total_count > 10 THEN '🔥 激进型策略'
        WHEN active_count * 100.0 / total_count > 2 THEN '⚖️ 平衡型策略'
        ELSE '💤 保守型策略'
    END
FROM activity_summary
ORDER BY count DESC NULLS LAST;`,
                      'signal-distribution'
                    )
                  }
                  className="absolute top-3 right-3 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md transition-colors flex items-center gap-1"
                >
                  {copiedQuery === 'signal-distribution' ? (
                    <>
                      <span>✓</span>
                      已复制
                    </>
                  ) : (
                    <>
                      <span>📋</span>
                      复制 SQL
                    </>
                  )}
                </button>
                <pre className="text-green-400 text-sm">
                  {`-- 信号分布和策略特征分析
WITH signal_stats AS (
    SELECT
        signal,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM trade_signals), 2) as percentage
    FROM trade_signals
    GROUP BY signal
),
activity_summary AS (
    SELECT
        SUM(CASE WHEN signal IN ('B', 'S') THEN count ELSE 0 END) as active_count,
        SUM(CASE WHEN signal IN ('H', 'E') THEN count ELSE 0 END) as passive_count,
        SUM(count) as total_count
    FROM signal_stats
)
SELECT
    signal || ' (' ||
    CASE
        WHEN signal = 'B' THEN 'Buy'
        WHEN signal = 'S' THEN 'Sell'
        WHEN signal = 'H' THEN 'Hold'
        WHEN signal = 'E' THEN 'Empty'
        ELSE 'Unknown'
    END || ')' as signal_type,
    count,
    percentage || '%' as percentage_str,
    CASE
        WHEN signal IN ('B', 'S') THEN '🔥 Active Trading'
        WHEN signal IN ('H', 'E') THEN '💤 Passive Holding'
        ELSE '❓ Unknown'
    END as activity_style
FROM signal_stats
UNION ALL
SELECT
    '--- 策略风格评估 ---',
    NULL,
    ROUND(active_count * 100.0 / total_count, 2) || '% Active, ' ||
    ROUND(passive_count * 100.0 / total_count, 2) || '% Passive',
    CASE
        WHEN active_count * 100.0 / total_count > 10 THEN '🔥 激进型策略'
        WHEN active_count * 100.0 / total_count > 2 THEN '⚖️ 平衡型策略'
        ELSE '💤 保守型策略'
    END
FROM activity_summary
ORDER BY count DESC NULLS LAST;`}
                </pre>
              </div>
            </section>

            {/* 查询4: 最近信号状态 */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                🕐 最近信号状态
              </h2>
              <p className="text-gray-600 mb-4">
                查看最新的信号状态，了解当前策略的持仓情况。
              </p>
              <div className="relative bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <button
                  onClick={() =>
                    copyToClipboard(
                      `-- 最近信号状态查询
SELECT
    date,
    symbol,
    signal,
    CASE
        WHEN signal = 'B' THEN '🟢 Buy'
        WHEN signal = 'S' THEN '🔴 Sell'
        WHEN signal = 'H' THEN '🟡 Hold'
        WHEN signal = 'E' THEN '⚪ Empty'
        ELSE '❓ Unknown'
    END as signal_status
FROM trade_signals
WHERE date >= (SELECT date(MAX(date), '-7 days') FROM trade_signals)
ORDER BY date DESC, symbol
LIMIT 30;`,
                      'recent-signals'
                    )
                  }
                  className="absolute top-3 right-3 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md transition-colors flex items-center gap-1"
                >
                  {copiedQuery === 'recent-signals' ? (
                    <>
                      <span>✓</span>
                      已复制
                    </>
                  ) : (
                    <>
                      <span>📋</span>
                      复制 SQL
                    </>
                  )}
                </button>
                <pre className="text-green-400 text-sm">
                  {`-- 最近信号状态查询
SELECT
    date,
    symbol,
    signal,
    CASE
        WHEN signal = 'B' THEN '🟢 Buy'
        WHEN signal = 'S' THEN '🔴 Sell'
        WHEN signal = 'H' THEN '🟡 Hold'
        WHEN signal = 'E' THEN '⚪ Empty'
        ELSE '❓ Unknown'
    END as signal_status
FROM trade_signals
WHERE date >= (SELECT date(MAX(date), '-7 days') FROM trade_signals)
ORDER BY date DESC, symbol
LIMIT 30;`}
                </pre>
              </div>
            </section>

            {/* 使用技巧 */}
            <section className="mb-10">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                💡 使用技巧
              </h2>
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <ul className="list-disc list-inside space-y-2 text-green-800">
                  <li>复制查询到 Datasette 的 SQL 编辑器中执行</li>
                  <li>修改 WHERE 条件来筛选特定时间段或股票</li>
                  <li>结合 GROUP BY 和聚合函数进行自定义统计</li>
                  <li>使用 ORDER BY 对结果进行排序</li>
                  <li>添加 LIMIT 控制返回的记录数量</li>
                </ul>
              </div>
            </section>

            {/* 返回链接 */}
            <div className="text-center mt-8">
              <Link
                href="/me"
                className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                ← 返回我的策略组合
              </Link>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
