import React from 'react';

import { useRouter } from 'next/router';

import { PortfolioForm } from '../components/PortfolioForm';
import { Meta } from '../layout/Meta';
import { Section } from '../layout/Section';
import { Footer } from '../templates/Footer';
import { Header } from '../templates/Header';
import { AppConfig } from '../utils/AppConfig';

export const Portfolio: React.FC = () => {
  const router = useRouter();
  const { mode, code } = router.query;

  const getPageTitle = () => {
    switch (mode) {
      case 'update':
        return '更新代码策略投资组合';
      case 'copy':
        return '定制代码策略投资组合';
      default:
        return '定制代码策略投资组合';
    }
  };

  return (
    <div className="antialiased text-gray-600">
      <Meta
        title={`${getPageTitle()} - ${AppConfig.title}`}
        description={AppConfig.description}
      />
      <Header />
      <Section title={`${getPageTitle()}`}>
        <PortfolioForm
          mode={(mode as 'update' | 'copy') || 'copy'}
          initialCode={code as string}
        />
      </Section>

      <Footer />
    </div>
  );
};

export default Portfolio;
