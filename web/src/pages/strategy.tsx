import { GetServerSideProps } from 'next';

import { StrategyProps } from '../../types/types';
import { Strategy } from '../templates/Strategy';
import { getOfficialPortfolios } from '../utils/api';

export const runtime = 'experimental-edge';

const StrategyPage: React.FC<StrategyProps> = ({ strategies }) => {
  return <Strategy strategies={strategies} />;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  try {
    const strategies = await getOfficialPortfolios(
      'strategy',
      context.req.headers.cookie
    );
    return {
      props: {
        strategies,
      },
    };
  } catch (error) {
    console.error('Failed to fetch strategies:', error);
    return {
      props: {
        strategies: [],
      },
    };
  }
};

export default StrategyPage;
