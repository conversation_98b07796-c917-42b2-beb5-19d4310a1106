import { ReactNode } from 'react';

import { FooterCopyright } from './FooterCopyright';

type ICenteredFooterProps = {
  logo: ReactNode;
  mainLinks: ReactNode;
  socialLinks: ReactNode;
  children?: ReactNode;
};

const CenteredFooter = (props: ICenteredFooterProps) => (
  <div className="text-center">
    {props.logo}

    <nav className="flex flex-col items-center space-y-2 mt-4">
      <ul className="flex flex-row justify-center items-center space-x-4 text-sm font-normal">
        {props.mainLinks}
      </ul>
      <ul className="flex flex-row justify-center items-center space-x-4 text-sm font-normal">
        {props.socialLinks}
      </ul>
    </nav>

    {props.children}

    <div className="mt-8 text-sm">
      <FooterCopyright />
    </div>
  </div>
);

export { CenteredFooter };
