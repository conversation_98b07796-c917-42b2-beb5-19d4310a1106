import { Meta } from '../layout/Meta';
import { Section } from '../layout/Section';
import { AppConfig } from '../utils/AppConfig';
import { Footer } from './Footer';
import { Header } from './Header';
import { PostCard } from './PostCard';

const Posts = () => {
  return (
    <div className="antialiased text-gray-600">
      <Meta title={AppConfig.title} description="i365会员专栏" />
      <Header />
      <Section>
        <div className="flex flex-col items-center justify-center mb-10 text-center">
          <h1 className="text-3xl font-bold">策引会员专栏</h1>
        </div>
      </Section>
      <Section>
        <div className="relative flex py-0 items-center">
          <div className="flex-grow border-t border-gray-200"></div>
          <h2 className="flex-shrink mx-4 text-gray-600 text-2xl">
            策引产品相关
          </h2>
          <div className="flex-grow border-t border-gray-200"></div>
        </div>
        <div className="flex flex-col items-center justify-center mb-10 text-center">
          <p>分享策引产品相关</p>
        </div>
      </Section>
      <Section>
        <div className="flex flex-col mb-10">
          <PostCard
            post={{
              id: '6f143d64-8d16-4e5d-a66b-abc41cdd6fc5',
              title: '策引原语策略功能介绍',
              description:
                '本次分享将以视频会议的方式分享以下主题：策引的功能介绍、定位及未来规划，还有核心功能原语策略的介绍。',
              date: 1750474777000,
            }}
          ></PostCard>
          <PostCard
            post={{
              id: 'a8d140c8-2860-4243-a6c9-0bb63ea2b844',
              title: '策引新增功能介绍',
              description:
                '本次分享将以视频会议的方式分享以下主题：如何分析一个策略的风险特征、策略交易信号深度分析、高阶调试技巧、组合页面新增指标介绍、邮件模版市场分析及其他相关问题。',
              date: 1751071539000,
            }}
          ></PostCard>
        </div>
      </Section>
      <Section>
        <div className="relative flex py-0 items-center">
          <div className="flex-grow border-t border-gray-200"></div>
          <h2 className="flex-shrink mx-4 text-gray-600 text-2xl">
            上班族的投资之路
          </h2>
          <div className="flex-grow border-t border-gray-200"></div>
        </div>
        <div className="flex flex-col items-center justify-center mb-10 text-center">
          <p>分享上班族如何通过简单的交易系统实现财富增值。</p>
        </div>
      </Section>
      <Section>
        <div className="flex flex-col mb-10">
          <PostCard
            post={{
              id: 'b8d36460-6132-4063-abd0-039030d52098',
              title: '投资第一课',
              description:
                '本期内容介绍投资的基本知识，希望能在这篇文章中让你大致了解个人长期投资的全景图。',
              date: 1697283303823,
            }}
          ></PostCard>
          <PostCard
            post={{
              id: '37f6c3b4-7765-44db-887b-21d5a10e9845',
              title: '多策略交易提醒',
              description:
                '本次分享将以视频会议的方式介绍如何策引（我的投资助手）产品的多策略交易提醒功能，包括多种策略的设计过程及如何通过模拟组合回测数据来了解交易策略的表现。',
              date: 1711792655372,
            }}
          ></PostCard>
          <PostCard
            post={{
              id: '6db4580e-00f0-448d-b3b5-e775ec51cf4b',
              title: '全球投资组合',
              description:
                '本次分享将以视频会议的方式介绍策引全球投资组合的详细情况，包括投资组合的标的池、交易策略与资金策略等方面的内容，还包括全球开户与资金出海等方面的内容。',
              date: 1714203776000,
            }}
          ></PostCard>
          <PostCard
            post={{
              id: '2ff33d2b-79bd-4303-aeda-6cf419fcd360',
              title: '交易的本质',
              description:
                '了解交易策略、资金策略和模拟组合的本质对投资者来说非常重要，这有助于他们更好地理解投资系统的运作方式。',
              date: 1715394062000,
            }}
          ></PostCard>
        </div>
      </Section>
      <Footer />
    </div>
  );
};

export { Posts };
