import Link from 'next/link';

import { IPost } from '../../types/types';

type IPostCardProps = {
  post: IPost;
};

const PostCard = ({ post }: IPostCardProps) => {
  return (
    <Link href={`/post/${post.id}`} passHref>
      <div className="flex flex-col items-center justify-center mb-10 text-center cursor-pointer hover:text-red-500">
        {post.image && (
          <img src={post.image} alt={post.title} className="w-full" />
        )}
        <h3 className="text-2xl font-bold">{post.title}</h3>
        <p>{post.description}</p>
        {post.date && (
          <div className="flex flex-col items-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {new Date(post.date).toLocaleDateString()}
            </p>
            <div className="w-2 h-2 mt-2 rounded-full bg-gray-400 mr-1"></div>
          </div>
        )}
      </div>
    </Link>
  );
};

export { PostCard };
