import { Meta } from '../layout/Meta';
import { Section } from '../layout/Section';
import { Footer } from './Footer';
import { Header } from './Header';

const About = () => {
  const timelineEvents = [
    {
      year: '2024年',
      content:
        '全面升级策引：支持多种交易策略和多市场投资组合，允许用户自定义投资组合和交易策略。',
    },
    {
      year: '2023年',
      content:
        '产品转型与AI化：引入Chat2Invest AI驱动的投资分析工具，重构系统，更名为我的投资助手。建立了专属知识星球社区，供用户交流工具使用技巧和市场信息分享，请注意，社区内容不构成任何投资建议。',
    },
    {
      year: '2022年',
      content: '功能扩展：加入模拟组合回测评估功能，此工具开源，用户数接近1K。',
    },
    {
      year: '2020年',
      content: '技术升级与重构：推出自定义指标触发通知工具“Invest Alchemy”。',
    },
    {
      year: '2018年',
      content:
        '功能调整：推出支持用户自定义配置的“被动收入投资组合”管理功能，由用户自行选择指数基金等标的。开发新Excel模板监控投资组合。',
    },
    { year: '2016年', content: '初试锋芒：开发“交易日记”App，关注指数基金。' },
    {
      year: '2015年',
      content: '灾难与启发：经历A股股灾，创业公司倒闭，学习投资交易原理。',
    },
  ];

  return (
    <div className="antialiased text-gray-600">
      <Meta
        title="策引的故事"
        description="Discover the journey of 策引 - our story and evolution"
      />
      <Header />

      <Section title="策引的故事">
        <div className="timeline-container mx-auto py-5">
          {timelineEvents.map((item, idx) => (
            <div
              key={idx}
              className={`flex items-start ${
                idx % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
              } relative py-5`}
            >
              <div className="z-10 px-4 py-2 rounded-lg shadow-lg bg-white w-full max-w-md">
                <h3 className="font-semibold text-lg mb-1">{item.year}</h3>
                <p>{item.content}</p>
              </div>
              <div
                className="absolute w-3 h-3 bg-main rounded-full"
                style={{
                  left: '50%',
                  transform: 'translateX(-50%)',
                  top: '10px',
                }}
              ></div>
              {idx < timelineEvents.length - 1 && (
                <div
                  className="absolute w-1 bg-main"
                  style={{
                    left: '50%',
                    transform: 'translateX(-50%)',
                    top: '10px',
                    height: '100%',
                  }}
                ></div>
              )}
            </div>
          ))}
        </div>
      </Section>
      <Footer />
    </div>
  );
};

export { About };
