import Link from 'next/link';

import { Meta } from '../layout/Meta';
import { Section } from '../layout/Section';
import { AppConfig } from '../utils/AppConfig';
import { Footer } from './Footer';
import { Header } from './Header';
import { ImageWithContent } from './Premium';

const Chat2Invest = () => {
  return (
    <div className="antialiased text-gray-600">
      <Meta title={AppConfig.title} description={AppConfig.description} />
      <Header />

      <div>
        <Section title="Chat2Invest">
          <div>
            <p className="mt-4 text-xl md:px-20">
              <Link
                href="https://www.chat2invest.com/"
                className="underline text-red-500 hover:text-main mr-1"
              >
                Chat2Invest
              </Link>
              是一款基于大模型的支持多语言的AI驱动的投资分析工具。您可以使用它来获取个股的深度分析报告和多维度数据洞察，还可以根据多种技术指标和用户自定义条件筛选股票。目前支持A股与美股共计超过17000只股票与ETF的分析。在未来，我们还将支持更多的股票市场及加密币市场。
            </p>
          </div>
        </Section>
      </div>

      <Section>
        <h3 className="text-2xl font-bold text-main text-center">功能特性</h3>
        <ImageWithContent
          id="chat2invest_analysi_stock"
          title="个股深度分析"
          description={`
          它能够从基本面、技术面与消息面等多个维度为您提供个股的分析数据和信息。它支持A股与美股共计超过17000只股票与ETF的分析。
          <ul class="list-disc list-inside flex flex-wrap justify-between mt-2">
            <li class="w-full sm:w-1/2">
              <a href="https://www.chat2invest.com/ticker/us/nasdaq/stock" class="underline text-red-500 hover:text-main mr-1">美股纳斯达克股票</a>
            </li>
            <li class="w-full sm:w-1/2">
              <a href="https://www.chat2invest.com/ticker/us/nasdaq/etf" class="underline text-red-500 hover:text-main mr-1">美股纳斯达克ETF</a>
            </li>
            <li class="w-full sm:w-1/2">
              <a href="https://www.chat2invest.com/ticker/us/nyse/stock" class="underline text-red-500 hover:text-main mr-1">美股纽交所股票</a>
            </li>
            <li class="w-full sm:w-1/2">
              <a href="https://www.chat2invest.com/ticker/us/nyse/etf" class="underline text-red-500 hover:text-main mr-1">美股纽交所ETF</a>
            </li>
            <li class="w-full sm:w-1/2">
              <a href="https://www.chat2invest.com/ticker/china/sh/stock" class="underline text-red-500 hover:text-main mr-1">A股上交所股票</a>
            </li>
            <li class="w-full sm:w-1/2">
              <a href="https://www.chat2invest.com/ticker/china/sh/etf" class="underline text-red-500 hover:text-main mr-1">A股上交所ETF</a>
            </li>
            <li class="w-full sm:w-1/2">
              <a href="https://www.chat2invest.com/ticker/china/sz/stock" class="underline text-red-500 hover:text-main mr-1">A股深交所股票</a>
            </li>
            <li class="w-full sm:w-1/2">
              <a href="https://www.chat2invest.com/ticker/china/sz/etf" class="underline text-red-500 hover:text-main mr-1">A股深交所ETF</a>
            </li>
          </ul>
        `}
          imageUrl="/assets/images/chat2invest.png"
          imagePosition="left"
        />
        <ImageWithContent
          id="chat2invest_portfolio"
          title="自定义投资组合"
          description={`可通过自然语言创建基于用户自定义规则或技术指标组合的自定义投资组合，可自定义标的池及创建时间，可订阅组合的交易通知。（2025年上线）</span>
        `}
          imageUrl="/assets/images/grid-icons/asset-8.svg"
          imagePosition="right"
        />
        <ImageWithContent
          id="chat2invest_market"
          title="市场分析"
          description={`Chat2Invest可以帮助您从多个维度了解目前的市场情况，包括市场估值、市场风险、市场情绪等。<span class="underline">（2025年上线）</span>
        `}
          imageUrl="/assets/images/grid-icons/asset-4.svg"
          imagePosition="left"
        />
        <ImageWithContent
          id="chat2invest_learning"
          title="投资学习"
          description={`基于大模型海量的金融知识库，您可以与Chat2Invest进行投资学习。
        `}
          imageUrl="/assets/images/grid-icons/asset-6.svg"
          imagePosition="right"
        />
      </Section>

      <Section title="重要问题">
        <div className="space-y-4">
          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                策引会员包含Chat2Invest的所有功能吗？
              </h5>

              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>

            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              是的，两者的账号是通用的。策引会员可使用
              <Link
                href={'https://www.chat2invest.com/'}
                className="text-red-500"
              >
                Chat2Invest
              </Link>
              的所有功能。
            </p>
          </details>
        </div>
      </Section>
      <Footer />
    </div>
  );
};

export { Chat2Invest };
