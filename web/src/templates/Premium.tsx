import { useState } from 'react';

import { trackEvent } from '../utils/trackEvent';

type Props = {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  imagePosition: 'left' | 'right';
};

const ImageWithContent = ({
  id,
  title,
  description,
  imageUrl,
  imagePosition,
}: Props) => {
  const isImageOnLeft = imagePosition === 'left';

  // useState to show the message after the button is clicked
  const [message, setMessage] = useState('');

  const handleClick = async () => {
    // @ts-ignore
    umami.track(id);
    await trackEvent('IWantThisFeature', { featureId: id });
    setMessage('已经收到您的需求，感谢您的反馈！');
  };

  return (
    <div className="flex flex-wrap">
      <div
        id={id}
        className={`w-full md:w-1/2 p-6 ${isImageOnLeft ? 'md:order-2' : ''}`}
      >
        <h3 className="text-3xl text-gray-800 font-bold leading-none mb-3">
          {title}
        </h3>
        <div dangerouslySetInnerHTML={{ __html: description }} />
        <button
          className="mt-4 bg-gray-500 text-white font-bold py-2 px-4 rounded m-auto"
          onClick={handleClick}
        >
          我需要此功能
        </button>
        {message && <p className="mt-4 text-main">{message}</p>}
      </div>
      <div
        className={`w-full md:w-1/2 p-6 ${isImageOnLeft ? 'md:order-1' : ''}`}
      >
        <img src={imageUrl} alt="" className="w-8/12" />
      </div>
    </div>
  );
};

export { ImageWithContent };
