import React from 'react';

import Link from 'next/link';

import EULANotice from '../components/EULANotice';
import { FeatureCard } from '../components/FeatureCard';
import { Meta } from '../layout/Meta';
import { Section } from '../layout/Section';
import { AppConfig } from '../utils/AppConfig';
import { Footer } from './Footer';
import { Header } from './Header';

const Pricing = () => {
  const handleClick = (plan: string) => () => {
    if (plan === 'annual') {
      // @ts-ignore
      umami.track('Yearly button');
      window.open(
        `https://chat2invest.lemonsqueezy.com/buy/dd853669-d61b-445e-90c8-dd1b189ec138`,
        '_blank'
      );
    }
    if (plan === 'lifetime') {
      // @ts-ignore
      umami.track('Lifetime button');
      window.open(
        `https://chat2invest.lemonsqueezy.com/buy/cae39408-9499-422c-86ca-05f81f0e7ac3`,
        '_blank'
      );
    }
  };



  return (
    <div className="antialiased text-gray-600">

      <Meta title={AppConfig.title} description={AppConfig.description} />
      <Header />

      {/* 年中优惠 */}
      <Section>
        <div className="bg-yellow-50 border-l-4 border-main text-gray-800 p-6 mb-8 rounded-r-lg">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-main rounded-full flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-800">年中优惠活动</h2>
            </div>
            <p className="text-gray-700 mb-6">策引分析工具会员限时半价优惠，立即享受专业的市场分析服务</p>
            
            <div className="grid md:grid-cols-2 gap-6 text-sm">
              <div className="bg-white p-4 rounded-lg border border-yellow-200">
                <h3 className="font-medium text-gray-800 mb-2">获取优惠</h3>
                <div className="space-y-2">
                  <p>微信联系：<code className="bg-gray-100 px-2 py-0.5 rounded font-mono text-xs">improve365_cn</code></p>
                  <p>说明购买意向即可获取半价优惠码</p>
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg border border-yellow-200">
                <h3 className="font-medium text-gray-800 mb-2">其他优惠</h3>
                <div className="space-y-2">
                  <p>• 首页问卷：获得1个月试用</p>
                  <p>• <Link href="https://www.i365.tech/pricing" className="text-main hover:underline">i365会员</Link>：可享半价</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Section>

      {/* 会员计划部分 */}
      <Section title="会员计划">
        <div className="flex flex-col md:flex-row items-stretch justify-center gap-8">
          <div className="border border-gray-200 rounded-lg p-6 w-full md:w-1/2 max-w-md flex flex-col justify-between relative">
            {/* 优惠标签 */}
            <div className="absolute -top-3 -right-3 bg-main text-black px-3 py-1 rounded-full text-xs font-medium">
              限时半价
            </div>
            <div>
              <h2 className="text-2xl font-bold mb-4">年付会员</h2>
              <div className="mb-2">
                <p className="text-lg text-gray-500 line-through">
                  原价：$88<span className="text-sm">/年</span>
                </p>
                <p className="text-3xl font-semibold text-main">
                  半价：$44<span className="text-xl">/年</span>
                </p>
              </div>
              <ul className="list-disc list-inside mb-4 text-left">
                <li>AI驱动的分析工具 (Chat2Invest)</li>
                <li>自定义模拟投资组合</li>
                <li>自定义策略信号提醒</li>
                <li>支持A股、美股与加密币分析</li>
                <li>知识星球社区</li>
                <li>会员专栏内容</li>
              </ul>
            </div>
            <div className="mt-4 space-y-2">
              <button
                onClick={handleClick('annual')}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full transition duration-300"
              >
                原价购买 ($88)
              </button>
              <p className="text-center text-sm text-gray-600">
                💬 微信 <span className="font-mono font-bold text-main">improve365_cn</span> 获取半价优惠
              </p>
            </div>
          </div>
          <div className="border-2 border-main rounded-lg p-6 w-full md:w-1/2 max-w-md bg-yellow-50 flex flex-col justify-between transform hover:scale-105 transition-transform duration-300 relative">
            {/* 优惠标签 */}
            <div className="absolute -top-3 -right-3 bg-main text-black px-3 py-1 rounded-full text-xs font-medium">
              限时半价
            </div>
            <div>
              <h2 className="text-2xl font-bold mb-4 text-gray-800">
                终身会员
              </h2>
              <div className="mb-2">
                <p className="text-lg text-gray-500 line-through">
                  原价：$499<span className="text-sm">/终身</span>
                </p>
                <p className="text-3xl font-semibold text-main">
                  半价：$249<span className="text-xl">/终身</span>
                </p>
              </div>
              <ul className="list-disc list-inside mb-4 text-left">
                <li>包含年付会员所有功能</li>
                <li>永久享受功能更新</li>
                <li>优先技术支持</li>
                <li>1V1咨询服务</li>
                <li>一次付费，终身使用</li>
              </ul>
            </div>
            <div className="mt-4 space-y-2">
              <button
                onClick={handleClick('lifetime')}
                className="bg-main hover:bg-yellow-600 text-black font-bold py-2 px-4 rounded w-full transition duration-300"
              >
                原价购买 ($499)
              </button>
              <a
                href="https://calendly.com/myinvestpilot/meet-with-me"
                target="_blank"
                rel="noopener noreferrer"
                className="block text-center bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full transition duration-300"
              >
                📅 预约1V1咨询服务
              </a>
              <p className="text-center text-sm text-gray-600">
                💬 微信 <span className="font-mono font-bold text-main">improve365_cn</span> 获取半价优惠
              </p>
            </div>
          </div>
        </div>
        <EULANotice />
      </Section>

      {/* 会员权益部分 */}
      <Section title="会员权益">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FeatureCard
            title="AI驱动的投资分析工具 (Chat2Invest)"
            description="支持从基本面、技术面与消息面等多维度提供投资分析。支持A股与美股超17000只股票与ETF分析，未来将支持多种投资策略筛选股票，所有分析结果仅供用户参考。"
            videoUrl="https://media.i365.tech/myinvestpilot/chat2invest-demo.mp4"
          />
          <FeatureCard
            title="自定义模拟组合"
            description="定制官方组合，创建基于不同交易策略的自定义模拟组合。自定义标的池（支持A股、美股及加密币），微调策略参数，订阅组合信号通知。"
            videoUrl="https://media.i365.tech/myinvestpilot/custom-portfolio-demo.mp4"
          />
          <FeatureCard
            title="投资组合分析"
            description="深度分析用户自定义的模拟组合，了解收益归因。从风险与回报等多个角度分析组合表现，为用户进行长期投资决策提供数据参考。"
            videoUrl="https://media.i365.tech/myinvestpilot/portfolio-analysis-demo.mp4"
          />
          <FeatureCard
            title="自定义策略信号通知"
            description="订阅用户自定义的投资策略交易信号，及时接收邮件提醒。支持双均线、特定止损与均线策略等多种策略，提醒服务仅根据用户设定的条件发送通知。"
            videoUrl="https://media.i365.tech/myinvestpilot/trade-signal-demo.mp4"
          />

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold mb-2">多市场支持</h3>
            <img
              src="/assets/images/market.svg"
              alt="多市场支持"
              className="w-full h-48 object-cover mb-4"
            />
            <p>
              策引目前支持A股、美股及加密币市场，覆盖超过1.7万只股票及ETF。未来我们计划进一步扩展，支持更多市场和投资标的，为您提供更全面的投资选择。
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-semibold mb-2">知识社区</h3>
            <img
              src="/assets/images/zsxq.jpeg"
              alt="社区交流"
              className="w-full h-48 object-cover mb-4"
            />
            <p>
              会员可免费加入专属知识星球社区及微信群，获取专业投资知识，与其他会员交流投资心得。
            </p>
          </div>
        </div>
      </Section>

      {/* 常见问题部分 */}
      <Section title="常见问题">
        <div className="space-y-4">
          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                如何选择适合我的会员计划？
              </h5>
              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>
            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              如果您是新手投资者或想尝试我们的服务，建议选择年付会员。如果您是长期投资者，并且确信我们的服务能为您带来价值，终身会员将是更具成本效益的选择。
            </p>
          </details>
          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                我有问题咨询，怎么联系你们？
              </h5>
              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>
            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              如果您有任何问题，可发送邮件至{' '}
              <span className="text-red-500">hello(at)myinvestpilot.com</span>{' '}
              与我们联系。
            </p>
          </details>
        </div>
      </Section>

      <Footer />
    </div>
  );
};

export { Pricing };
