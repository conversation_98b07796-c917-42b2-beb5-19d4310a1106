import React, { ReactNode } from 'react';

import { FeatureShowcase } from '../components/FeatureShowcase';
import { Meta } from '../layout/Meta';
import { AppConfig } from '../utils/AppConfig';
import { Footer } from './Footer';
import { Header } from './Header';
import { Hero } from './Hero';

interface BaseProps {
  children?: ReactNode;
  meta?: {
    title?: string;
    description?: string;
  };
  showDefaultContent?: boolean;
}

const Base = ({ children, meta, showDefaultContent = false }: BaseProps) => {
  const metaData = {
    title: meta?.title || AppConfig.title,
    description: meta?.description || AppConfig.description,
  };

  return (
    <div className="antialiased text-gray-600">
      <Meta {...metaData} />
      <Header />
      {showDefaultContent ? (
        <>
          <Hero />
          <FeatureShowcase />
        </>
      ) : (
        <main>{children}</main>
      )}
      <Footer />
    </div>
  );
};

export { Base };
