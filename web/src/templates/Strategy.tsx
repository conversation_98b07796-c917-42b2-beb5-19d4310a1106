import Link from 'next/link';

import { StrategyProps } from '../../types/types';
import { Meta } from '../layout/Meta';
import { Section } from '../layout/Section';
import { AppConfig } from '../utils/AppConfig';
import { Footer } from './Footer';
import { Header } from './Header';

const Strategy: React.FC<StrategyProps> = ({ strategies }) => {
  const renderStrategies = () => (
    <Section title="策略及相关组合">
      {/* 创建模拟策略组合的CTA区域 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-8 mb-8 text-center">
        <div className="max-w-2xl mx-auto">
          <div className="text-4xl mb-4">📊</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-3">
            创建模拟投资策略组合
          </h2>
          <p className="text-gray-600 mb-4 text-lg">
            基于量化分析工具，创建用于学习和研究的模拟策略组合
          </p>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6 text-sm text-yellow-800">
            <div className="flex items-start space-x-2">
              <div className="text-yellow-600">⚠️</div>
              <div>
                <strong>重要提示：</strong>
                本功能仅用于投资学习和策略研究，所创建的组合为模拟组合，不构成任何投资建议。投资有风险，请谨慎决策。
              </div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              href="/portfolios/create"
              passHref
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              创建模拟组合
            </Link>
            <div className="text-sm text-gray-500">
              📈 多市场数据 • 🔧 参数调整 • 📊 历史回测
            </div>
          </div>
        </div>
      </div>

      <p className="mt-4 text-xl md:px-20">
        选择下面某个策略的投资组合，可基于此组合的策略配置，定制创建您的模拟投资组合用于学习研究。
      </p>
      <div className="space-y-8">
        {strategies.map((strategy) => (
          <div
            key={strategy.strategy_id}
            className="bg-white p-6 rounded-lg shadow-md"
          >
            <h3 className="text-xl font-semibold mb-2">
              {strategy.strategy_name}
            </h3>
            <p className="text-gray-600 mb-4">
              {strategy.strategy_description}
            </p>
            <h4 className="text-lg font-medium mb-2">相关组合：</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {strategy.official_portfolios.map((portfolio) => (
                <div key={portfolio.code} className="border p-4 rounded-md">
                  <h5 className="font-medium">{portfolio.name}</h5>
                  <p className="text-sm text-gray-500 mb-4">
                    市场: {portfolio.market === 'China' ? '中国' : '美国'}
                  </p>
                  <Link
                    href={{
                      pathname: '/portfolio',
                      query: { mode: 'copy', code: portfolio.code },
                    }}
                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                  >
                    定制模拟组合
                  </Link>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </Section>
  );

  return (
    <div className="antialiased text-gray-600">
      <Meta title={AppConfig.title} description={AppConfig.description} />
      <Header />

      {renderStrategies()}

      <div>
        <Section title="多策略交易提醒">
          <div>
            <p className="mt-4 text-xl md:px-20">
              策引支持多种交易策略的每日交易邮件提醒。当您订阅了某个
              <Link href={'/portfolios'} className="text-blue-500">
                投资组合
              </Link>
              的交易提醒后，我们会在每个交易日的开盘前为您发送邮件，告知您今日的交易计划。您可以根据邮件中的指引，自行决定是否进行交易。同时，该策略关联的模拟组合也会在每个交易日的开盘前进行调仓。您可以在该组合的详情页查看每日的调仓记录。如果您订阅后，你将会收到类似下面的邮件：
            </p>
            <div className="pt-10 pb-10 mx-auto">
              <img
                src="/assets/images/email.png"
                alt="多策略交易提醒"
                className="mx-auto"
              ></img>
              <p className="text-sm text-gray-500 mt-2 text-center">
                <span className="font-semibold">示例邮件内容解读：</span>
                邮件会包含&ldquo;交易信号&rdquo;（如买入、卖出、持有），&ldquo;组合表现&rdquo;（当日盈亏），&ldquo;持仓&rdquo;（当前持有的标的及成本），以及&ldquo;组合净值曲线图&rdquo;（历史表现概览）。请注意，此为示例邮件，实际内容可能因策略和市场情况而有所不同。
              </p>
            </div>
            <p className="mt-4 text-xl md:px-20 text-red-500 font-semibold">
              <span className="underline">重要提示：</span>{' '}
              策引平台仅提供数据分析工具和信息，所有交易信号及邮件提醒仅供参考，不构成任何投资建议。用户应根据自身风险承受能力和独立判断进行投资决策。历史表现不代表未来收益，投资有风险，入市需谨慎。
            </p>
          </div>
        </Section>
      </div>

      <Section title="重要问题">
        <div className="space-y-4">
          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                基于官方策略的自定义组合介绍
              </h5>

              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>

            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              目前支持基于官方组合内置的交易策略和资金策略进行自定义组合。比如，您可以选择A股1号组合，通过复制其配置，自定义一个新的组合，可以选择您感兴趣的标的，或者调整资金分配策略，或者微调交易策略的参数。目前的限制有：
              <br />
              <span className="font-medium">
                1. 目前不支持修改交易策略和资金策略，只能微调策略参数。
              </span>
              <br />
              <span className="font-medium">
                2. 每个用户最多可以创建 5 个自定义组合。
              </span>
              <br />
              <span className="font-medium">
                3. 每个用户最多订阅 5 个组合的交易提醒。
              </span>
              <br />
              <span className="font-medium">
                4.
                如果您创建了自定义组合，但是没有订阅交易提醒，那么该组合将会在两天后被删除。
              </span>
            </p>
          </details>

          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                是否支持其他交易策略？
              </h5>

              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>

            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              我们会不定期上线主流的交易策略，您可以选择您感兴趣的策略并进行自定义组合，回测组合的历史表现，并且订阅交易提醒。如果您有好的策略想要分享，可以联系我们，我们会考虑上线。
            </p>
          </details>

          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                交易信号中状态的定义是什么？
              </h5>

              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>

            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              买入：之前没有持仓此标的，现在根据此策略的交易信号可以进行买入操作。
              <br />
              卖出：当前标的根据此策略之前的信号是持有状态，目前可以全部卖出或部分卖出。大部分策略的卖出都是全仓卖出。
              <br />
              持有：之前根据策略的信号已经买入的标的，目前仍处于持仓状态中。
              <br />
              空仓：在本策略下，没有任何持有的标的。例如，如果一个策略的标的池中有10个标的，其中三个处于&ldquo;持有&rdquo;状态，一个&ldquo;买入&rdquo;，一个&ldquo;卖出&rdquo;，那么剩余五个标的处于&ldquo;空仓&rdquo;状态。
            </p>
          </details>

          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                如何在接收到组合买入信号时分配资金？各个组合的资金策略是什么？
              </h5>

              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>

            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              资金策略是用来决定如何分配资金进行投资的策略。不同的投资组合有其独特的资金分配策略。例如：
              <br />
              A股1号组合：使用百分比资金策略，最大20%的资金用于每个标的。如果一天内有3个买入信号，但只有足够的资金购买两个标的，组合将根据信号顺序选择前两个进行交易。
              <br />
              A股2号组合：采取满仓买入策略，即买入信号出现时，将所有可用资金投入推荐的标的。
              <br />
              投资者应根据自己的风险偏好和资金状况设定个人资金策略。
            </p>
          </details>

          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                我已经持有了订阅的策略组合中显示空仓的标的，该卖出吗？订阅的策略组合中显示已经持有的标的，我现在应该买入吗？
              </h5>

              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>

            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              这是一个订阅组合建仓的典型问题。很多人中途关注某个组合，但是这个组合已经建仓并持有一些标的很久了，该怎么买入？建议是你应该等待这个标的出现买入信号的时候才能买入，这样你才能与该组合的策略保持一致。
              <br />
              那闲置的资金该怎么处理？一方面你可以买一些活期的理财来等待入场机会，或者你可以关注其他有交易机会的组合来提高资金利用率，但要注意一旦你选择了其他组合的策略，那卖出应必须跟随那个组合的策略，保持策略的一致性（买入和卖出都是同一个策略）很重要。
              <br />
              所以对于策略已经买入持有的标的，你也只能等待下次买入的机会，当然也不是完全不能买入，如果策略的买入价格和目前的价格相差不大，可以考虑跟入，或者在能承担亏损风险的前提下，分批买入也可以，但是一旦你选择跟随这个策略，如果这个标的在这个策略下卖出了，不论你怎么买入，都应该跟随卖出。
            </p>
          </details>

          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                邮件提醒的时间以及非交易日的信号处理是怎样的？
              </h5>
              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>
            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              我们会在每个交易日的开盘前发送邮件，告知您今日的策略及组合的交易计划。具体时间如下：
              <br />- <strong>A股市场</strong>
              ：开市前（上午9:30-11:30和下午1:00-3:00），通常在北京时间早上9点左右发送，周一不会发送邮件，周一的操作见上周六的邮件通知。
              <br />- <strong>美股市场</strong>
              ：开市前（晚上9:30至次日凌晨4:00），通常在北京时间下午4点左右发送，周一不会发送邮件，周一的操作见上周六的邮件通知。
              <br />- <strong>加密币市场</strong>
              ：因为是全天候交易，通常在北京时间中午发送邮件。
              <br />
              在非交易日，对于A股与美股来说，如节假日，我们使用最近一个交易日的数据来更新信号。例如，这周四是公共假期，直到下周一收假，那你周四依旧会收到邮件通知，实际上这是对下周一的操作指引。
            </p>
          </details>

          <details className="group">
            <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
              <h5 className="font-medium text-gray-900">
                策略支持日内策略吗？
              </h5>

              <svg
                className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </summary>

            <p className="px-4 mt-4 leading-relaxed text-gray-700">
              目前所有的策略都是基于日线数据进行的，因此不支持日内策略。我们的策略是基于简单且长期投资的理念，通过回测和实证的方法，选取了长期收益稳定的策略。
            </p>
          </details>
        </div>
      </Section>
      <Footer />
    </div>
  );
};

export { Strategy };
