import React, { useState, useEffect, useMemo } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/router';
import { InfinitySpin } from 'react-loader-spinner';

import { Portfolio, Profile } from '../../types/types';
import Modal from '../components/Modal';
import Notification from '../components/Notification';
import { Meta } from '../layout/Meta';
import { Section } from '../layout/Section';
import {
  deletePortfolio,
  getProfile,
  getUserPortfolios,
  getSubscribedPortfolios,
  updatePortfolioData,
} from '../utils/api';
import { getUpdatePagePath } from '../utils/portfolioHelper';
import { trackEvent } from '../utils/trackEvent';
import { Footer } from './Footer';
import { Header } from './Header';

export const Me: React.FC = () => {
  const router = useRouter();
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [subscribedPortfolios, setSubscribedPortfolios] = useState<
    { portfolio_code: string }[]
  >([]);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<{
    isOpen: boolean;
    message: string;
    type: 'success' | 'error';
  }>({ isOpen: false, message: '', type: 'success' });
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
    isConfirmDisabled: boolean;
    showSpinner: boolean;
  }>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    isConfirmDisabled: false,
    showSpinner: false,
  });

  const errorMsgWithLoginLink = useMemo(
    () => '请登录后再操作',
    []
  );

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [profileData, portfoliosData, subscribedPortfoliosData] =
          await Promise.all([
            getProfile(),
            getUserPortfolios(),
            getSubscribedPortfolios(),
          ]);
        setProfile(profileData);
        setPortfolios(portfoliosData);
        setSubscribedPortfolios(subscribedPortfoliosData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(errorMsgWithLoginLink);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [errorMsgWithLoginLink]);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const handleDelete = async (portfolioId: string) => {
    setModalState({
      isOpen: true,
      title: '确认删除',
      message: '您确定要删除这个投资组合吗？此操作不可逆。',
      isConfirmDisabled: false,
      showSpinner: false,
      onConfirm: async () => {
        try {
          await deletePortfolio(portfolioId);
          await trackEvent('UserDeletePortfolio', {});
          setModalState((prev) => ({
            ...prev,
            title: '删除成功',
            message: '投资组合正在删除中，请稍候...',
            isConfirmDisabled: true,
            showSpinner: true,
          }));

          setTimeout(() => {
            setModalState((prev) => ({ ...prev, isOpen: false }));
            setNotification({
              isOpen: true,
              message: '投资组合已成功删除，页面即将刷新。',
              type: 'success',
            });
            setTimeout(() => {
              router.reload();
            }, 2000);
          }, 3000);
        } catch (e) {
          console.error('Error deleting portfolio:', e);
          setModalState({
            isOpen: true,
            title: '删除失败',
            message: '无法删除投资组合，请稍后再试。',
            onConfirm: () => {},
            isConfirmDisabled: false,
            showSpinner: false,
          });
        }
      },
    });
  };

  const handleUpdateContent = (portfolioId: string) => {
    // 从已有的portfolios数据中找到对应的组合
    const portfolio = portfolios.find(p => p.code === portfolioId);
    
    if (portfolio) {
      try {
        const updatePath = getUpdatePagePath(portfolioId, portfolio);
        router.push(updatePath);
      } catch (error) {
        console.error('Error determining portfolio type:', error);
        // 如果判断失败，默认跳转到代码策略页面
        router.push(`/portfolio?mode=update&code=${portfolioId}`);
      }
    } else {
      // 如果找不到组合数据，默认跳转到代码策略页面
      router.push(`/portfolio?mode=update&code=${portfolioId}`);
    }
  };

  const handleUpdateData = async (portfolioId: string) => {
    setModalState({
      isOpen: true,
      title: '更新投资组合数据',
      message:
        '更新操作可能需要5-10分钟完成。请耐心等待，如果遇到问题，可以查看日志并联系管理员。',
      isConfirmDisabled: false,
      showSpinner: false,
      onConfirm: async () => {
        try {
          await updatePortfolioData(portfolioId);
          setModalState((prevState) => ({ ...prevState, isOpen: false }));
          setNotification({
            isOpen: true,
            message: '更新请求已发送，请等待5-10分钟后查看结果。',
            type: 'success',
          });
        } catch (e) {
          console.log('Update initiated, waiting for async processing');
          setModalState((prevState) => ({ ...prevState, isOpen: false }));
          setNotification({
            isOpen: true,
            message:
              '更新请求已发送，请等待5-10分钟后查看结果。如果遇到问题，请查看日志。',
            type: 'success',
          });
        } finally {
          await trackEvent('UserPortfolioDataUpdate', {
            portfolio: portfolioId,
          });
          setTimeout(
            () => setNotification({ isOpen: false, message: '', type: 'success' }),
            5000
          );
        }
      },
    });
  };

  const handleViewLog = (portfolioId: string) => {
    const currentDate = new Date();
    const formattedDate = `${currentDate.getFullYear()}_${String(
      currentDate.getMonth() + 1
    ).padStart(2, '0')}_${String(currentDate.getDate()).padStart(2, '0')}`;
    const logUrl = `https://media.i365.tech/myinvestpilot/portfolios/${portfolioId}/${portfolioId}_${formattedDate}.log`;
    window.open(logUrl, '_blank');
  };

  const handleViewDetails = (portfolioId: string) => {
    router.push(`/portfolios/${portfolioId}`);
  };

  // 信号分析 - 跳转到专门的信号分析页面
  const handleSignalAnalysis = async (portfolioId: string) => {
    // 直接跳转到信号分析页面
    window.location.href = `/portfolios/${portfolioId}/signals`;
  };

  const renderPortfolioList = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {portfolios.map((portfolio) => (
        <div
          key={portfolio.id}
          className="border rounded shadow p-4 flex flex-col h-full"
        >
          <div className="flex-grow">
            <h3 className="text-xl font-semibold mb-2">{portfolio.name}</h3>
            <p className="text-sm mb-2">{portfolio.description}</p>
            <p className="text-sm">
              <span className="font-medium">代码:</span>{' '}
              <code className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded font-mono text-sm font-bold">
                {portfolio.code}
              </code>
            </p>
            <p className="text-sm">
              <span className="font-medium">市场:</span> {portfolio.market}
            </p>
            <p className="text-sm">
              <span className="font-medium">货币:</span> {portfolio.currency}
            </p>
            <p className="text-sm">
              <span className="font-medium">开始日期:</span>{' '}
              {new Date(portfolio.start_date).toLocaleDateString()}
            </p>
            <p className="text-sm">
              <span className="font-medium">股票代码:</span>{' '}
              {Array.isArray(portfolio.symbols) 
                ? portfolio.symbols.map(s => typeof s === 'string' ? s : s.symbol).join(', ')
                : ''}
            </p>
          </div>
          {/* 按钮区域 */}
          <div className="mt-4 flex flex-col gap-3">
            {/* 管理操作组 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="text-xs text-blue-600 font-medium mb-2 flex items-center">
                <span className="mr-1">⚙️</span>
                管理操作
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleViewDetails(portfolio.code)}
                  className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                >
                  组合详情
                </button>
                <button
                  onClick={() => handleUpdateContent(portfolio.code)}
                  className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors"
                >
                  更新配置
                </button>
                <button
                  onClick={() => handleUpdateData(portfolio.code)}
                  className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors"
                >
                  更新数据
                </button>
              </div>
            </div>

            {/* 分析诊断组 */}
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
              <div className="text-xs text-purple-600 font-medium mb-2 flex items-center">
                <span className="mr-1">📊</span>
                分析诊断
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleViewLog(portfolio.code)}
                  className="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600 transition-colors"
                >
                  组合日志
                </button>
                <button
                  onClick={() => handleSignalAnalysis(portfolio.code)}
                  className="px-3 py-1 bg-indigo-500 text-white rounded text-sm hover:bg-indigo-600 transition-colors"
                >
                  信号分析
                </button>
              </div>
            </div>

            {/* 危险操作组 */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="text-xs text-red-600 font-medium mb-2 flex items-center">
                <span className="mr-1">⚠️</span>
                危险操作
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleDelete(portfolio.code)}
                  className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors border-2 border-red-300"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const renderPortfolios = () => {
    if (portfolios.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-xl mb-4">您还没有创建任何投资组合</p>
          <Link href="/portfolios" legacyBehavior>
            <a className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition duration-200">
              定制属于您的投资组合
            </a>
          </Link>
        </div>
      );
    }

    return (
      <>
        {/* 重要提醒 */}
        <div className="mb-6 bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
          <div className="flex items-start space-x-3">
            <div className="text-amber-500 text-xl">⚠️</div>
            <div>
              <div className="font-semibold text-amber-800 mb-2">重要提醒</div>
              <div className="text-sm text-amber-700 space-y-1">
                <p>• <strong>订阅要求：</strong>创建的投资组合必须在 <span className="font-bold text-amber-900">48小时内</span> 被至少一人订阅（包括自己）</p>
                <p>• <strong>自动清理：</strong>未被订阅的组合将在创建后2天自动删除，以保证系统资源的有效利用</p>
                <p>• <strong>操作建议：</strong>创建组合后请及时点击"组合详情"进行订阅，然后手动点击"更新数据"并等待3-5分钟获取初始数据。如无数据显示请查看"组合日志"排查问题，后续组合将在设定时间自动更新</p>
              </div>
            </div>
          </div>
        </div>
        {renderPortfolioList()}
      </>
    );
  };

  const renderSubscribedPortfolios = () => {
    if (subscribedPortfolios.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-xl mb-4">
            您还没有订阅任何投资组合，去组合页面看看吧！
          </p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {subscribedPortfolios.map((portfolio) => (
          <div
            key={portfolio.portfolio_code}
            className="border rounded shadow p-4 flex flex-col h-full"
          >
            <div className="flex-grow">
              <p className="text-sm">
                <span className="font-medium">代码:</span>{' '}
                <code className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded font-mono text-sm font-bold">
                  {portfolio.portfolio_code}
                </code>
                <img
                  src={`https://media.i365.tech/myinvestpilot/portfolios/${portfolio.portfolio_code}/${portfolio.portfolio_code}_portfolio_thumbnail.png`}
                  alt={portfolio.portfolio_code}
                  className="h-32 mb-4"
                />
              </p>
            </div>
            <div className="mt-4">
              <button
                onClick={() => handleViewDetails(portfolio.portfolio_code)}
                className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
              >
                查看详情
              </button>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderProfile = () => {
    if (!profile) return null;

    return (
      <Section title="会员信息">
        <div className="p-4 border rounded shadow">
          <p className="text-sm">
            <span className="font-medium">邮箱:</span> {profile.email}
          </p>
          <p className="text-sm">
            <span className="font-medium">会员到期日:</span>{' '}
            {formatDate(profile.premium_end_date)}
          </p>
        </div>
      </Section>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-64">
          <InfinitySpin width="200" color="#4fa94d" />
        </div>
      );
    }

    if (error) {
      return (
        <div className="mb-4 p-4 bg-red-100 border-l-4 border-red-500 text-red-700">
          <span>
            {error}
            {error.includes('登录') && (
              <>
                ，点击
                <a
                  href="https://login.myinvestpilot.com/?r=www.myinvestpilot.com/me"
                  className="text-blue-600 hover:text-blue-800 underline ml-1 mr-1"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  【这里登录】
                </a>
                即可
              </>
            )}
          </span>
        </div>
      );
    }

    return (
      <>
        <Section title="我订阅的组合">{renderSubscribedPortfolios()}</Section>
        <Section title="我的策略组合">{renderPortfolios()}</Section>
        {renderProfile()}
      </>
    );
  };

  return (
    <div className="antialiased text-gray-600">
      <Meta title="我的" description="我的 - 策引" />
      <Header />
      <div className="container mx-auto px-4 py-8">{renderContent()}</div>
      <Footer />
      <Modal
        isOpen={modalState.isOpen}
        onClose={() => setModalState({ ...modalState, isOpen: false })}
        onConfirm={modalState.onConfirm}
        title={modalState.title}
        message={modalState.message}
        isConfirmDisabled={modalState.isConfirmDisabled}
        showSpinner={modalState.showSpinner}
      />
      <Notification
        isOpen={notification.isOpen}
        message={notification.message}
        type={notification.type}
      />
    </div>
  );
};
