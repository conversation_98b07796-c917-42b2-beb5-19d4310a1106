import { useState, useEffect } from 'react';

import { MarketData, MarketType } from '../../types/market';
import { CryptoMarketIndicator } from '../components/markets/CryptoMarketIndicator';
import { MarketEquityBondRatio } from '../components/markets/MarketEquityBondRatio';
import { MarketHeader } from '../components/markets/MarketHeader';
import { MarketIndicatorCard } from '../components/markets/MarketIndicatorCard';
import { MarketSummary } from '../components/markets/MarketSummary';
import { USMacroIndicator } from '../components/markets/USMacroIndicator';
import Notification from '../components/Notification';
import { Section } from '../layout/Section';
import { fetchMarketData } from '../utils/market';
import { trackEvent } from '../utils/trackEvent';
import { Base } from './Base';

interface MarketsTemplateProps {
  meta?: {
    title: string;
    description: string;
  };
}

interface MarketDataState {
  [key: string]: MarketData | null;
}

interface AIAnalysisState {
  cn: boolean;
  us: boolean;
  crypto: boolean;
}

const MarketsTemplate = ({ meta }: MarketsTemplateProps) => {
  const [marketsData, setMarketsData] = useState<MarketDataState>({
    cn: null,
    us: null,
    crypto: null,
  });
  const [activeMarket, setActiveMarket] = useState<MarketType>('us');
  const [aiAnalysisState, setAiAnalysisState] = useState<AIAnalysisState>({
    cn: false,
    us: false,
    crypto: false,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showNotification, setShowNotification] = useState(false);

  useEffect(() => {
    const loadMarketData = async () => {
      if (marketsData[activeMarket]) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const data = await fetchMarketData<MarketData>(
          activeMarket,
          'market_summary.json'
        );
        setMarketsData((prev) => ({
          ...prev,
          [activeMarket]: data,
        }));
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : '加载市场数据失败';
        setError(errorMessage);
        setShowNotification(true);
        setTimeout(() => setShowNotification(false), 3000);
      } finally {
        setLoading(false);
      }
    };

    loadMarketData();
  }, [activeMarket, marketsData]);

  const handleMarketChange = (market: MarketType) => {
    setActiveMarket(market);
  };

  const handleToggleAiAnalysis = async () => {
    setAiAnalysisState((prev) => ({
      ...prev,
      [activeMarket]: !prev[activeMarket],
    }));
    // @ts-ignore
    umami.track('MarketAIAnalysis', { market: activeMarket });
    await trackEvent('MarketAIAnalysis', { market: activeMarket });
  };

  const renderMarketContent = () => {
    const currentMarketData = marketsData[activeMarket];

    if (!currentMarketData) return null;

    switch (activeMarket) {
      case 'cn':
        if (currentMarketData.market !== 'CN') return null;
        return (
          <div className="space-y-8">
            <MarketSummary
              data={currentMarketData}
              showAiAnalysis={aiAnalysisState.cn}
              onToggleAiAnalysis={handleToggleAiAnalysis}
            />

            <MarketIndicatorCard
              title="股债收益偏离指标 (SBRD)"
              description="跟踪股票市场相对于债券市场的强弱变化"
              explanation="SBRD指标反映了权益市场和债券市场的收益差异。从经济学角度看，权益资本作为生产要素，其长期收益理应高于债务资本。当这个差异显著偏离历史正常水平时，往往暗示了市场定价体系的临时性失衡，这种失衡通常会通过各种市场力量来修正。"
              data={currentMarketData.indicators.sbrd}
              imgSrc="https://media.i365.tech/myinvestpilot/markets/cn/sbrd.png"
            />

            <MarketIndicatorCard
              title="PE历史分位"
              description="反映当前市场估值在历史分布中的位置"
              explanation="PE（市盈率）历史分位指标反映了当前市场估值在历史分布中的相对位置。它通过比较当前市场PE与历史数据，帮助投资者了解市场的整体估值水平。较高的分位数表明市场估值偏高，可能面临调整压力；较低的分位数则表明市场估值相对便宜，可能存在投资机会。"
              data={currentMarketData.indicators.pe_percentile}
              imgSrc="https://media.i365.tech/myinvestpilot/markets/cn/pe_percentile.png"
            />

            <MarketEquityBondRatio
              data={currentMarketData.indicators.equity_bond_ratio}
            />
          </div>
        );
      case 'us':
        if (currentMarketData.market !== 'US') return null;
        return (
          <div className="space-y-8">
            <MarketSummary
              data={currentMarketData}
              showAiAnalysis={aiAnalysisState.us}
              onToggleAiAnalysis={handleToggleAiAnalysis}
            />

            <USMacroIndicator
              data={currentMarketData.indicators.macro}
              imgSrc="https://media.i365.tech/myinvestpilot/markets/us/us_macro.png"
            />
          </div>
        );
      case 'crypto':
        if (currentMarketData.market !== 'CRYPTO') return null;
        return (
          <div className="space-y-8">
            <MarketSummary
              data={currentMarketData}
              showAiAnalysis={aiAnalysisState.crypto}
              onToggleAiAnalysis={handleToggleAiAnalysis}
            />

            <CryptoMarketIndicator
              data={currentMarketData.indicators.crypto}
              imgSrc="https://media.i365.tech/myinvestpilot/markets/crypto/crypto_market_indicators.png"
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Base meta={meta}>
      <Section>
        <MarketHeader
          activeMarket={activeMarket}
          onMarketChange={handleMarketChange}
        />

        <Notification
          isOpen={showNotification}
          message={error || ''}
          type="error"
        />

        {/* 使用绝对定位和透明度过渡来平滑切换内容 */}
        <div className="relative min-h-[600px]">
          {' '}
          {/* 设置一个最小高度防止布局跳动 */}
          {loading ? (
            <div className="absolute inset-0 flex justify-center items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : (
            <div
              key={activeMarket} // 添加 key 确保切换时重新渲染
              className="transition-all duration-300 ease-in-out opacity-100"
            >
              {renderMarketContent()}
            </div>
          )}
        </div>
      </Section>
    </Base>
  );
};

export { MarketsTemplate };
