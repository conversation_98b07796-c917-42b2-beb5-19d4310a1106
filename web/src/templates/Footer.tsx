import Link from 'next/link';

import { Background } from '../background/Background';
import { CenteredFooter } from '../footer/CenteredFooter';
import { Section } from '../layout/Section';
import { Logo } from './Logo';

const Footer = () => (
  <Background color="bg-gray-100 mt-6">
    <Section>
      <CenteredFooter
        logo={<Logo />}
        mainLinks={
          <>
            <Link
              href="/learning"
              className="text-amber-600 hover:text-amber-700 transition-colors"
            >
              知识社区
            </Link>
            <Link
              href="/post"
              className="text-amber-600 hover:text-amber-700 transition-colors"
            >
              会员专栏
            </Link>
            <Link
              href="https://www.bmpi.dev/money/"
              className="text-amber-600 hover:text-amber-700 transition-colors"
            >
              博客文章
            </Link>
            <Link
              href="https://www.bmpi.dev/money/guide-to-open-global-investment-account/"
              className="text-amber-600 hover:text-amber-700 transition-colors"
            >
              全球开户
            </Link>
            <Link
              href="/about"
              className="text-amber-600 hover:text-amber-700 transition-colors"
            >
              关于策引
            </Link>
            <Link
              href="/eula"
              className="text-amber-600 hover:text-amber-700 transition-colors"
            >
              协议与免责
            </Link>
          </>
        }
        socialLinks={
          <>
            <Link
              href="https://x.com/myinvestpilot"
              className="text-amber-600 hover:text-amber-700 transition-colors"
            >
              Twitter
            </Link>
            <Link
              href="https://t.me/myinvestpilot"
              className="text-amber-600 hover:text-amber-700 transition-colors"
            >
              Telegram
            </Link>
            <Link
              href="https://discord.gg/S9mzJfqfKD"
              className="text-amber-600 hover:text-amber-700 transition-colors"
            >
              Discord
            </Link>
          </>
        }
      >
        <div className="text-sm text-gray-600 text-center mt-8">
          策引为个人开发的全球市场数据分析工具，通过付费方式开通使用权限，不构成公开服务。
          <br />
          本平台仅提供数据分析工具，不提供投资咨询、投资建议或任何交易信号。
          <br />
          所有分析结果、技术指标及AI生成内容仅供参考，不构成任何投资建议。
          <br />
          用户需遵守所在地区法律法规并独立判断并自主决策，承担所有投资风险。
        </div>
      </CenteredFooter>
    </Section>
  </Background>
);

export { Footer };
