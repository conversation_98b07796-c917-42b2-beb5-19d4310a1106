import Link from 'next/link';

import { Meta } from '../layout/Meta';
import { Section } from '../layout/Section';
import { AppConfig } from '../utils/AppConfig';
import { Footer } from './Footer';
import { Header } from './Header';

const Learning = () => {
  return (
    <div className="antialiased text-gray-600">
      <Meta title={AppConfig.title} description={AppConfig.description} />
      <Header />

      <div>
        <Section title="知识社区">
          <div>
            <p className="mt-4 text-xl md:px-20">
              投资是个漫长而复杂的过程，我们希望通过知识社区为您提供更多的投资知识。在知识社区中，您可以了解到投资的基本概念、投资的方法、投资的技巧等。我们会定期更新知识社区的内容，希望能够帮助您更好地理解投资。
            </p>
            <p className="mt-4 text-xl md:px-20">
              策引提供以下的一些渠道供您获取投资知识：
            </p>
            <ul className="mt-4 text-xl md:px-20">
              <li className="pb-2">
                <Link
                  href={'https://t.zsxq.com/18d6OcxUt'}
                  className="text-blue-500"
                >
                  知识星球
                </Link>
                ：我们会在知识星球上分享一些关于工具使用、市场信息及个人投资学习心得，请注意，这些分享不构成任何投资建议。
              </li>
              <li className="pb-2">
                <Link href="/post" className="text-blue-500">
                  会员专栏
                </Link>
                ：不定期更新一些投资知识，有时会以视频会议的形式进行。
              </li>
              <li className="pb-2">
                <Link href="/chat2invest" className="text-blue-500">
                  AI
                </Link>
                ：基于ChatGPT的AI驱动的投资分析工具，可以帮助您获取海量的投资知识。
              </li>
              <li className="pb-2">
                <Link
                  href="https://www.bmpi.dev/money/"
                  className="text-blue-500"
                >
                  博客
                </Link>
                ：不定期更新一些可公开的投资知识。
              </li>
              <li className="pb-2">
                微信群：通知策引产品的重要更新及解答相关问题。
              </li>
            </ul>
          </div>
        </Section>
      </div>

      <Footer />
    </div>
  );
};

export { Learning };
