import Link from 'next/link';
import { useRouter } from 'next/router';

import PromotionBanner from '../components/PromotionBanner';
import { Meta } from '../layout/Meta';
import { Section } from '../layout/Section';
import { NavbarTwoColumns } from '../navigation/NavbarTwoColumns';
import { AppConfig } from '../utils/AppConfig';
import { Logo } from './Logo';

const Header = () => {
  const router = useRouter();

  const navItems = [
    { href: '/portfolios', text: '组合', className: '' },
    { href: '/markets', text: '市场', className: '' },
    { href: '/pricing', text: '会员', className: '' },
    { href: '/me', text: '我的', className: '' },
    { href: '/chat2invest', text: 'AI', className: 'shiny-ai font-semibold' },
    { href: 'https://docs.myinvestpilot.com/docs/', text: '文档', className: '' },
  ];

  return (
    <div className="antialiased text-gray-600">
      <Meta
        title={AppConfig.title}
        description={AppConfig.description}
        canonical={AppConfig.canonical + router.asPath}
      />
      <Section yPadding="py-6">
        <NavbarTwoColumns logo={<Logo xl />}>
          {navItems.map((item, index) => (
            <li key={index} className="text-base md:text-xl w-full md:w-auto">
              <Link
                href={item.href}
                className={`block px-2 py-1 hover:bg-gray-50 md:hover:bg-transparent ${item.className}`}
              >
                {item.text}
              </Link>
            </li>
          ))}
        </NavbarTwoColumns>
      </Section>
      {/* 全局优惠通知条 */}
      <PromotionBanner />
    </div>
  );
};

export { Header };
