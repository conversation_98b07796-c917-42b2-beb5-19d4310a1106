import { AppConfig } from '../utils/AppConfig';

type ILogoProps = {
  xl?: boolean;
};

const Logo = (props: ILogoProps) => {
  const size = props.xl ? '44' : '32';
  const fontStyle = props.xl
    ? 'font-semibold lg:text-3xl text-sm'
    : 'font-semibold lg:text-xl text-base';

  return (
    <span className={`text-gray-900 inline-flex items-center ${fontStyle}`}>
      <img
        alt="logo"
        width={size}
        height={size}
        src="/logo.svg"
        className="mr-2"
      />
      {AppConfig.site_name}
    </span>
  );
};

export { Logo };
