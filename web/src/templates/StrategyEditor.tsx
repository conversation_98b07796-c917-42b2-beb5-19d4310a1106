import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { InfinitySpin } from 'react-loader-spinner';

import { getPortfolioDetails, ApiError } from '../utils/api';
import { Portfolio } from '../../types/types';
import { StrategyVisualizer } from '../components/StrategyVisualizer';
import { TradeStrategy } from '../utils/strategyVisualizer';

interface StrategyEditorProps {
  mode?: 'create' | 'update' | 'view';
  initialCode?: string;
}

export const StrategyEditor: React.FC<StrategyEditorProps> = ({
  mode = 'create',
  initialCode,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [error, setError] = useState<string>('');
  const [currentStrategy, setCurrentStrategy] = useState<TradeStrategy | null>(null);

  // 加载现有投资组合数据（如果是更新或查看模式）
  useEffect(() => {
    const loadPortfolioData = async () => {
      if ((mode === 'update' || mode === 'view') && initialCode) {
        setLoading(true);
        try {
          const portfolioData = await getPortfolioDetails(initialCode);
          setPortfolio(portfolioData);
        } catch (err) {
          const apiError = err as ApiError;
          setError(`加载投资组合失败: ${apiError.message}`);
        } finally {
          setLoading(false);
        }
      }
    };

    loadPortfolioData();
  }, [mode, initialCode]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <InfinitySpin width="200" color="#3B82F6" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center">
          <div className="text-red-600 text-lg">⚠️</div>
          <div className="ml-3">
            <h3 className="text-red-800 font-medium">加载错误</h3>
            <p className="text-red-700 mt-1">{error}</p>
          </div>
        </div>
        <button
          onClick={() => router.back()}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          返回
        </button>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">
              🎨 策引可视化策略编辑器
            </h2>
            <p className="text-gray-600">
              {mode === 'view' 
                ? '查看策略的可视化图形表示' 
                : '通过拖拽和连接的方式构建您的交易策略'}
            </p>
          </div>
          <div className="text-4xl">📊</div>
        </div>
      </div>

      {/* 策略可视化组件 */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden" style={{ height: '600px' }}>
        <StrategyVisualizer
          strategyDsl={portfolio?.strategy_definition}
          mode={mode === 'view' ? 'readonly' : 'edit'}
          onStrategyChange={(strategy) => {
            setCurrentStrategy(strategy);
            console.log('策略已更新:', strategy);
          }}
        />
      </div>

      {/* 策略信息面板 */}
      {portfolio && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-700 mb-2">当前策略信息：</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-500">策略名称:</span>
              <span className="ml-2 text-gray-700">{portfolio.name}</span>
            </div>
            <div>
              <span className="text-gray-500">策略代码:</span>
              <span className="ml-2 text-gray-700">{portfolio.code}</span>
            </div>
            <div>
              <span className="text-gray-500">市场:</span>
              <span className="ml-2 text-gray-700">{portfolio.market}</span>
            </div>
          </div>
          {portfolio.strategy_definition?.trade_strategy && (
            <div className="mt-3 text-sm">
              <span className="text-gray-500">策略组成:</span>
              <span className="ml-2 text-gray-700">
                {portfolio.strategy_definition.trade_strategy.indicators?.length || 0} 个指标，
                {portfolio.strategy_definition.trade_strategy.signals?.length || 0} 个信号
              </span>
            </div>
          )}
          {currentStrategy && (
            <div className="mt-3 text-sm">
              <span className="text-gray-500">当前编辑:</span>
              <span className="ml-2 text-gray-700">
                {currentStrategy.indicators?.length || 0} 个指标，
                {currentStrategy.signals?.length || 0} 个信号
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
