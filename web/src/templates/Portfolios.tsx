import Link from "next/link";
import { useState } from "react";

import FAQSection from "../components/FAQSection";
import PortfolioItem from "../components/PortfolioItem";
import RiskDisclaimer from "../components/RiskDisclaimer";
import { Meta } from "../layout/Meta";
import { Section } from "../layout/Section";
import { Portfolio, StrategyGroup } from "../../types/types";
import { AppConfig } from "../utils/AppConfig";
import { Footer } from "./Footer";
import { Header } from "./Header";
import faqDataImport from "../data/faqData";

interface PortfoliosProps {
  portfolios: StrategyGroup[];
}

interface StrategySectionProps {
  title: string;
  description: string;
  portfolios: Portfolio[];
  activeFilter: string;
  isExpanded: boolean;
  onToggle: () => void;
}

// 策略分区组件，显示策略标题、描述和组合卡片，支持折叠功能
const StrategySection: React.FC<StrategySectionProps> = ({ 
  title, 
  description, 
  portfolios, 
  activeFilter, 
  isExpanded, 
  onToggle 
}) => {
  // 根据当前筛选条件过滤组合卡片
  const filteredPortfolios = activeFilter === 'all'
    ? portfolios
    : portfolios.filter((portfolio: Portfolio) => {
        // 将市场名称转换为筛选器格式
        const market = portfolio.market.toLowerCase();
        return market === activeFilter || 
               (activeFilter === 'cn' && market === 'china') || 
               (activeFilter === 'us' && market === 'us') || 
               (activeFilter === 'crypto' && market === 'crypto');
      });
  
  // 如果该策略分区下没有符合筛选条件的组合卡片，则不显示整个分区
  if (filteredPortfolios.length === 0) {
    return null;
  }
  
  // 转换策略名称为更友好的中文名
  const strategyNameMap: { [key: string]: string } = {
    "BuyHoldStrategy": "买入持有策略",
    "DualMovingAverageStrategy": "双均线策略",
    "ChandelierExitMAStrategy": "吊灯止损均线策略",
    "MomentumRotationStrategy": "动量轮动策略",
  };
  
  const displayTitle = strategyNameMap[title] || title;
  
  return (
    <div className="container mx-auto py-6">
      {/* 可点击的标题栏 */}
      <div 
        className="flex items-center justify-between cursor-pointer group bg-white rounded-lg p-4 mb-4 border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200"
        onClick={onToggle}
      >
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <h3 className="text-2xl font-bold text-gray-800 group-hover:text-gray-900">
              {displayTitle}
            </h3>
            <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
              {filteredPortfolios.length} 个组合
            </span>
          </div>
          {description && (
            <p className="text-gray-600 mt-1 text-sm">{description}</p>
          )}
        </div>
        
        {/* 折叠指示器 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 font-medium">
            {isExpanded ? '收起' : '展开'}
          </span>
          <div 
            className={`transform transition-transform duration-200 ${
              isExpanded ? 'rotate-180' : 'rotate-0'
            }`}
          >
            <svg className="w-5 h-5 text-gray-400 group-hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      {/* 折叠内容区域 */}
      <div 
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isExpanded ? 'max-h-none opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
          {filteredPortfolios.map((portfolio: Portfolio) => (
            <PortfolioItem key={portfolio.code} portfolio={portfolio} />
          ))}
        </div>
      </div>
    </div>
  );
};

const Portfolios: React.FC<PortfoliosProps> = ({ portfolios }) => {
  // 设置市场筛选状态，默认显示全部
  const [activeFilter, setActiveFilter] = useState<string>('all');
  
  // 策略折叠状态管理 - 默认全部展开
  const [expandedStrategies, setExpandedStrategies] = useState<{ [key: string]: boolean }>(() => {
    const initialState: { [key: string]: boolean } = {};
    portfolios.forEach((strategyGroup) => {
      initialState[strategyGroup.strategy_id] = true; // 所有策略默认展开
    });
    return initialState;
  });

  // 定义市场筛选选项
  const marketFilters = [
    { id: 'all', name: '全部' },
    { id: 'cn', name: 'A股' },
    { id: 'us', name: '美股' },
    { id: 'crypto', name: '加密币' }
  ];

  // Map strategy names to friendly Chinese names
  const strategyNames: { [key: string]: string } = {
    'DualMovingAverageStrategy': '双均线策略',
    'MovingAverageCrossoverStrategy': '均线交叉策略',
    'RSIStrategy': 'RSI指标策略',
    'BreakoutStrategy': '突破策略',
    'MeanReversionStrategy': '均值回归策略',
    'TrendFollowingStrategy': '趋势跟踪策略',
    'BollingerBandsStrategy': '布林带策略',
    'MACDStrategy': 'MACD策略',
    'ValueInvestingStrategy': '价值投资策略'
  };

  // 切换单个策略的展开/折叠状态
  const toggleStrategy = (strategyId: string) => {
    setExpandedStrategies(prev => ({
      ...prev,
      [strategyId]: !prev[strategyId]
    }));
  };

  // 全部展开
  const expandAll = () => {
    const allExpanded: { [key: string]: boolean } = {};
    portfolios.forEach((strategyGroup) => {
      allExpanded[strategyGroup.strategy_id] = true;
    });
    setExpandedStrategies(allExpanded);
  };

  // 全部折叠
  const collapseAll = () => {
    const allCollapsed: { [key: string]: boolean } = {};
    portfolios.forEach((strategyGroup) => {
      allCollapsed[strategyGroup.strategy_id] = false;
    });
    setExpandedStrategies(allCollapsed);
  };

  // 检查是否所有策略都已展开
  const allExpanded = portfolios.every(strategyGroup => 
    expandedStrategies[strategyGroup.strategy_id]
  );

  // 检查是否所有策略都已折叠
  const allCollapsed = portfolios.every(strategyGroup => 
    !expandedStrategies[strategyGroup.strategy_id]
  );

  return (
    <div className="antialiased text-gray-600">
      <Header />
      <Meta
        title="策引全球投资组合 - 我的投资助手"
        description="探索基于多种策略构建的全球投资组合，用于个人投资研究和分析"
        canonical={`${AppConfig.canonical}/portfolios`}
      />
      
      <RiskDisclaimer />

      <Section title="全球投资组合">
        {/* 市场筛选器 */}
        <div className="sticky top-0 z-10 bg-white py-3 px-4 mb-8 rounded-xl shadow-sm border border-gray-100">
          <div className="max-w-3xl mx-auto flex justify-center">
            <div className="inline-flex bg-gray-100 rounded-full p-1">
              {marketFilters.map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => setActiveFilter(filter.id)}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${activeFilter === filter.id 
                    ? 'bg-white text-main shadow-sm' 
                    : 'text-gray-500 hover:text-main'}`}
                >
                  {filter.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 创建模拟投资组合的CTA区域 */}
        <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-xl p-8 mb-8 text-center">
          <div className="max-w-2xl mx-auto">
            <div className="text-4xl mb-4">📊</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-3">
              创建模拟投资策略组合
            </h2>
            <p className="text-gray-600 mb-6 text-lg">
              基于量化分析工具和官方策略模板，创建用于投资学习和策略研究的模拟组合
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/portfolios/create"
                passHref
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-amber-400 to-amber-500 text-white font-semibold rounded-lg hover:from-amber-500 hover:to-amber-600 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                创建模拟组合
              </Link>
              <div className="text-sm text-gray-500">
                📊 数据分析 • 🔧 参数调整 • 📧 学习提醒
              </div>
            </div>
          </div>
        </div>

        {/* 全局展开/折叠控制 */}
        <div className="container mx-auto px-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-xl font-semibold text-gray-800">投资策略组合</h2>
              <span className="text-sm text-gray-500">
                共 {portfolios.length} 种策略
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={expandAll}
                disabled={allExpanded}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                  allExpanded 
                    ? 'text-gray-400 cursor-not-allowed' 
                    : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                }`}
              >
                全部展开
              </button>
              <span className="text-gray-300">|</span>
              <button
                onClick={collapseAll}
                disabled={allCollapsed}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                  allCollapsed 
                    ? 'text-gray-400 cursor-not-allowed' 
                    : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                }`}
              >
                全部折叠
              </button>
            </div>
          </div>
        </div>

        {/* 策略分组展示区域 */}
        <div className="container mx-auto px-4">
          {portfolios.map((strategyGroup) => (
            <StrategySection
              key={strategyGroup.strategy_id}
              title={strategyNames[strategyGroup.strategy_name] || strategyGroup.strategy_name}
              description={strategyGroup.strategy_description}
              portfolios={strategyGroup.official_portfolios}
              activeFilter={activeFilter}
              isExpanded={expandedStrategies[strategyGroup.strategy_id] || false}
              onToggle={() => toggleStrategy(strategyGroup.strategy_id)}
            />
          ))}
        </div>
      </Section>

      <Section title="策略分析与投资研究">
        <div className="max-w-4xl mx-auto">
          {/* 主要描述区域 */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-8 mb-8 border border-blue-100">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-800 mb-3">策略跟踪与分析服务</h3>
                <p className="text-gray-700 text-lg leading-relaxed">
                  策引提供多种投资策略的深度分析和跟踪服务。当您关注某个投资组合后，我们会定期为您提供该策略的分析报告，包含策略执行情况、市场环境分析和投资研究参考。这些分析内容旨在帮助您更好地理解投资策略的运作机制，为您的投资研究提供数据支持和决策参考。
                </p>
              </div>
            </div>
          </div>

          {/* 示例展示区域 */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="text-center mb-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-2">策略分析报告示例</h4>
              <p className="text-gray-600">了解我们的策略分析报告包含哪些内容</p>
            </div>
            
            <div className="flex justify-center mb-6">
              <Link 
                href={"https://media.i365.tech/myinvestpilot/myinvestpilot_cn_global_email_sample.html"} 
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-medium rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                target="_blank"
                rel="noopener noreferrer"
                passHref
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                查看A股全球策略分析报告样例
              </Link>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h5 className="font-semibold text-gray-800 mb-3 flex items-center">
                <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                分析报告主要内容
              </h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>策略信号分析：</strong>基于量化模型生成的策略建议（仅供研究参考）</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>组合表现追踪：</strong>模拟组合的历史表现和风险指标</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>持仓结构分析：</strong>当前模拟持仓的资产配置和成本分析</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                  <span><strong>市场环境解读：</strong>影响策略表现的市场因素分析</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Section>

      <Section title="常见问题">
        <FAQSection categories={faqDataImport.categories} />
      </Section>
      <Footer />
    </div>
  );
};

export { Portfolios };
