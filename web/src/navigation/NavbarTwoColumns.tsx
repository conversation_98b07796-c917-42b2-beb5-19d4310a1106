import { ReactNode, useState } from 'react';

import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

type INavbarProps = {
  logo: ReactNode;
  children: ReactNode;
};

const NavbarTwoColumns = (props: INavbarProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="relative">
      <div className="flex flex-wrap justify-between items-center">
        <div>
          <Link href="/" legacyBehavior>
            <a>{props.logo}</a>
          </Link>
        </div>

        {/* 移动端菜单按钮 */}
        <button
          className="md:hidden p-2"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          {isMenuOpen ? (
            <XMarkIcon className="h-6 w-6" />
          ) : (
            <Bars3Icon className="h-6 w-6" />
          )}
        </button>

        {/* 桌面端导航 */}
        <nav className="hidden md:block">
          <ul className="navbar flex items-center font-medium text-xl text-gray-800">
            {props.children}
          </ul>
        </nav>

        {/* 移动端导航菜单 */}
        {isMenuOpen && (
          <div className="absolute top-full left-0 right-0 bg-white shadow-lg rounded-b-lg md:hidden z-50">
            <nav className="py-2">
              <ul className="flex flex-col space-y-2">
                {Array.isArray(props.children) &&
                  props.children.map((child, index) => (
                    <li key={index} className="px-4 py-2 hover:bg-gray-50">
                      {child}
                    </li>
                  ))}
              </ul>
            </nav>
          </div>
        )}
      </div>
    </div>
  );
};

export { NavbarTwoColumns };
