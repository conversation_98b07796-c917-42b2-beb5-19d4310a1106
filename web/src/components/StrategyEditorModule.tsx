import React, { useState, useEffect, useCallback } from 'react';
import { JsonEditor } from 'json-edit-react';
import dynamic from 'next/dynamic';
import { InfinitySpin } from 'react-loader-spinner';

// 动态导入StrategyVisualizer，避免SSR问题
const StrategyVisualizer = dynamic(() => import('./StrategyVisualizer').then(mod => ({ default: mod.StrategyVisualizer })), {
  ssr: false,
  loading: () => <div className="flex justify-center items-center h-64"><InfinitySpin width="200" color="#4fa94d" /></div>
});

export type EditorMode = 'visual' | 'text' | 'flow';

interface StrategyEditorModuleProps {
  /** 当前策略JSON数据 */
  data: any;
  /** 数据更新回调 */
  onDataChange: (data: any) => void;
  /** 编辑模式 */
  mode: EditorMode;
  /** 模式切换回调 */
  onModeChange: (mode: EditorMode) => void;
  /** JSON解析错误回调 */
  onParsingError?: (error: string) => void;
  /** 是否只读模式 */
  readonly?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 未来扩展：节点参数编辑回调 */
  onNodeParamEdit?: (nodeId: string, params: any) => void;
}

/**
 * 策略编辑器模块组件
 * 
 * 支持三种编辑模式：
 * 1. visual: 可视化表单编辑（JsonEditor）
 * 2. text: 文本编辑（textarea）
 * 3. flow: 流程图可视化（StrategyVisualizer）
 * 
 * 特性：
 * - 模块化设计，易于扩展
 * - 支持模式间数据同步
 * - 为未来的参数编辑功能预留接口
 * - 错误处理和用户反馈
 */
export const StrategyEditorModule: React.FC<StrategyEditorModuleProps> = ({
  data,
  onDataChange,
  mode,
  onModeChange,
  onParsingError,
  readonly = false,
  className = '',
  onNodeParamEdit,
}) => {
  const [localData, setLocalData] = useState(data);
  const [textValue, setTextValue] = useState('');

  // 同步外部数据变化
  useEffect(() => {
    setLocalData(data);
    setTextValue(JSON.stringify(data, null, 2));
  }, [data]);

  // 处理可视化表单编辑器的数据更新
  const handleVisualEditorChange = useCallback((newData: any) => {
    try {
      setLocalData(newData);
      onDataChange(newData);
      if (onParsingError) {
        onParsingError(''); // 清除错误
      }
    } catch (error) {
      if (onParsingError) {
        onParsingError(`可视化编辑器错误: ${(error as Error).message}`);
      }
    }
  }, [onDataChange, onParsingError]);

  // 处理文本编辑器的数据更新
  const handleTextEditorChange = useCallback((value: string) => {
    setTextValue(value);
    
    try {
      if (!value.trim()) {
        return;
      }
      
      const parsedData = JSON.parse(value);
      setLocalData(parsedData);
      onDataChange(parsedData);
      if (onParsingError) {
        onParsingError(''); // 清除错误
      }
    } catch (error) {
      if (onParsingError) {
        onParsingError('JSON格式错误，请检查语法是否正确');
      }
    }
  }, [onDataChange, onParsingError]);

  // 处理流程图可视化器的数据更新
  const handleFlowEditorChange = useCallback((newData: any) => {
    try {
      setLocalData(newData);
      onDataChange(newData);
      if (onParsingError) {
        onParsingError(''); // 清除错误
      }
    } catch (error) {
      if (onParsingError) {
        onParsingError(`流程图编辑器错误: ${(error as Error).message}`);
      }
    }
  }, [onDataChange, onParsingError]);

  // 处理节点参数编辑（未来功能）
  const handleNodeParameterEdit = useCallback((nodeId: string, params: any) => {
    if (onNodeParamEdit) {
      onNodeParamEdit(nodeId, params);
    }
    // 未来这里会实现直接修改localData中对应节点的参数
    // 然后调用onDataChange更新整个策略
  }, [onNodeParamEdit]);

  // 渲染模式切换按钮
  const renderModeSelector = () => (
    <div className="flex items-center space-x-2 mb-4">
      <span className="text-sm text-gray-600">编辑模式:</span>
      <button
        type="button"
        onClick={() => onModeChange('visual')}
        className={`px-3 py-1 text-sm rounded-md transition-colors ${
          mode === 'visual'
            ? 'bg-blue-500 text-white'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        }`}
        disabled={readonly}
      >
        📝 表单
      </button>
      <button
        type="button"
        onClick={() => onModeChange('text')}
        className={`px-3 py-1 text-sm rounded-md transition-colors ${
          mode === 'text'
            ? 'bg-blue-500 text-white'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        }`}
        disabled={readonly}
      >
        📄 文本
      </button>
      <button
        type="button"
        onClick={() => onModeChange('flow')}
        className={`px-3 py-1 text-sm rounded-md transition-colors ${
          mode === 'flow'
            ? 'bg-blue-500 text-white'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        }`}
      >
        🔄 流程图
      </button>
    </div>
  );

  // 渲染当前模式的编辑器
  const renderEditor = () => {
    switch (mode) {
      case 'visual':
        return (
          <JsonEditor
            data={localData}
            setData={handleVisualEditorChange}
            rootName="strategy"
            enableClipboard={true}
            showArrayIndices={true}
            showCollectionCount={true}
            showStringQuotes={true}
            stringTruncate={100}
            minWidth="100%"
            maxWidth="100%"
            readOnly={readonly}
            onError={(error) => {
              console.error('JSON Editor Error:', error);
              if (onParsingError) {
                if (error.error?.code === 'INVALID_JSON') {
                  onParsingError('无效的JSON格式！请检查语法是否正确');
                } else if (error.error?.code === 'UPDATE_ERROR') {
                  onParsingError('更新失败！请检查输入的数据格式是否正确');
                } else {
                  onParsingError(`编辑器错误：${error.error?.message || '未知错误'}`);
                }
              }
            }}
          />
        );

      case 'text':
        return (
          <textarea
            value={textValue}
            onChange={(e) => handleTextEditorChange(e.target.value)}
            rows={20}
            readOnly={readonly}
            className="w-full p-3 border border-gray-300 rounded-md bg-white font-mono text-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
            placeholder="在此输入 JSON 策略定义..."
          />
        );

      case 'flow':
        return (
          <div className="border border-gray-300 rounded-md overflow-hidden" style={{ height: '600px' }}>
            <StrategyVisualizer
              strategyDsl={localData}
              mode="readonly" // 流程图模式始终为只读，纯可视化
              onStrategyChange={handleFlowEditorChange}
              onNodeParameterEdit={handleNodeParameterEdit} // 未来功能接口
            />
          </div>
        );

      default:
        return <div>未知的编辑模式</div>;
    }
  };

  return (
    <div className={`strategy-editor-module ${className}`}>
      {renderModeSelector()}
      {renderEditor()}
    </div>
  );
};

export default StrategyEditorModule;
