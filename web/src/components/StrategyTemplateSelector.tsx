import React from 'react';

import { StrategyTemplate } from '../../types/types';
import templates from '../utils/strategyTemplates.json';

interface Props {
  onLoadTemplate: (json: any) => void;
}

const StrategyTemplateSelector: React.FC<Props> = ({ onLoadTemplate }) => (
  <div className="space-y-4">
    {(templates as StrategyTemplate[]).map((tpl) => (
      <div key={tpl.name} className="border p-4 rounded-md">
        <h4 className="font-semibold">{tpl.name}</h4>
        <p className="text-sm text-gray-600 mb-2">{tpl.description}</p>
        <button
          type="button"
          onClick={() => onLoadTemplate(tpl.dslJson)}
          className="px-2 py-1 bg-blue-500 text-white rounded"
        >
          加载模板
        </button>
      </div>
    ))}
  </div>
);

export default StrategyTemplateSelector;
