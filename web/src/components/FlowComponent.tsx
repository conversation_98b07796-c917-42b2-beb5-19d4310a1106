import React, { useCallback } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
} from 'reactflow';
import 'reactflow/dist/style.css';

interface FlowComponentProps {
  initialNodes: Node[];
  initialEdges: Edge[];
  onNodesChange?: (nodes: Node[]) => void;
  onEdgesChange?: (edges: Edge[]) => void;
  mode?: 'readonly' | 'edit';
}

const FlowComponent: React.FC<FlowComponentProps> = ({
  initialNodes,
  initialEdges,
  onNodesChange,
  onEdgesChange,
  mode = 'readonly',
}) => {
  const [nodes, setNodes, onNodesChangeInternal] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChangeInternal] = useEdgesState(initialEdges);

  // 处理连接
  const onConnect = useCallback(
    (params: Edge | Connection) => {
      if (mode === 'edit') {
        const newEdges = addEdge(params, edges);
        setEdges(newEdges);
        onEdgesChange?.(newEdges);
      }
    },
    [edges, mode, onEdgesChange, setEdges]
  );

  // 处理节点变化
  const handleNodesChange = useCallback(
    (changes: any) => {
      onNodesChangeInternal(changes);
      if (mode === 'edit') {
        onNodesChange?.(nodes);
      }
    },
    [nodes, mode, onNodesChange, onNodesChangeInternal]
  );

  // 处理边变化
  const handleEdgesChange = useCallback(
    (changes: any) => {
      onEdgesChangeInternal(changes);
      if (mode === 'edit') {
        onEdgesChange?.(edges);
      }
    },
    [edges, mode, onEdgesChange, onEdgesChangeInternal]
  );

  // 当初始数据变化时更新节点和边
  React.useEffect(() => {
    setNodes(initialNodes);
  }, [initialNodes, setNodes]);

  React.useEffect(() => {
    setEdges(initialEdges);
  }, [initialEdges, setEdges]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={onConnect}
        nodesDraggable={mode === 'edit'}
        nodesConnectable={mode === 'edit'}
        elementsSelectable={mode === 'edit'}
        fitView
      >
        <Controls />
        <MiniMap />
        <Background variant={BackgroundVariant.Dots} />
      </ReactFlow>
    </div>
  );
};

export default FlowComponent;
