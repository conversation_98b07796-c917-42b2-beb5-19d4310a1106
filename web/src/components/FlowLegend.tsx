import React, { useState } from 'react';

interface LegendItem {
  color: string;
  borderColor: string;
  label: string;
  description: string;
}

const legendItems: LegendItem[] = [
  {
    color: '#3B82F6', // blue-500
    borderColor: '#2563EB', // blue-600
    label: '输入节点',
    description: '数据流的起点，只有输出连线'
  },
  {
    color: '#6B7280', // gray-500
    borderColor: '#4B5563', // gray-600
    label: '中间节点',
    description: '处理数据的中间步骤，有输入和输出连线'
  },
  {
    color: '#10B981', // emerald-500
    borderColor: '#059669', // emerald-600
    label: '买入信号输出',
    description: '策略的买入信号输出'
  },
  {
    color: '#EF4444', // red-500
    borderColor: '#DC2626', // red-600
    label: '卖出信号输出',
    description: '策略的卖出信号输出'
  },
  {
    color: '#8B5CF6', // violet-500
    borderColor: '#7C3AED', // violet-600
    label: '指标调试输出',
    description: '调试用指标输出，会在信号数据库中显示'
  },
  {
    color: '#F59E0B', // amber-500
    borderColor: '#D97706', // amber-600
    label: '市场指标调试输出',
    description: '调试用市场指标输出，会在信号数据库中显示'
  },
  {
    color: '#9CA3AF', // gray-400
    borderColor: '#6B7280', // gray-500
    label: '未使用节点',
    description: '策略中定义但未被引用的组件'
  }
];

interface FlowLegendProps {
  className?: string;
}

export default function FlowLegend({ className = '' }: FlowLegendProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* 可点击的标题栏 */}
      <div
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h3 className="text-sm font-semibold text-gray-900">节点类型说明</h3>
        <div className={`transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}>
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* 可折叠的内容 */}
      {isExpanded && (
        <div className="px-3 pb-3">
          <div className="space-y-2">
            {legendItems.map((item, index) => (
              <div key={index} className="flex items-center space-x-3">
                {/* 颜色示例 */}
                <div
                  className="w-4 h-4 rounded flex-shrink-0"
                  style={{
                    backgroundColor: item.color,
                    border: `2px solid ${item.borderColor}`
                  }}
                />

                {/* 标签和描述 */}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900">
                    {item.label}
                  </div>
                  <div className="text-xs text-gray-500">
                    {item.description}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 额外说明 */}
          <div className="mt-4 pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              <div className="flex items-center space-x-2 mb-1">
                <div className="w-3 h-0.5 bg-gray-400"></div>
                <span>连接线表示数据流向</span>
              </div>
              <div className="text-gray-400">
                箭头指向数据的流动方向
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
