import Link from 'next/link';

import { Portfolio } from '../../types/types';

interface PortfolioItemProps {
  portfolio: Portfolio;
}

const PortfolioItem = ({ portfolio }: PortfolioItemProps) => {
  const { name, description, code, market } = portfolio;
  // Define market tag styles based on market type
  const marketTagStyles: {[key: string]: string} = {
    china: 'bg-red-100 text-red-800 border-red-200',
    us: 'bg-blue-100 text-blue-800 border-blue-200',
    crypto: 'bg-purple-100 text-purple-800 border-purple-200',
    default: 'bg-gray-100 text-gray-800 border-gray-200'
  };

  // Get the market display name
  const marketNames: {[key: string]: string} = {
    china: 'A股',
    us: '美股',
    crypto: '加密币'
  };
  
  const marketKey = market?.toLowerCase() || 'default';
  const marketStyle = marketTagStyles[marketKey] || marketTagStyles.default;
  const marketName = marketNames[marketKey] || market;

  return (
    <div className="flex flex-col border rounded-lg shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden bg-white transform hover:-translate-y-1">
      {/* Portfolio Image */}
      <div className="relative overflow-hidden">
        <Link href={`/portfolios/${code}`} passHref>
          <img
            src={`https://media.i365.tech/myinvestpilot/portfolios/${code}/${code}_portfolio_thumbnail.png`}
            alt={name}
            className="w-full h-40 object-cover"
          />
          {/* Market Tag */}
          <div className="absolute top-3 right-3">
            <span className={`text-xs font-medium px-2 py-1 rounded-full border ${marketStyle}`}>
              {marketName}
            </span>
          </div>
        </Link>
      </div>
      
      {/* Content */}
      <div className="p-4">
        <Link href={`/portfolios/${code}`} passHref>
          <h3 className="text-lg font-semibold text-gray-800 hover:text-main transition-colors mb-2 line-clamp-1">
            {name}
          </h3>
        </Link>
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{description}</p>
        <div className="mt-auto flex flex-wrap justify-between items-center gap-2">
          <Link 
            href={`/portfolios/${code}`}
            passHref
            className="text-sm text-main hover:text-amber-600 font-medium flex items-center"
          >
            查看详情
            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </Link>
          
          <Link
            href={{
              pathname: '/portfolio',
              query: { mode: 'copy', code },
            }}
            passHref
            className="text-sm bg-amber-500 text-white px-3 py-1.5 rounded hover:bg-amber-600 transition-colors flex items-center"
          >
            <svg 
              className="w-4 h-4 mr-1" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            定制模拟组合
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PortfolioItem;
