import React from 'react';

interface PortfolioTabsProps {
  currentTab: string;
  setCurrentTab: (tab: string) => void;
  isLoggedIn: boolean;
  transactions: string[][] | undefined;
  holdings: string[][] | undefined;
  fundings: string[][] | undefined;
  transactionsPageIndex: number;
  setTransactionsPageIndex: (index: number) => void;
  transactionsPageNum: number;
  holdingsPageIndex: number;
  setHoldingsPageIndex: (index: number) => void;
  holdingsPageNum: number;
  fundingsPageIndex: number;
  setFundingsPageIndex: (index: number) => void;
  fundingsPageNum: number;
  setTransactionsData: (pageNum: number) => void;
  setHoldingsData: (pageNum: number) => void;
  setFundingsData: (pageNum: number) => void;
}

const PortfolioTabs: React.FC<PortfolioTabsProps> = ({
  currentTab,
  setCurrentTab,
  isLoggedIn,
  transactions,
  holdings,
  fundings,
  transactionsPageIndex,
  setTransactionsPageIndex,
  transactionsPageNum,
  holdingsPageIndex,
  setHoldingsPageIndex,
  holdingsPageNum,
  fundingsPageIndex,
  setFundingsPageIndex,
  fundingsPageNum,
  setTransactionsData,
  setHoldingsData,
  setFundingsData,
}) => {
  const renderPagination = (
    currentPage: number,
    setPage: (page: number) => void,
    totalPages: number,
    setData: (page: number) => void
  ) => (
    <div className="inline-flex items-center justify-center space-x-3 mt-3">
      <button
        className="inline-flex items-center justify-center w-8 h-8 border border-gray-100 rounded"
        onClick={() => {
          if (currentPage > 1) {
            setPage(currentPage - 1);
            setData(currentPage - 1);
          }
        }}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-3 h-3"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      <p className="text-xs">
        {currentPage}
        <span className="mx-0.25">/</span>
        {totalPages}
      </p>

      <button
        className="inline-flex items-center justify-center w-8 h-8 border border-gray-100 rounded"
        onClick={() => {
          if (currentPage < totalPages) {
            setPage(currentPage + 1);
            setData(currentPage + 1);
          }
        }}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-3 h-3"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
            clipRule="evenodd"
          />
        </svg>
      </button>
    </div>
  );

  const renderTransactionsTable = () => (
    <table className="min-w-full text-sm border border-gray-100 divide-y-2 divide-gray-200">
      <thead>
        <tr className="divide-x divide-gray-100">
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            日期
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            代码
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            名称
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            类型
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            数量
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            价格
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            金额
          </th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200">
        {transactions?.map((transaction, i) => (
          <tr className="divide-x divide-gray-100" key={i}>
            {transaction.map((value, j) => (
              <td
                key={j}
                className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap text-center"
              >
                {value}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );

  const renderHoldingsTable = () => (
    <table className="min-w-full text-sm border border-gray-100 divide-y-2 divide-gray-200">
      <thead>
        <tr className="divide-x divide-gray-100">
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            日期
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            代码
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            持仓数量
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            收盘价
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            市值
          </th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200">
        {holdings?.map((holding, i) => (
          <tr className="divide-x divide-gray-100" key={i}>
            {holding.map((value, j) => (
              <td
                key={j}
                className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap text-center"
              >
                {value}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );

  const renderFundingsTable = () => (
    <table className="min-w-full text-sm border border-gray-100 divide-y-2 divide-gray-200">
      <thead>
        <tr className="divide-x divide-gray-100">
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            日期
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            类型
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            变动金额
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            可用资金
          </th>
          <th className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap">
            总资产
          </th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200">
        {fundings?.map((funding, i) => (
          <tr className="divide-x divide-gray-100" key={i}>
            {funding.map((value, j) => (
              <td
                key={j}
                className="px-4 py-2 font-medium text-gray-900 whitespace-nowrap text-center"
              >
                {value}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );

  const renderLoginMessage = () => {
    let message = '请点击【我的】菜单登录后查看';
    switch (currentTab) {
      case 'transaction':
        message += '历史交易记录';
        break;
      case 'holding':
        message += '历史持仓情况';
        break;
      case 'funding':
        message += '资金流水详情';
        break;
      default:
        message += '详细内容';
    }

    return (
      <div className="text-center py-8">
        <p className="text-lg font-medium text-gray-600">{message}</p>
      </div>
    );
  };

  return (
    <div className="sm:w-9/12 w-11/12 mt-20 mb-20">
      <nav className="flex text-base font-bold border-b border-gray-100">
        <button
          onClick={() => setCurrentTab('transaction')}
          className={`p-4 -mb-px border-b ${
            currentTab === 'transaction'
              ? 'border-current text-yellow-500'
              : 'border-transparent hover:text-yellow-500'
          }`}
        >
          历史交易
        </button>
        <button
          onClick={() => setCurrentTab('holding')}
          className={`p-4 -mb-px border-b ${
            currentTab === 'holding'
              ? 'border-current text-yellow-500'
              : 'border-transparent hover:text-yellow-500'
          }`}
        >
          历史持仓
        </button>
        <button
          onClick={() => setCurrentTab('funding')}
          className={`p-4 -mb-px border-b ${
            currentTab === 'funding'
              ? 'border-current text-yellow-500'
              : 'border-transparent hover:text-yellow-500'
          }`}
        >
          资金流水
        </button>
      </nav>

      <div className="overflow-x-auto mt-5">
        {isLoggedIn ? (
          <>
            {currentTab === 'transaction' && renderTransactionsTable()}
            {currentTab === 'holding' && renderHoldingsTable()}
            {currentTab === 'funding' && renderFundingsTable()}
          </>
        ) : (
          renderLoginMessage()
        )}
      </div>

      {isLoggedIn && (
        <>
          {currentTab === 'transaction' &&
            renderPagination(
              transactionsPageIndex,
              setTransactionsPageIndex,
              transactionsPageNum,
              setTransactionsData
            )}
          {currentTab === 'holding' &&
            renderPagination(
              holdingsPageIndex,
              setHoldingsPageIndex,
              holdingsPageNum,
              setHoldingsData
            )}
          {currentTab === 'funding' &&
            renderPagination(
              fundingsPageIndex,
              setFundingsPageIndex,
              fundingsPageNum,
              setFundingsData
            )}
        </>
      )}
    </div>
  );
};

export default PortfolioTabs;
