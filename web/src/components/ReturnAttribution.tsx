import React, { useState, useEffect, useRef } from 'react';

// 简化类型定义 - 动态导入ECharts
type EChartsOption = any;

interface ReturnAttributionData {
  annual: {
    year: number;
    contributionPercentage: number;
  }[];
  assets: {
    symbol: string;
    name: string;
    contributionPercentage: number;
  }[];
}

// 市场类型定义
type MarketType = 'CN' | 'US' | 'CRYPTO' | 'DEFAULT';

// 颜色方案配置
interface ColorScheme {
  positive: {
    start: string;
    end: string;
  };
  negative: {
    start: string;
    end: string;
  };
}

// 常量定义
const MAX_ASSETS_TO_DISPLAY = 10;

// SQL结果映射辅助函数
const mapAnnualResults = (results: any[]): Array<{ year: number; contributionPercentage: number }> => {
  return results.map((row) => {
    // 期望列顺序：year, contribution_percentage
    if (!Array.isArray(row) || row.length < 2) {
      console.warn('Invalid annual result row:', row);
      return { year: 0, contributionPercentage: 0 };
    }
    return {
      year: Number(row[0]) || 0,
      contributionPercentage: Number(row[1]) || 0,
    };
  });
};

const mapAssetResults = (results: any[]): Array<{ symbol: string; name: string; contributionPercentage: number }> => {
  return results.map((row) => {
    // 期望列顺序：symbol, name, contribution_percentage
    if (!Array.isArray(row) || row.length < 3) {
      console.warn('Invalid asset result row:', row);
      return { symbol: '', name: '', contributionPercentage: 0 };
    }
    return {
      symbol: String(row[0]) || '',
      name: String(row[1]) || '',
      contributionPercentage: Number(row[2]) || 0,
    };
  });
};

// 计算颜色强度的辅助函数
const calculateIntensity = (value: number, contributions: number[]) => {
  const absValues = contributions.map(v => Math.abs(v));
  const maxAbsValue = Math.max(...absValues);
  
  // 使用一致的最大绝对值进行强度计算
  const intensity = maxAbsValue > 0 ? 0.5 + 0.5 * (Math.abs(value) / maxAbsValue) : 0.5;
  
  return { intensity };
};

// 市场类型映射辅助函数
export const getMarketType = (market?: string): MarketType => {
  const marketLower = market?.toLowerCase();
  if (marketLower === 'china' || marketLower === 'cn') return 'CN';
  if (marketLower === 'us') return 'US';
  if (marketLower === 'crypto') return 'CRYPTO';
  return 'DEFAULT';
};

// 色系映射
const COLOR_SCHEMES: Record<MarketType, ColorScheme> = {
  // A股市场：红涨绿跌
  CN: {
    positive: {
      start: '#f87171',
      end: '#b91c1c'
    },
    negative: {
      start: '#4ade80',
      end: '#15803d'
    }
  },
  // 美股市场：蓝涨红跌
  US: {
    positive: {
      start: '#60a5fa',
      end: '#1e40af'
    },
    negative: {
      start: '#f87171',
      end: '#b91c1c'
    }
  },
  // 加密货币：紫涨橙跌
  CRYPTO: {
    positive: {
      start: '#c084fc',
      end: '#7e22ce'
    },
    negative: {
      start: '#fb923c',
      end: '#c2410c'
    }
  },
  // 默认：蓝涨橙跌 (中性)
  DEFAULT: {
    positive: {
      start: '#60a5fa',
      end: '#1e40af'
    },
    negative: {
      start: '#fb923c',
      end: '#c2410c'
    }
  }
};

// 将HEX颜色转换为RGBA
const hexToRgba = (hex: string, alpha: number) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

interface ReturnAttributionProps {
  sqlDB: any;
  execSafely: (db: any, sql: string) => any[];
  marketType?: MarketType; // 可选市场类型参数
}

const ReturnAttribution: React.FC<ReturnAttributionProps> = ({ 
  sqlDB, 
  execSafely, 
  marketType = 'DEFAULT' // 默认使用中性色系
}) => {
  const [attributionData, setAttributionData] = useState<ReturnAttributionData | null>(null);
  const [loading, setLoading] = useState(true);
  
  const annualChartRef = useRef<HTMLDivElement>(null);
  const assetChartRef = useRef<HTMLDivElement>(null);
  const annualChartInstance = useRef<any>();
  const assetChartInstance = useRef<any>();
  
  // 客户端加载ECharts
  const [echartsLib, setEchartsLib] = useState<any>(null);
  
  // 加载ECharts库
  useEffect(() => {
    // 只在浏览器环境加载ECharts
    if (typeof window !== 'undefined') {
      // 动态导入ECharts
      Promise.all([
        import('echarts/core'),
        import('echarts/charts'),
        import('echarts/components'),
        import('echarts/renderers')
      ]).then(([echarts, { BarChart }, { GridComponent, TooltipComponent }, { CanvasRenderer }]) => {
        // 注册ECharts组件
        echarts.use([GridComponent, TooltipComponent, BarChart, CanvasRenderer]);
        setEchartsLib(echarts);
      }).catch((error) => {
        console.error('Failed to load ECharts modules:', error);
      });
    }
  }, []);

  // 获取数据
  useEffect(() => {
    if (!sqlDB) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      try {
        // 查询年度贡献率
        const annualResults = execSafely(sqlDB, `SELECT year, contribution_percentage FROM return_attribution_annual ORDER BY year`);

        // 查询资产贡献率
        const assetResults = execSafely(sqlDB, `SELECT symbol, name, contribution_percentage FROM return_attribution_assets ORDER BY ABS(contribution_percentage) DESC`);

        // 如果没有数据，不显示错误信息
        if (annualResults.length === 0 && assetResults.length === 0) {
          setLoading(false);
          return;
        }

        const data: ReturnAttributionData = {
          annual: mapAnnualResults(annualResults),
          assets: mapAssetResults(assetResults),
        };

        setAttributionData(data);
      } catch (err) {
        console.error('Error fetching attribution data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    
    // 清理函数
    // eslint-disable-next-line consistent-return
    return () => {
      if (annualChartInstance.current) {
        annualChartInstance.current.dispose();
      }
      if (assetChartInstance.current) {
        assetChartInstance.current.dispose();
      }
    };
  }, [sqlDB, execSafely]);
  
  // 初始化年度贡献图表
  useEffect(() => {
    // 如果没有数据，echarts未加载，或DOM还未挂载，则不初始化
    if (!attributionData?.annual || !annualChartRef.current || !echartsLib) {
      return;
    }
    
    // 如果已有实例，先销毁
    if (annualChartInstance.current) {
      annualChartInstance.current.dispose();
    }
    
    // 创建新实例
    const chart = echartsLib.init(annualChartRef.current);
    annualChartInstance.current = chart;

    const years = attributionData.annual.map(item => item.year);
    const contributions = attributionData.annual.map(item => item.contributionPercentage);

    // 获取当前市场配色方案
    const colorScheme = COLOR_SCHEMES[marketType];
    
    const options: EChartsOption = {
      tooltip: {
        trigger: 'axis',
        formatter(params: any) {
          return `${params[0].name}年<br/>贡献: ${params[0].value.toFixed(2)}%`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: years,
        axisLabel: {
          fontSize: 12,
          fontFamily: 'Arial'
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%',
          fontSize: 12,
          fontFamily: 'Arial'
        }
      },
      series: [
        {
          name: '年度贡献率',
          type: 'bar',
          barWidth: '60%',
          data: contributions.map((value) => {
            // 计算渐变强度
            const { intensity } = calculateIntensity(value, contributions);
            
            return {
              value,
              itemStyle: {
                color: value >= 0
                  ? hexToRgba(colorScheme.positive.end, intensity)
                  : hexToRgba(colorScheme.negative.end, intensity)
              },
              label: {
                show: true,
                position: value >= 0 ? 'top' : 'bottom',
                formatter: (params: any) => `${params.value.toFixed(2)}%`,
                fontSize: 12
              }
            };
          }),
          emphasis: {
            itemStyle: {
              opacity: 0.9
            }
          }
        }
      ],
      markLine: {
        data: [
          {
            yAxis: 0,
            lineStyle: {
              color: '#374151',
              type: 'dashed'
            }
          }
        ]
      }
    };

    chart.setOption(options);

    // 处理窗口大小变化
    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    // eslint-disable-next-line consistent-return
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [attributionData?.annual, marketType, echartsLib]);

  // 初始化资产贡献图表
  useEffect(() => {
    if (!attributionData?.assets || !assetChartRef.current || !echartsLib) {
      return;
    }
    
    if (assetChartInstance.current) {
      assetChartInstance.current.dispose();
    }

    // 创建新实例
    const chart = echartsLib.init(assetChartRef.current);
    assetChartInstance.current = chart;
    
    // 获取当前市场配色方案
    const colorScheme = COLOR_SCHEMES[marketType];
    
    // 按贡献的绝对值降序排序，并限制显示数量
    const limitedAssets = [...attributionData.assets].slice(0, MAX_ASSETS_TO_DISPLAY);
    const symbols = limitedAssets.map(item => item.symbol);
    const contributions = limitedAssets.map(item => item.contributionPercentage);
    
    const options: EChartsOption = {
      tooltip: {
        trigger: 'axis',
        formatter(params: any) {
          const asset = attributionData.assets.find((a: any) => a.symbol === params[0].name);
          return `${params[0].name} ${asset?.name || ''}<br/>贡献: ${params[0].value.toFixed(2)}%`;
        }
      },
      grid: {
        left: '15%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}%',
          fontSize: 12,
          fontFamily: 'Arial'
        }
      },
      yAxis: {
        type: 'category',
        data: symbols.reverse(),
        axisLabel: {
          fontSize: 12,
          fontFamily: 'Arial'
        }
      },
      series: [
        {
          name: '资产贡献',
          type: 'bar',
          barWidth: '60%',
          data: contributions.map((value: number) => {
            const { intensity } = calculateIntensity(value, contributions);
            
            return {
              value,
              itemStyle: {
                color: value >= 0
                  ? hexToRgba(colorScheme.positive.end, intensity)
                  : hexToRgba(colorScheme.negative.end, intensity)
              },
              label: {
                show: true,
                position: 'right',
                formatter: (params: any) => `${params.value.toFixed(2)}%`,
                fontSize: 12
              }
            };
          }),
          emphasis: {
            itemStyle: {
              opacity: 0.9
            }
          }
        }
      ]
    };

    chart.setOption(options);

    // 处理窗口大小变化
    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    // eslint-disable-next-line consistent-return
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [attributionData?.assets, marketType, echartsLib]);

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold text-main mb-4">收益归因分析</h2>
      
      {loading && <div className="text-center py-8">加载中...</div>}
      
      {!loading && (!attributionData || (attributionData.annual.length === 0 && attributionData.assets.length === 0)) && (
        <div className="text-center py-8">暂无归因数据</div>
      )}
      
      {!loading && attributionData && (attributionData.annual.length > 0 || attributionData.assets.length > 0) && (
        <>
          {/* 年度归因分析图表 */}
          {attributionData.annual.length > 0 && (
            <div className="mb-6">
              <h3 className="text-xl font-bold text-main mb-2">年度收益贡献</h3>
              <div
                ref={annualChartRef}
                className="w-full h-64 bg-gray-50 rounded-lg"
                style={{ minHeight: '280px' }}
              ></div>
            </div>
          )}
          
          {/* 资产贡献分析图表 */}
          {attributionData.assets.length > 0 && (
            <div className="mb-4">
              <h3 className="text-xl font-bold text-main mb-2">资产收益贡献</h3>
              <div
                ref={assetChartRef}
                className="w-full h-64 bg-gray-50 rounded-lg"
                style={{ minHeight: '280px' }}
              ></div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ReturnAttribution;
