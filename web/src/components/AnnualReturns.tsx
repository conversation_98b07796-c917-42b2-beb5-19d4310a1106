import React from 'react';

import { motion } from 'framer-motion';

interface AnnualReturnsProps {
  annualReturns: string | undefined;
}

const AnnualReturns: React.FC<AnnualReturnsProps> = ({ annualReturns }) => {
  const returns = annualReturns ? JSON.parse(annualReturns) : {};

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
    },
  };

  return (
    <motion.div
      className="sm:w-9/12 w-11/12 mb-10"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <h3 className="text-2xl font-bold text-main mb-4">历年表现</h3>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {Object.entries(returns).map(([year, return_value]) => (
          <motion.div
            key={year}
            className="p-4 border rounded-lg shadow-md"
            variants={itemVariants}
            whileHover={{
              scale: 1.05,
              boxShadow: '0px 5px 15px rgba(0,0,0,0.1)',
              transition: { duration: 0.3 },
            }}
          >
            <h4 className="text-lg font-semibold">{year}年</h4>
            <p
              className={`text-xl font-bold ${
                Number(return_value) >= 0 ? 'text-red-600' : 'text-green-600'
              }`}
            >
              {(Number(return_value) * 100).toFixed(2)}%
            </p>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default AnnualReturns;
