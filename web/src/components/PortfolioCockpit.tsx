import React, { useState } from 'react';

import { motion } from 'framer-motion';
import { Info, TrendingUp, AlertTriangle, Award, Clock } from 'lucide-react';

import { PerformanceData, BenchmarkData } from '../../types/types';

interface PortfolioCockpitProps {
  netValue: string | undefined;
  performance: PerformanceData | undefined;
  benchmark: BenchmarkData | undefined;
  firstFundingDate: string | undefined;
  latestTradeDate: string | undefined;
}

const InstrumentPanel: React.FC<{
  title: string;
  value: string | number;
  description: string;
  color: string;
  icon: React.ReactNode;
  benchmarkValue?: number;
  rawValue?: number;
  unit?: string;
  isPercentage?: boolean;
}> = ({ 
  title, 
  value, 
  description, 
  color, 
  icon, 
  benchmarkValue,
  rawValue,
  unit = '%',
  isPercentage = true
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const formatBenchmarkValue = (val: number) => 
    isPercentage ? `${(val * 100).toFixed(2)}${unit}` : `${val.toFixed(2)}`;

  const calculateDifference = () => {
    if (benchmarkValue === undefined) return null;
    
    let numericValue;
    if (rawValue !== undefined) {
      numericValue = rawValue;
    } else if (typeof value === 'number') {
      numericValue = value;
    } else {
      return null;
    }
    
    const diff = numericValue - benchmarkValue;
    const formatted = isPercentage ? `${(diff * 100).toFixed(2)}${unit}` : `${diff.toFixed(2)}`;
    const sign = diff > 0 ? '+' : '';
    return `${sign}${formatted}`;
  };

  return (
    <motion.div
      className={`relative p-6 m-2 rounded-lg shadow-xl ${color}`}
      whileHover={{ scale: 1.05 }}
      transition={{ type: 'spring', stiffness: 300 }}
    >
      <div className="flex items-center mb-2">
        {icon}
        <div className="ml-2">
          <h5 className="text-2xl font-bold">{value}</h5>
          {benchmarkValue !== undefined && (
            <div className="text-xs text-gray-600 mt-1">
              <div>基准: {formatBenchmarkValue(benchmarkValue)}</div>
              <div className="font-bold text-gray-700">
                超额: {calculateDifference()}
              </div>
            </div>
          )}
        </div>
      </div>
      <p className="text-sm font-medium text-gray-600">{title}</p>
      <Info
        className="absolute top-2 right-2 text-gray-400 cursor-pointer"
        size={18}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      />
      {showTooltip && (
        <div className="absolute z-10 p-2 text-sm text-white bg-gray-800 rounded-md shadow-lg -top-2 right-8 max-w-xs">
          {description}
        </div>
      )}
    </motion.div>
  );
};

const PortfolioCockpit: React.FC<PortfolioCockpitProps> = ({
  netValue,
  performance,
  benchmark,
  firstFundingDate,
  latestTradeDate,
}) => {
  return (
    <div className="w-full max-w-6xl mx-auto p-6 bg-gray-100 rounded-xl shadow-lg border border-gray-200 mb-10">
      <h2 className="text-2xl font-bold mb-6 text-center text-main">
        投资组合驾驶舱
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div>
          <h3 className="text-xl font-semibold mb-4 flex items-center text-yellow-600">
            <TrendingUp className="mr-2" /> 核心仪表
          </h3>
          <div className="space-y-4">
            <InstrumentPanel
              title="最新净值"
              value={netValue ?? 'N/A'}
              description="投资组合当前的净值"
              color="bg-yellow-100"
              icon={<TrendingUp className="text-yellow-500" />}
            />
            {performance?.xirr ? (
              <InstrumentPanel
                title="内部收益率"
                value={`${(performance.xirr * 100).toFixed(2)}%`}
                description="考虑现金流时间价值的年化收益率指标"
                color="bg-yellow-100"
                icon={<TrendingUp className="text-yellow-500" />}
              />
            ) : (
              <InstrumentPanel
                title="年复合收益率"
                value={
                  performance
                    ? `${(performance.cagr * 100).toFixed(2)}%`
                    : 'N/A'
                }
                description="反映长期投资回报的年复合收益率，与等比例买入持有基准对比"
                color="bg-yellow-100"
                icon={<TrendingUp className="text-yellow-500" />}
                benchmarkValue={benchmark?.cagr}
                rawValue={performance?.cagr}
              />
            )}
            <InstrumentPanel
              title="胜率"
              value={
                performance
                  ? `${(performance.winRate * 100).toFixed(2)}%`
                  : 'N/A'
              }
              description="盈利交易在总交易中的比例"
              color="bg-yellow-100"
              icon={<TrendingUp className="text-yellow-500" />}
            />
            <InstrumentPanel
              title="盈亏比"
              value={performance ? performance.profitFactor.toFixed(2) : 'N/A'}
              description="平均盈利与平均亏损的比率"
              color="bg-yellow-100"
              icon={<TrendingUp className="text-yellow-500" />}
            />
          </div>
        </div>

        <div>
          <h3 className="text-xl font-semibold mb-4 flex items-center text-red-600">
            <AlertTriangle className="mr-2" /> 风险雷达
          </h3>
          <div className="space-y-4">
            <InstrumentPanel
              title="夏普比率"
              value={performance ? performance.sharpeRatio.toFixed(2) : 'N/A'}
              description="反映风险调整后的收益能力，与等比例买入持有基准对比"
              color="bg-red-100"
              icon={<AlertTriangle className="text-red-500" />}
              benchmarkValue={benchmark?.sharpeRatio}
              rawValue={performance?.sharpeRatio}
              isPercentage={false}
              unit=""
            />
            <InstrumentPanel
              title="当前回撤"
              value={
                performance
                  ? `${(performance.currentDrawdown * 100).toFixed(2)}%`
                  : 'N/A'
              }
              description="当前投资组合的亏损幅度"
              color="bg-red-100"
              icon={<AlertTriangle className="text-red-500" />}
            />
            <InstrumentPanel
              title="最大回撤"
              value={
                performance
                  ? `${(Math.abs(performance.maxDrawdown) * 100).toFixed(2)}%`
                  : 'N/A'
              }
              description="历史最大亏损幅度，与等比例买入持有基准对比（负值表示风险控制更好）"
              color="bg-red-100"
              icon={<AlertTriangle className="text-red-500" />}
              benchmarkValue={benchmark ? Math.abs(benchmark.maxDrawdown) : undefined}
              rawValue={performance ? Math.abs(performance.maxDrawdown) : undefined}
            />
            <InstrumentPanel
              title="最大回撤时间"
              value={
                performance ? `${performance.maxDrawdownDurationDays}天` : 'N/A'
              }
              description="历史最大回撤持续的天数"
              color="bg-red-100"
              icon={<AlertTriangle className="text-red-500" />}
            />
          </div>
        </div>

        <div>
          <h3 className="text-xl font-semibold mb-4 flex items-center text-blue-600">
            <Award className="mr-2" /> 性能指标
          </h3>
          <div className="space-y-4">
            {performance?.volatility && (
              <InstrumentPanel
                title="波动率"
                value={`${(performance.volatility * 100).toFixed(2)}%`}
                description="年化波动率，衡量投资组合价格波动的程度，与等比例买入持有基准对比"
                color="bg-blue-100"
                icon={<Award className="text-blue-500" />}
                benchmarkValue={benchmark?.volatility}
                rawValue={performance?.volatility}
              />
            )}
            <InstrumentPanel
              title="SQN"
              value={performance ? performance.sqn.toFixed(2) : 'N/A'}
              description="系统质量指数，衡量交易系统的整体质量"
              color="bg-blue-100"
              icon={<Award className="text-blue-500" />}
            />
            <InstrumentPanel
              title="VWR"
              value={performance ? performance.vwr.toFixed(2) : 'N/A'}
              description="波动率加权回报，考虑波动率后的收益率指标"
              color="bg-blue-100"
              icon={<Award className="text-blue-500" />}
            />
            <InstrumentPanel
              title="总交易次数"
              value={performance ? performance.totalTrades : 'N/A'}
              description="策略执行的总交易次数"
              color="bg-blue-100"
              icon={<Award className="text-blue-500" />}
            />
          </div>
        </div>

        <div>
          <h3 className="text-xl font-semibold mb-4 flex items-center text-green-600">
            <Clock className="mr-2" /> 航行日志
          </h3>
          <div className="space-y-4">
            <InstrumentPanel
              title="运行天数"
              value={performance ? performance.runningDays : 'N/A'}
              description="策略运行的总天数"
              color="bg-green-100"
              icon={<Clock className="text-green-500" />}
            />
            <InstrumentPanel
              title="建仓日期"
              value={firstFundingDate ?? 'N/A'}
              description="投资组合开始运作的日期"
              color="bg-green-100"
              icon={<Clock className="text-green-500" />}
            />
            <InstrumentPanel
              title="更新日期"
              value={latestTradeDate ?? 'N/A'}
              description="数据最后更新的日期"
              color="bg-green-100"
              icon={<Clock className="text-green-500" />}
            />
          </div>
        </div>
      </div>

      {benchmark && (
        <div className="mt-6 text-center text-xs text-gray-500">
          基准策略说明：等比例买入持有策略是指在初始时等比例买入所有标的资产并持有到期，用作衡量主动投资策略表现的基准。
          基准数据包括年复合收益率 {(benchmark.cagr * 100).toFixed(2)}%、
          夏普比率 {benchmark.sharpeRatio.toFixed(2)}、
          最大回撤 {(benchmark.maxDrawdown * 100).toFixed(2)}%
          {benchmark.volatility && `、波动率 ${(benchmark.volatility * 100).toFixed(2)}%`}。
        </div>
      )}
    </div>
  );
};

export default PortfolioCockpit;
