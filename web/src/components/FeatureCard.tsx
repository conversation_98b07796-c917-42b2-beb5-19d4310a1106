interface FeatureCardProps {
  title: string;
  description: string;
  videoUrl: string;
}

const FeatureCard = ({ title, description, videoUrl }: FeatureCardProps) => (
  <div className="bg-white rounded-lg shadow-md p-6">
    <h3 className="text-xl font-semibold mb-2">{title}</h3>
    <video className="w-full mb-4 rounded" autoPlay loop muted playsInline>
      <source src={videoUrl} type="video/mp4" />
      Your browser does not support the video tag.
    </video>
    <p>{description}</p>
  </div>
);

export { FeatureCard };
