import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useRouter } from 'next/router';
import initSqlJs, { Database } from 'sql.js';
import { getUniversalSignalQueries } from '../utils/signalAnalysisQueries';
import { trackEvent } from '../utils/trackEvent';
import PortfolioSignalsChart from './PortfolioSignalsChart';
import { getPublicPortfolioInfo, getPortfolioSignalDatabase, getSignalAnalysisUrl } from '../utils/api';

interface AnalysisResults {
  title: string;
  summary: string;
  data: any[];
}

interface HealthStatus {
  dataHealth: string;
  signalEffectiveness: string;
  activityLevel: string;
  overallRating: string;
}

const analysisModules = [
  {
    id: 'signal_health_check',
    title: '🏥 数据健康检查',
    description: '验证信号数据的完整性和有效性',
    priority: 'high'
  },
  {
    id: 'signal_effectiveness',
    title: '🎯 信号有效性分析',
    description: '评估买卖信号的预测准确性和盈利能力',
    priority: 'high'
  },
  {
    id: 'trading_frequency',
    title: '⏰ 交易时机分析',
    description: '了解策略的交易频率和节奏特征',
    priority: 'medium'
  },
  {
    id: 'market_adaptation',
    title: '🌍 市场适应性分析',
    description: '策略在不同市场环境下的表现评估',
    priority: 'medium'
  },
  {
    id: 'signal_transition_logic',
    title: '🔄 信号切换逻辑检查',
    description: '验证信号状态转换的合理性',
    priority: 'low'
  },
  {
    id: 'risk_signals',
    title: '⚠️ 风险信号识别',
    description: '识别策略中的潜在风险因素',
    priority: 'high'
  },
  {
    id: 'signal_distribution',
    title: '📊 信号分布分析',
    description: '分析信号类型分布和策略风格',
    priority: 'low'
  },
  {
    id: 'volatility_analysis',
    title: '📈 波动性分析',
    description: '分析各标的价格波动性，识别高风险资产和杠杆特征',
    priority: 'high'
  },
];

// 活跃度评估常量
const ACTIVITY_THRESHOLDS = {
  HIGH_FREQUENCY_TRADES_PER_MONTH: 20,
  MEDIUM_FREQUENCY_TRADES_PER_MONTH: 10,
  HIGH_SWITCH_RATE: 0.3,
  MEDIUM_SWITCH_RATE: 0.15,
  DATA_HEALTH: {
    GOOD_THRESHOLD: 1000,
    FAIR_THRESHOLD: 100,
  },
  SIGNAL_EFFECTIVENESS: {
    EXCELLENT_THRESHOLD: 0.95,
    FAIR_THRESHOLD: 0.8,
  },
} as const;

const PortfolioSignals: React.FC = () => {
  const router = useRouter();
  const { slug } = router.query;

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [database, setDatabase] = useState<Database | null>(null);
  const [portfolioName, setPortfolioName] = useState<string>('');
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(true);

  // 新增状态变量
  const [activeModule, setActiveModule] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResults | null>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [isOpeningOnlineSQL, setIsOpeningOnlineSQL] = useState<boolean>(false);

  // 修复：添加ref用于可靠的滚动
  const resultsRef = useRef<HTMLDivElement>(null);
  const errorRef = useRef<HTMLDivElement>(null);

  const portfolioSlug = Array.isArray(slug) ? slug[0] : slug || '';

  // 缓存查询配置，避免重复调用
  const queryConfigs = useMemo(() => getUniversalSignalQueries(), []);

  // 修复：使用useEffect替代setTimeout进行滚动
  useEffect(() => {
    if (analysisResults && resultsRef.current) {
      resultsRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [analysisResults]);

  useEffect(() => {
    if (analysisError && errorRef.current) {
      errorRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [analysisError]);

  // 安全的Markdown渲染组件 - 替代dangerouslySetInnerHTML
  const SafeMarkdownRenderer: React.FC<{ text: string }> = ({ text }) => {
    const processedLines = useMemo(() => {
      return text.split('\n').map((line, index) => {
        // 修复：优先处理列表项，再处理其中的粗体文本
        if (line.startsWith('- ')) {
          const content = line.slice(2);
          const parts = content.split(/(\*\*.*?\*\*)/g);
          return (
            <div key={index} className="mb-1 flex items-start">
              <span className="mr-2">•</span>
              <span>
                {parts.map((part, partIndex) => {
                  if (part.startsWith('**') && part.endsWith('**')) {
                    return <strong key={partIndex}>{part.slice(2, -2)}</strong>;
                  }
                  return <span key={partIndex}>{part}</span>;
                })}
              </span>
            </div>
          );
        }

        // 处理粗体文本 **text**
        if (line.includes('**')) {
          const parts = line.split(/(\*\*.*?\*\*)/g);
          return (
            <div key={index} className="mb-1">
              {parts.map((part, partIndex) => {
                if (part.startsWith('**') && part.endsWith('**')) {
                  return <strong key={partIndex}>{part.slice(2, -2)}</strong>;
                }
                return <span key={partIndex}>{part}</span>;
              })}
            </div>
          );
        }
        
        // 普通文本
        return line.trim() ? (
          <div key={index} className="mb-1">
            {line}
          </div>
        ) : (
          <div key={index} className="h-2" />
        );
      });
    }, [text]);

    return <div className="text-sm text-gray-700 whitespace-pre-line prose prose-sm max-w-none">{processedLines}</div>;
  };

  // 执行健康检查
  const performHealthCheck = useCallback((db: Database) => {
    try {
      // 基础数据检查
      const totalRecordsStmt = db.prepare('SELECT COUNT(*) FROM trade_signals');
      totalRecordsStmt.step();
      const totalRecordsResult = totalRecordsStmt.getAsObject();
      const totalRecords = Number(totalRecordsResult['COUNT(*)']) || 0;
      totalRecordsStmt.free();

      // 信号有效性检查
      const validSignalsStmt = db.prepare('SELECT COUNT(*) FROM trade_signals WHERE signal IN ("B", "S", "H", "E")');
      validSignalsStmt.step();
      const validSignalsResult = validSignalsStmt.getAsObject();
      const validSignals = Number(validSignalsResult['COUNT(*)']) || 0;
      validSignalsStmt.free();

      // 计算健康度
      let dataHealth: string;
      if (totalRecords > ACTIVITY_THRESHOLDS.DATA_HEALTH.GOOD_THRESHOLD) {
        dataHealth = '✅ 良好';
      } else if (totalRecords > ACTIVITY_THRESHOLDS.DATA_HEALTH.FAIR_THRESHOLD) {
        dataHealth = '⚠️ 一般';
      } else {
        dataHealth = '❌ 不足';
      }

      let signalEffectiveness: string;
      const effectiveness = totalRecords > 0 ? validSignals / totalRecords : 0;
      if (effectiveness > ACTIVITY_THRESHOLDS.SIGNAL_EFFECTIVENESS.EXCELLENT_THRESHOLD) {
        signalEffectiveness = '✅ 优秀';
      } else if (effectiveness > ACTIVITY_THRESHOLDS.SIGNAL_EFFECTIVENESS.FAIR_THRESHOLD) {
        signalEffectiveness = '⚠️ 一般';
      } else {
        signalEffectiveness = '❌ 较差';
      }

      // 风险评估：基于信号分布和交易频率
      let activityLevel: string;
      try {
        // 计算交易频率（每月平均交易次数）
        const tradingFreqStmt = db.prepare(`
          SELECT 
            COUNT(*) as total_trades,
            (julianday(MAX(date)) - julianday(MIN(date))) / 30.0 as months
          FROM trade_signals 
          WHERE signal IN ("B", "S")
        `);
        tradingFreqStmt.step();
        const freqResult = tradingFreqStmt.getAsObject();
        const totalTrades = Number(freqResult.total_trades) || 0;
        const months = Number(freqResult.months) || 1;
        const tradesPerMonth = totalTrades / months;
        tradingFreqStmt.free();

        // 计算信号切换频率（评估策略稳定性）
        const switchFreqStmt = db.prepare(`
          SELECT COUNT(*) as switches
          FROM (
            SELECT date, signal, 
                   LAG(signal) OVER (PARTITION BY symbol ORDER BY date) as prev_signal
            FROM trade_signals 
            WHERE signal IN ("B", "S", "H", "E")
          ) 
          WHERE signal != prev_signal AND prev_signal IS NOT NULL
        `);
        switchFreqStmt.step();
        const switchResult = switchFreqStmt.getAsObject();
        const switches = Number(switchResult.switches) || 0;
        switchFreqStmt.free();

        // 活跃度评估逻辑 - 使用常量和除零保护
        const switchRate = totalRecords > 0 ? switches / totalRecords : 0;
        if (tradesPerMonth > ACTIVITY_THRESHOLDS.HIGH_FREQUENCY_TRADES_PER_MONTH || switchRate > ACTIVITY_THRESHOLDS.HIGH_SWITCH_RATE) {
          activityLevel = '🔴 较高';
        } else if (tradesPerMonth > ACTIVITY_THRESHOLDS.MEDIUM_FREQUENCY_TRADES_PER_MONTH || switchRate > ACTIVITY_THRESHOLDS.MEDIUM_SWITCH_RATE) {
          activityLevel = '🟡 中等';
        } else {
          activityLevel = '🟢 较低';
        }
      } catch (error) {
        console.error('Risk assessment failed:', error);
        activityLevel = '📊 评估失败';
      }

      // 修复：更准确的整体评级逻辑
      const ratings = [dataHealth, signalEffectiveness, activityLevel];
      let overallRating: string;
      if (ratings.some(r => r.includes('❌'))) {
        overallRating = '⚠️ 需关注';
      } else if (ratings.every(r => r.includes('✅') || r.includes('🟢'))) {
        overallRating = '🌟 优秀';
      } else {
        overallRating = '⚖️ 良好';
      }

      setHealthStatus({
        dataHealth,
        signalEffectiveness,
        activityLevel,
        overallRating
      });
    } catch (error) {
      console.error('健康检查失败:', error);
      setHealthStatus({
        dataHealth: '❌ 检查失败',
        signalEffectiveness: '❌ 检查失败',
        activityLevel: '❌ 检查失败',
        overallRating: '❌ 检查失败'
      });
    }
  }, []);

  const loadDatabase = useCallback(async () => {
    // 等待路由准备就绪
    if (!router.isReady) {
      return;
    }
    
    if (!slug || typeof slug !== 'string') {
      setError('Invalid portfolio symbol');
      setLoading(false);
      return;
    }

    try {
      const portfolioInfo = await getPublicPortfolioInfo(slug);
      setPortfolioName(portfolioInfo.name);

      const arrayBuffer = await getPortfolioSignalDatabase(slug);

      const SQL = await initSqlJs({
        locateFile: (file) =>
          `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`,
      });

      const db = new SQL.Database(new Uint8Array(arrayBuffer));
      setDatabase(db);
      
      // 执行基础健康检查
      performHealthCheck(db);
      
      setLoading(false);
    } catch (err) {
      if (err instanceof Error && err.message.includes('401')) {
        setIsLoggedIn(false);
      } else {
        setError(
          `Failed to load signal database: ${
            err instanceof Error ? err.message : String(err)
          }`
        );
      }
      setLoading(false);
    }
  }, [slug, router.isReady]);

  useEffect(() => {
    loadDatabase();
  }, [loadDatabase]);

  useEffect(() => {
    const trackPageView = async () => {
      await trackEvent('UserPortfolioStrategySignals', {});
    };
    trackPageView();
  }, []);

  // 处理分析模块点击
  const handleAnalysisModuleClick = async (moduleId: string) => {
    if (isAnalyzing || !database) return;
    
    setIsAnalyzing(true);
    setActiveModule(moduleId);
    setAnalysisError(null);
    
    try {
      const queryConfig = queryConfigs[moduleId as keyof typeof queryConfigs];
      
      if (!queryConfig) {
        throw new Error(`未找到分析模块: ${moduleId}`);
      }
      
      const stmt = database.prepare(queryConfig.sql);
      const results = [];
      while (stmt.step()) {
        results.push(stmt.getAsObject());
      }
      stmt.free();
      
      setAnalysisResults({
        title: queryConfig.title,
        summary: queryConfig.description,
        data: results
      });
      
    } catch (err) {
      console.error('分析执行失败:', err);
      setAnalysisError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 获取状态颜色
  const getStatusColor = (value: string): string => {
    if (value.includes('✅') || value.includes('🌟')) return 'text-green-600 font-medium';
    if (value.includes('⚠️') || value.includes('🟡')) return 'text-yellow-600 font-medium';
    if (value.includes('❌') || value.includes('🔴')) return 'text-red-600 font-medium';
    if (value.includes('📊') || value.includes('🔵')) return 'text-blue-600 font-medium';
    return '';
  };

  // 获取优先级样式
  const getPriorityStyles = (priority: string) => {
    switch (priority) {
      case 'high':
        return {
          border: 'border-red-200 hover:border-red-300',
          bg: 'bg-red-50 border-red-200',
          badge: 'bg-red-100 text-red-700',
          button: 'border-red-300 text-red-700 hover:bg-red-50',
          label: '🔴 关键'
        };
      case 'medium':
        return {
          border: 'border-yellow-200 hover:border-yellow-300',
          bg: 'bg-yellow-50 border-yellow-200',
          badge: 'bg-yellow-100 text-yellow-700',
          button: 'border-yellow-300 text-yellow-700 hover:bg-yellow-50',
          label: '🟡 重要'
        };
      default:
        return {
          border: 'border-gray-200 hover:border-gray-300',
          bg: 'bg-gray-50 border-gray-200',
          badge: 'bg-gray-100 text-gray-700',
          button: 'border-gray-300 text-gray-700 hover:bg-gray-50',
          label: '🔵 辅助'
        };
    }
  };

  // 获取分析建议
  const getAnalysisInsight = (moduleId: string): string => {
    const insights: Record<string, string> = {
      signal_health_check: '确保数据质量是策略分析的基础。关注无效信号和数据缺失问题。',
      signal_effectiveness: '成功率超过60%表示策略具有一定预测能力，但需结合风险考虑。',
      trading_frequency: '高频交易可能增加成本，低频交易可能错失机会，需要平衡。',
      market_adaptation: '优秀策略应在不同市场环境下都有相对稳定的表现。',
      signal_transition_logic: '异常的信号切换可能表明策略逻辑存在问题或市场突发事件。',
      risk_signals: '及时识别和处理风险信号对策略的长期成功至关重要。',
      signal_distribution: '了解策略的交易风格有助于设定合理的期望和风险管理。',
      volatility_analysis: '高波动性通常意味着高风险高收益，杠杆ETF和成长股常表现出较高波动性。'
    };
    return insights[moduleId] || '深入分析有助于更好地理解和优化策略表现。';
  };

  // 处理在线SQL分析
  const handleOnlineSQLAnalysis = async () => {
    if (!portfolioSlug) return;
    
    setIsOpeningOnlineSQL(true);
    setAnalysisError(null);
    
    try {
      const { presignedUrl } = await getSignalAnalysisUrl(portfolioSlug);

      // 为数据库添加简化说明文档
      const metadataObject = {
        title: `📊 ${portfolioSlug} 投资组合信号分析`,
        databases: {
          [portfolioSlug]: {
            title: `${portfolioSlug} 信号数据库`,
            description_html: `
                <h3>🔍 信号数据分析</h3>
                <p>主数据表: <code>trade_signals</code> | 信号类型: B(买入) S(卖出) H(持有) E(空仓)</p>
                <p><a href="https://docs.myinvestpilot.com/docs/primitives/advanced/troubleshooting" target="_blank">📖 查看完整分析手册 →</a></p>
              `,
            tables: {
              trade_signals: {
                title: '📈 交易信号表',
                description: '',
              },
            },
          },
        },
      };

      const metadataJson = JSON.stringify(metadataObject);
      const metadataDataUri = `data:application/json;charset=utf-8,${encodeURIComponent(
        metadataJson
      )}`;

      // 直接进入指定数据库页面
      const finalUrl = `https://lite.datasette.io/?url=${encodeURIComponent(
        presignedUrl
      )}&metadata=${encodeURIComponent(metadataDataUri)}#/${portfolioSlug}`;
      window.open(finalUrl, '_blank');
      
    } catch (err) {
      console.error('Failed to open online SQL analysis:', err);
      const errorMsg = err instanceof Error && err.message.includes('401') 
        ? '需要登录才能使用在线SQL分析功能。请先登录您的账户。'
        : '无法打开在线SQL分析工具，请稍后重试。(请确保你有该组合的权限，只有组合的创建者可以查看该组合的信号数据)';
      setAnalysisError(errorMsg);
    } finally {
      setIsOpeningOnlineSQL(false);
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center mb-2">
            <span className="text-yellow-600 mr-2">⚠️</span>
            <h2 className="text-lg font-semibold text-yellow-800">需要登录</h2>
          </div>
          <p className="text-yellow-700">请登录后查看策略信号分析。</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载策略分析数据...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center mb-2">
            <span className="text-red-600 mr-2">❌</span>
            <h2 className="text-lg font-semibold text-red-800">加载失败</h2>
          </div>
          <p className="text-red-700">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和描述 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 md:p-6">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0 lg:space-x-6">
          {/* 左侧：标题和描述 */}
          <div className="flex-1">
            <h1 className="text-xl md:text-2xl font-bold text-gray-900 mb-3 flex items-center">
              <span className="mr-2 md:mr-3 text-2xl md:text-3xl">🔬</span>
              <span className="leading-tight">策略信号深度分析 - {portfolioName}</span>
            </h1>
            <p className="text-sm md:text-base text-gray-600 leading-relaxed">
              运用高级SQL分析技术，全方位评估投资策略的交易信号质量、市场适应性与风险特征。
            </p>
          </div>
          
          {/* 右侧：在线SQL分析工具 */}
          <div className="flex-shrink-0 lg:w-72">
            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-4 shadow-sm">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <span className="text-purple-600 text-lg">🛠️</span>
                </div>
                <div>
                  <div className="text-sm font-semibold text-purple-800">专业分析工具</div>
                  <div className="text-xs text-purple-600">SQLite在线环境</div>
                </div>
              </div>
              
              <button
                onClick={handleOnlineSQLAnalysis}
                disabled={isOpeningOnlineSQL || !portfolioSlug}
                className="w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
              >
                {isOpeningOnlineSQL ? (
                  <>
                    <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>启动中...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    <span>在线SQL分析</span>
                  </>
                )}
              </button>
              
              <div className="mt-3 text-xs text-purple-600 text-center leading-relaxed">
                💡 支持自定义查询、可视化图表、数据导出
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 分析错误展示 - 移至顶部 */}
      {analysisError && (
        <div ref={errorRef} className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center mb-2">
            <span className="text-red-600 mr-2">❌</span>
            <h3 className="text-lg font-semibold text-red-800">操作失败</h3>
          </div>
          <p className="text-red-700 mb-4">{analysisError}</p>
          <div className="flex space-x-3">
            <button
              onClick={() => setAnalysisError(null)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              关闭
            </button>
            {analysisError.includes('登录') && (
              <a
                href="/api/auth/login"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                前往登录
              </a>
            )}
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              刷新页面
            </button>
          </div>
        </div>
      )}

      {/* 价格图表部分 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200 p-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <span className="mr-2">📈</span>
            历史价格走势与交易信号
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            可视化展示策略的历史交易信号分布，点击图例可以控制显示/隐藏特定资产
          </p>
        </div>
        <div className="p-4">
          <PortfolioSignalsChart 
            showSignals={true}
            showNormalization={true}
            signalDatabase={database}
          />
        </div>
      </div>

      {/* 策略健康评估总览 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200 p-4 md:p-6">
          <h2 className="text-lg md:text-xl font-semibold text-gray-900 flex items-center">
            <span className="mr-2">💊</span>
            策略健康度总览
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            快速了解策略的整体健康状况和关键指标（鼠标悬停查看详细说明）
          </p>
        </div>
        <div className="p-4 md:p-6">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
            {[
              { 
                label: '数据完整性', 
                value: healthStatus?.dataHealth || '检查中...', 
                color: 'blue',
                tooltip: '基于信号数据量评估：>1000条记录为良好，100-1000条为一般，<100条为不足。数据量越大，分析结果越可靠。'
              },
              { 
                label: '信号有效性', 
                value: healthStatus?.signalEffectiveness || '检查中...', 
                color: 'green',
                tooltip: '检查信号格式的规范性：计算有效信号(B买入/S卖出/H持有/E空仓)占总信号的比例。>95%为优秀，80-95%为一般，<80%为较差。'
              },
              { 
                label: '交易活跃度', 
                value: healthStatus?.activityLevel || '评估中...', 
                color: 'yellow',
                tooltip: '基于交易频率和信号切换评估：🟢较低(<10次/月,切换率<15%) 🟡中等(10-20次/月,切换率15-30%) 🔴较高(>20次/月,切换率>30%)。注意：这不是投资风险评估，仅反映策略活跃程度。'
              },
              { 
                label: '综合评级', 
                value: healthStatus?.overallRating || '计算中...', 
                color: 'purple',
                tooltip: '综合数据完整性、信号有效性和交易活跃度的整体评估。🌟优秀：所有指标都达到最佳状态；⚖️良好：部分指标需要关注。'
              }
            ].map(({ label, value, color, tooltip }) => {
              // 修复：使用静态类名映射避免Tailwind JIT编译问题
              const colorStyles = {
                blue: {
                  container: 'bg-blue-50 border-blue-200',
                  text: 'text-blue-700',
                },
                green: {
                  container: 'bg-green-50 border-green-200',
                  text: 'text-green-700',
                },
                yellow: {
                  container: 'bg-yellow-50 border-yellow-200',
                  text: 'text-yellow-700',
                },
                purple: {
                  container: 'bg-purple-50 border-purple-200',
                  text: 'text-purple-700',
                },
              };
              
              const styles = colorStyles[color as keyof typeof colorStyles] || { container: 'bg-gray-50 border-gray-200', text: 'text-gray-700' };
              
              return (
              <div key={label} className={`${styles.container} rounded-lg p-3 md:p-4 text-center lg:text-left relative group`}>
                <div className="text-xs md:text-sm text-gray-600 mb-1 font-medium flex items-center justify-center lg:justify-start">
                  {label}
                  <span className="ml-1 text-gray-400 cursor-help">ℹ️</span>
                </div>
                <div className={`text-sm md:text-lg font-semibold ${styles.text} leading-tight`}>{value}</div>
                
                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-64 text-left">
                  <div className="whitespace-normal leading-relaxed">{tooltip}</div>
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                </div>
              </div>
              );
            })}
          </div>
          
          {/* 重要提示 */}
          <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <span className="text-amber-600 text-lg flex-shrink-0">⚠️</span>
              <div className="text-sm text-amber-800">
                <div className="font-semibold mb-2">重要说明</div>
                <div className="space-y-1 text-amber-700">
                  <p>• <strong>数据完整性</strong>：仅反映信号数据的数量，不代表数据质量</p>
                  <p>• <strong>信号有效性</strong>：检查信号格式规范性，不评估信号准确性</p>
                  <p>• <strong>交易活跃度</strong>：衡量策略交易频率，不等同于投资风险评估</p>
                  <p>• <strong>综合评级</strong>：基于以上技术指标，不构成投资建议</p>
                </div>
                <div className="mt-3 text-xs text-amber-600 font-medium">
                  💡 如需真实的投资风险评估，请结合市场波动率、最大回撤、夏普比率等财务指标进行综合分析
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 分析模块网格 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {analysisModules.map((module) => {
          const styles = getPriorityStyles(module.priority);
          return (
          <div 
            key={module.id} 
            className={`bg-white rounded-lg shadow-sm border-2 transition-all duration-200 hover:shadow-md ${styles.border}`}
          >
            <div className={`border-b p-4 ${styles.bg}`}>
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  {module.title}
                </h3>
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${styles.badge}`}>
                  {styles.label}
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-2">{module.description}</p>
            </div>
            
            <div className="p-4">
              <button
                onClick={() => handleAnalysisModuleClick(module.id)}
                disabled={isAnalyzing}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                  activeModule === module.id
                    ? 'bg-blue-600 text-white shadow-md'
                    : `border-2 border-dashed ${styles.button}`
                } ${isAnalyzing ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-sm'}`}
              >
                {isAnalyzing && activeModule === module.id ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    分析中...
                  </span>
                ) : (
                  <span className="flex items-center justify-center">
                    <span className="mr-2">🔍</span>
                    开始分析
                  </span>
                )}
              </button>
            </div>
          </div>
          );
        })}
      </div>

      {/* 分析结果展示区域 */}
      {analysisResults && (
        <div ref={resultsRef} className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <span className="mr-2">📊</span>
                {analysisResults.title}
              </h3>
              <button
                onClick={() => setAnalysisResults(null)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          
          <div className="p-6">
            {/* 分析描述 */}
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <SafeMarkdownRenderer text={analysisResults.summary} />
            </div>

            {/* 结果表格 */}
            {analysisResults.data && analysisResults.data.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {Object.keys(analysisResults.data[0]).map((header) => (
                        <th
                          key={header}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {analysisResults.data.map((row, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        {Object.values(row).map((value, cellIndex) => (
                          <td 
                            key={cellIndex} 
                            className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap"
                          >
                            <span className={getStatusColor(String(value))}>
                              {String(value)}
                            </span>
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <span className="text-4xl mb-4 block">📝</span>
                <p>暂无分析数据</p>
              </div>
            )}

            {/* 专业提示 */}
            <div className="mt-6 p-4 bg-indigo-50 border border-indigo-200 rounded-lg">
              <div className="flex items-start">
                <span className="text-indigo-600 mr-2">💡</span>
                <div className="text-sm text-indigo-800">
                  <strong>专业提示：</strong>
                  <span className="ml-1">
                    {getAnalysisInsight(activeModule || '')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 帮助和文档链接 */}
      <div className="bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-lg p-4 md:p-6">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex-1 lg:pr-6">
            <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2 flex items-center">
              <span className="mr-2">📚</span>
              需要帮助？
            </h3>
            <p className="text-sm md:text-base text-gray-600 mb-4">
              查看详细的信号分析文档，了解更多专业分析技巧和解读方法
            </p>
            
            {/* 在线SQL分析工具介绍 - 移动端优化 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 md:p-4">
              <div className="flex flex-col sm:flex-row sm:items-start space-y-3 sm:space-y-0 sm:space-x-3">
                <div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                  <span className="text-blue-600 text-xl">🛠️</span>
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-blue-800 mb-2 text-sm md:text-base">在线SQL分析工具</h4>
                  <p className="text-blue-700 mb-3 text-sm leading-relaxed">
                    点击右上角的"在线SQL分析"按钮，可在专业的SQLite在线环境中：
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-blue-600 text-xs">
                    <div className="flex items-center space-x-1">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0"></span>
                      <span>运行自定义SQL查询分析策略数据</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0"></span>
                      <span>使用可视化图表展示分析结果</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0"></span>
                      <span>导出分析数据和报告</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0"></span>
                      <span>访问完整的trade_signals数据表</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex-shrink-0 lg:ml-6">
            <a
              href="https://docs.myinvestpilot.com/docs/primitives/advanced/troubleshooting/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 md:px-6 py-2.5 md:py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-md hover:shadow-lg text-sm md:text-base font-medium"
            >
              <span className="mr-2">📖</span>
              查看完整文档
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PortfolioSignals;
