import { InformationCircleIcon } from '@heroicons/react/24/outline';

import { EquityBondRatioData } from '../../../types/market';

interface MarketEquityBondRatioProps {
  data: EquityBondRatioData;
}

export const MarketEquityBondRatio: React.FC<MarketEquityBondRatioProps> = ({
  data,
}) => (
  <div className="bg-white rounded-lg shadow p-6 space-y-4">
    <div className="flex justify-between items-start">
      <div>
        <h3 className="text-2xl font-semibold">股债性价比</h3>
        <p className="text-gray-600 mt-1">对比股票市场与债券市场的相对收益</p>
      </div>
      <div className="text-right">
        <div className="text-3xl font-bold">
          {data.current_levels.earnings_yield.toFixed(2)}%
        </div>
        <p className="text-sm text-gray-500">当前股票收益率</p>
      </div>
    </div>

    <img
      src="https://media.i365.tech/myinvestpilot/markets/cn/equity_bond_ratio.png"
      alt="股债性价比图表"
      className="w-full rounded-lg"
    />

    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold">中国视角</h4>
        <p className="mt-2">
          相对于中债收益率({data.current_levels.cn_bond_yield}%) 的溢价为
          {data.china_perspective.ratio}%, 处于历史
          {data.china_perspective.percentile}%分位
        </p>
      </div>
      <div className="p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold">美国视角</h4>
        <p className="mt-2">
          相对于美债收益率({data.current_levels.us_bond_yield}%) 的溢价为
          {data.us_perspective.ratio}%, 处于历史{data.us_perspective.percentile}
          %分位
        </p>
      </div>
    </div>

    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-start">
        <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5" />
        <div className="ml-3">
          <h4 className="text-sm font-medium text-blue-900">指标分析</h4>
          <p className="mt-1 text-sm text-blue-700">
            目前处于{data.analysis.zone}， 风险等级{data.analysis.risk_level}。
            {data.analysis.suggestion}
          </p>
        </div>
      </div>
    </div>

    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <div className="flex items-start">
        <InformationCircleIcon className="h-5 w-5 text-gray-500 mt-0.5" />
        <div className="ml-3">
          <h4 className="text-sm font-medium text-gray-900">指标说明</h4>
          <p className="mt-1 text-sm text-gray-700">
            股债性价比通过比较股票市场收益率（盈利收益率）与债券市场收益率，帮助投资者评估股票市场的相对投资价值。当股票收益率显著高于债券收益率时，通常意味着股票市场具有较好的投资价值；反之则表明债券市场可能更具吸引力。该指标同时考虑了国内债券和美债视角，提供了更全面的市场比较。
          </p>
        </div>
      </div>
    </div>
  </div>
);
