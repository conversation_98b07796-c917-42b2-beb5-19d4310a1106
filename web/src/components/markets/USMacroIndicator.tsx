/* eslint-disable @typescript-eslint/naming-convention */
import { InformationCircleIcon } from '@heroicons/react/24/outline';

import { MacroIndicator, MacroIndicators } from '../../../types/market';

interface USMacroIndicatorItemProps {
  title: string;
  data: MacroIndicator;
  suffix?: string;
  subtitle?: string; // 添加可选的副标题
}

const USMacroIndicatorItem: React.FC<USMacroIndicatorItemProps> = ({
  title,
  data,
  suffix = '%',
  subtitle,
}) => (
  <div className="p-4 bg-white bg-opacity-50 rounded-lg">
    <div className="flex justify-between items-start mb-2">
      <div>
        <h4 className="text-lg font-medium">{title}</h4>
        {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
      </div>
      <span className="text-2xl font-bold">
        {data.current}
        {suffix}
      </span>
    </div>
    <div className="space-y-1 text-sm">
      <div className="flex justify-between">
        <span className="text-gray-600">历史分位</span>
        <span>{data.percentile.toFixed(1)}%</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">趋势</span>
        <span>{data.trend.trend}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">动量</span>
        <span>{data.trend.momentum}</span>
      </div>
    </div>
  </div>
);

interface USMacroIndicatorProps {
  data: MacroIndicators;
  imgSrc: string;
}

export const USMacroIndicator: React.FC<USMacroIndicatorProps> = ({
  data,
  imgSrc,
}) => {
  const { indicators, risk_assessment } = data;

  return (
    <div className="bg-white rounded-lg shadow p-6 space-y-4">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-2xl font-semibold">宏观经济指标</h3>
          <p className="text-gray-600 mt-1">美国主要宏观经济指标监控</p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold">
            {risk_assessment.score.toFixed(1)}
          </div>
          <p className="text-sm text-gray-500">风险评分</p>
        </div>
      </div>

      <img src={imgSrc} alt="宏观经济指标" className="w-full rounded-lg" />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <USMacroIndicatorItem
          title="核心PCE"
          data={indicators.pce}
          subtitle="美联储首选通胀指标"
        />
        <USMacroIndicatorItem
          title="国债收益率"
          data={indicators.bond_yield}
          subtitle="十年期国债"
        />
        <USMacroIndicatorItem title="失业率" data={indicators.unemployment} />
        <USMacroIndicatorItem
          title="CPI"
          data={indicators.inflation}
          subtitle="消费者物价指数"
        />
      </div>

      {/* 通胀对比分析卡片 */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-yellow-500 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-yellow-900">
              通胀指标对比
            </h4>
            <p className="mt-1 text-sm text-yellow-700">
              核心PCE（{indicators.pce.current}%）与CPI（
              {indicators.inflation.current}%） 的差异为
              {(indicators.pce.current - indicators.inflation.current).toFixed(
                1
              )}
              %。
              PCE是美联储首选的通胀指标，通常低于CPI，主要由于计算方法和覆盖范围的差异。
            </p>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-900">风险评估</h4>
            <p className="mt-1 text-sm text-blue-700">
              风险水平{risk_assessment.level}，{risk_assessment.summary}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-gray-500 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-gray-900">指标说明</h4>
            <p className="mt-1 text-sm text-gray-700">
              该指标集合监控美国经济的核心维度：核心PCE作为美联储首选的通胀指标，
              与CPI一起反映通胀压力；国债收益率反映市场利率水平和货币政策取向；
              失业率反映就业市场状况。这些指标的组合变化为判断经济周期位置和政策走向提供重要依据。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
