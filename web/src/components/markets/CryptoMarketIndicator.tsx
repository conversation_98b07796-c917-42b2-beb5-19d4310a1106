import { InformationCircleIcon } from '@heroicons/react/24/outline';

import { CryptoIndicator, CryptoIndicators } from '../../../types/market';

interface CryptoIndicatorItemProps {
  title: string;
  data: CryptoIndicator;
  suffix?: string;
}

const CryptoIndicatorItem: React.FC<CryptoIndicatorItemProps> = ({
  title,
  data,
  suffix = '',
}) => (
  <div className="p-4 bg-white bg-opacity-50 rounded-lg">
    <div className="flex justify-between items-start mb-2">
      <h4 className="text-lg font-medium">{title}</h4>
      <span className="text-2xl font-bold">
        {data.current.toFixed(2)}
        {suffix}
      </span>
    </div>
    <div className="space-y-1 text-sm">
      <div className="flex justify-between">
        <span className="text-gray-600">历史分位</span>
        <span>{data.percentile.toFixed(1)}%</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">趋势</span>
        <span>{data.trend.trend}</span>
      </div>
      <div className="flex justify-between">
        <span className="text-gray-600">风险等级</span>
        <span>{data.risk_level}</span>
      </div>
    </div>
  </div>
);

interface CryptoMarketIndicatorProps {
  data: CryptoIndicators;
  imgSrc: string;
}

export const CryptoMarketIndicator: React.FC<CryptoMarketIndicatorProps> = ({
  data,
  imgSrc,
}) => {
  return (
    <div className="bg-white rounded-lg shadow p-6 space-y-4">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-2xl font-semibold">加密货币市场指标</h3>
          <p className="text-gray-600 mt-1">比特币核心指标监控</p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold">
            {data.risk_assessment.score.toFixed(1)}
          </div>
          <p className="text-sm text-gray-500">风险评分</p>
        </div>
      </div>

      <img src={imgSrc} alt="加密货币市场指标" className="w-full rounded-lg" />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <CryptoIndicatorItem title="MVRV Z-Score" data={data.indicators.mvrv} />
        <CryptoIndicatorItem title="NVT比率" data={data.indicators.nvt} />
        <CryptoIndicatorItem
          title="恐惧贪婪指数"
          data={data.indicators.fear_greed}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <CryptoIndicatorItem
          title="波动率"
          data={data.indicators.volatility}
          suffix="%"
        />
        <CryptoIndicatorItem
          title="活跃地址数"
          data={data.indicators.active_addresses}
        />
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-900">风险评估</h4>
            <p className="mt-1 text-sm text-blue-700">
              风险水平{data.risk_assessment.level}，
              {data.risk_assessment.summary}
            </p>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-gray-500 mt-0.5" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-gray-900">指标说明</h4>
            <p className="mt-1 text-sm text-gray-700">
              该指标集合以比特币的视角从多个维度监控加密币市场风险状况：MVRV
              Z-Score反映市场估值水平，NVT比率衡量网络价值，
              恐惧贪婪指数反映市场情绪，波动率显示市场稳定性，活跃地址数反映网络活跃度。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
