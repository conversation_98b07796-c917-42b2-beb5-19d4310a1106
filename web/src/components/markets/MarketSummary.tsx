import { MarketData } from '../../../types/market';
import { getZoneColor, getZoneIcon } from '../../utils/marketStyle';
import { MarketAIAnalysis } from './MarketAIAnalysis';

interface MarketSummaryProps {
  data: MarketData;
  showAiAnalysis: boolean;
  onToggleAiAnalysis: () => void;
}

export const MarketSummary: React.FC<MarketSummaryProps> = ({
  data,
  showAiAnalysis,
  onToggleAiAnalysis,
}) => {
  if (data.market === 'CN') {
    return (
      <div
        className={`p-8 rounded-lg border ${getZoneColor(
          data.risk_assessment.level
        )}`}
      >
        <div className="flex items-start justify-between mb-6">
          <div>
            <h3 className="text-2xl font-semibold">当前市场状态</h3>
            <p className="text-sm text-gray-500 mt-1">
              更新时间: {new Date(data.last_update).toLocaleString()}
            </p>
          </div>
          {getZoneIcon(data.risk_assessment.level)}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="p-4 bg-white bg-opacity-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600">风险评分</p>
            <p className="text-2xl font-bold mt-2">
              {data.risk_assessment.score.toFixed(1)}
            </p>
          </div>
          <div className="p-4 bg-white bg-opacity-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600">风险水平</p>
            <p className="text-2xl font-bold mt-2">
              {data.risk_assessment.level}
            </p>
          </div>
          <div className="p-4 bg-white bg-opacity-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600">建议操作</p>
            <p className="text-2xl font-bold mt-2">
              {data.risk_assessment.summary}
            </p>
          </div>
        </div>

        <MarketAIAnalysis
          content={data.ai_analysis.content}
          show={showAiAnalysis}
          onToggle={onToggleAiAnalysis}
        />
      </div>
    );
  }

  if (data.market === 'US') {
    return (
      <div
        className={`p-8 rounded-lg border ${getZoneColor(
          data.indicators.macro.risk_assessment.level
        )}`}
      >
        <div className="flex items-start justify-between mb-6">
          <div>
            <h3 className="text-2xl font-semibold">当前市场状态</h3>
            <p className="text-sm text-gray-500 mt-1">
              更新时间: {new Date(data.last_update).toLocaleString()}
            </p>
          </div>
          {getZoneIcon(data.indicators.macro.risk_assessment.level)}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="p-4 bg-white bg-opacity-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600">风险评分</p>
            <p className="text-2xl font-bold mt-2">
              {data.indicators.macro.risk_assessment.score.toFixed(1)}
            </p>
          </div>
          <div className="p-4 bg-white bg-opacity-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600">风险水平</p>
            <p className="text-2xl font-bold mt-2">
              {data.indicators.macro.risk_assessment.level}
            </p>
          </div>
          <div className="p-4 bg-white bg-opacity-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600">建议操作</p>
            <p className="text-2xl font-bold mt-2">
              {data.indicators.macro.risk_assessment.summary}
            </p>
          </div>
        </div>

        <MarketAIAnalysis
          content={data.ai_analysis.content}
          show={showAiAnalysis}
          onToggle={onToggleAiAnalysis}
        />
      </div>
    );
  }

  if (data.market === 'CRYPTO') {
    return (
      <div
        className={`p-8 rounded-lg border ${getZoneColor(
          data.indicators.crypto.risk_assessment.level
        )}`}
      >
        <div className="flex items-start justify-between mb-6">
          <div>
            <h3 className="text-2xl font-semibold">当前市场状态</h3>
            <p className="text-sm text-gray-500 mt-1">
              更新时间: {new Date(data.last_update).toLocaleString()}
            </p>
          </div>
          {getZoneIcon(data.indicators.crypto.risk_assessment.level)}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="p-4 bg-white bg-opacity-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600">风险评分</p>
            <p className="text-2xl font-bold mt-2">
              {data.indicators.crypto.risk_assessment.score.toFixed(1)}
            </p>
          </div>
          <div className="p-4 bg-white bg-opacity-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600">风险水平</p>
            <p className="text-2xl font-bold mt-2">
              {data.indicators.crypto.risk_assessment.level}
            </p>
          </div>
          <div className="p-4 bg-white bg-opacity-50 rounded-lg">
            <p className="text-sm font-medium text-gray-600">建议操作</p>
            <p className="text-2xl font-bold mt-2">
              {data.indicators.crypto.risk_assessment.summary}
            </p>
          </div>
        </div>

        <MarketAIAnalysis
          content={data.ai_analysis.content}
          show={showAiAnalysis}
          onToggle={onToggleAiAnalysis}
        />
      </div>
    );
  }

  return null;
};
