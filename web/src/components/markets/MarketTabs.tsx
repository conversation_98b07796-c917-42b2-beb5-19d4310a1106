import { MarketType } from '../../../types/market';

interface MarketTab {
  id: MarketType;
  name: string;
  disabled?: boolean;
  comingSoon?: boolean;
}

interface MarketTabsProps {
  activeMarket: MarketType;
  onMarketChange: (market: MarketType) => void;
}

const MARKET_TABS: MarketTab[] = [
  { id: 'cn', name: 'A股市场', disabled: true, comingSoon: true },
  { id: 'us', name: '美股市场' },
  { id: 'crypto', name: '加密货币', disabled: true, comingSoon: true },
];

export const MarketTabs: React.FC<MarketTabsProps> = ({
  activeMarket,
  onMarketChange,
}) => {
  return (
    <div className="flex justify-center space-x-4">
      {MARKET_TABS.map((tab) => (
        <button
          key={tab.id}
          onClick={() => !tab.disabled && onMarketChange(tab.id)}
          disabled={tab.disabled}
          className={`px-4 py-2 rounded-lg ${
            // eslint-disable-next-line no-nested-ternary
            activeMarket === tab.id
              ? 'bg-gray-900 text-white'
              : tab.disabled
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-100 hover:bg-gray-200'
          }`}
        >
          {tab.name}
          {tab.comingSoon && ' (即将上线)'}
        </button>
      ))}
    </div>
  );
};
