/* eslint-disable no-plusplus */
import { useEffect, useState } from 'react';

import { SparklesIcon } from '@heroicons/react/24/outline';
import ReactMarkdown from 'react-markdown';

interface MarketAIAnalysisProps {
  content: string;
  show: boolean;
  onToggle: () => void;
}

export const MarketAIAnalysis: React.FC<MarketAIAnalysisProps> = ({
  content,
  show,
  onToggle,
}) => {
  const [typedContent, setTypedContent] = useState('');

  useEffect(() => {
    if (show && content) {
      let currentText = '';
      let currentIndex = 0;

      const typeText = () => {
        if (currentIndex < content.length) {
          currentText += content[currentIndex];
          setTypedContent(currentText);
          currentIndex++;
          setTimeout(typeText, 20);
        }
      };

      typeText();
    } else {
      setTypedContent('');
    }
  }, [show, content]);

  return (
    <div className="mt-8">
      <div className="flex items-center">
        <button
          onClick={onToggle}
          className="inline-flex items-center gap-2 px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors"
        >
          <SparklesIcon className="h-5 w-5" />
          AI 深度解析
        </button>
        <span className="ml-2 text-sm text-gray-500">
          此内容由 AI 模型生成，仅供参考，风险自负
        </span>
      </div>

      {show && (
        <div className="mt-6 bg-white rounded-lg p-8">
          <div className="prose max-w-none">
            <ReactMarkdown>{typedContent}</ReactMarkdown>
          </div>
        </div>
      )}
    </div>
  );
};
