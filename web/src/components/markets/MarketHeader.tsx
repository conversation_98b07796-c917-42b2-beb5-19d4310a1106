/* eslint-disable no-nested-ternary */
import React from 'react';

import { MarketType } from '../../../types/market';

export interface MarketHeaderProps {
  activeMarket: MarketType;
  onMarketChange: (market: MarketType) => void;
}

interface MarketTab {
  id: MarketType;
  name: string;
  description?: string;
  updateTime?: string;
  disabled?: boolean;
  comingSoon?: boolean;
}

const MARKET_TABS: MarketTab[] = [
  {
    id: 'cn',
    name: 'A股市场',
    description: '沪深300指数、估值、风险等核心指标监控',
    updateTime: '每周更新',
  },
  {
    id: 'us',
    name: '美股市场',
    description: '美国宏观经济指标与市场风险评估',
    updateTime: '每周更新',
  },
  {
    id: 'crypto',
    name: '加密货币',
    description: '比特币等主流加密货币市场分析',
    updateTime: '每日更新',
  },
];

// 确保组件使用正确的类型定义
export const MarketHeader: React.FC<MarketHeaderProps> = ({
  activeMarket,
  onMarketChange,
}) => {
  return (
    <>
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-main">市场洞察</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          通过多维度技术指标，深入解析市场动态，助您把握市场周期
        </p>
      </div>

      <div className="flex justify-center space-x-4 mt-8 mb-8">
        {MARKET_TABS.map((tab) => (
          <button
            key={tab.id}
            onClick={() => !tab.disabled && onMarketChange(tab.id)}
            disabled={tab.disabled}
            className={`px-6 py-3 rounded-lg transition-colors ${
              activeMarket === tab.id
                ? 'bg-gray-900 text-white'
                : tab.disabled
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-100 hover:bg-gray-200'
            }`}
          >
            <div className="text-left">
              <div className="font-medium">{tab.name}</div>
              {tab.description && (
                <div
                  className={`text-sm mt-1 ${
                    activeMarket === tab.id
                      ? 'text-gray-300'
                      : tab.disabled
                      ? 'text-gray-400'
                      : 'text-gray-500'
                  }`}
                >
                  {tab.description}
                  {tab.updateTime && (
                    <div className="text-sm mt-1 text-gray-600">
                      {tab.updateTime}
                    </div>
                  )}
                </div>
              )}
              {tab.comingSoon && (
                <div className="text-sm mt-1 text-gray-400">即将上线</div>
              )}
            </div>
          </button>
        ))}
      </div>
    </>
  );
};
