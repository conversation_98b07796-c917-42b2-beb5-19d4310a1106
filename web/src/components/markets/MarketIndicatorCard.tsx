/* eslint-disable no-nested-ternary */
import { InformationCircleIcon } from '@heroicons/react/24/outline';

import { IndicatorData } from '../../../types/market';

interface MarketIndicatorCardProps {
  title: string;
  description: string;
  explanation: string;
  data: IndicatorData;
  imgSrc: string;
}

export const MarketIndicatorCard: React.FC<MarketIndicatorCardProps> = ({
  title,
  description,
  explanation,
  data,
  imgSrc,
}) => (
  <div className="bg-white rounded-lg shadow p-6 space-y-4">
    <div className="flex justify-between items-start">
      <div>
        <h3 className="text-2xl font-semibold">{title}</h3>
        <p className="text-gray-600 mt-1">{description}</p>
      </div>
      <div className="text-right">
        <div className="text-3xl font-bold">
          {data.latest !== undefined
            ? `${data.latest}%`
            : data.latest_pe !== undefined
            ? data.latest_pe.toFixed(2)
            : ''}
        </div>
        <p className="text-sm text-gray-500">当前数值</p>
      </div>
    </div>

    <img src={imgSrc} alt={`${title}图表`} className="w-full rounded-lg" />

    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-start">
        <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5" />
        <div className="ml-3">
          <h4 className="text-sm font-medium text-blue-900">指标分析</h4>
          <p className="mt-1 text-sm text-blue-700">
            目前处于{data.zone}，风险等级{data.risk_level}。{data.suggestion}
          </p>
        </div>
      </div>
    </div>

    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <div className="flex items-start">
        <InformationCircleIcon className="h-5 w-5 text-gray-500 mt-0.5" />
        <div className="ml-3">
          <h4 className="text-sm font-medium text-gray-900">指标说明</h4>
          <p className="mt-1 text-sm text-gray-700">{explanation}</p>
        </div>
      </div>
    </div>
  </div>
);
