import React from 'react';
import { AlertTriangle, Info, TrendingUp, Shield, Zap } from 'lucide-react';

import { PerformanceData } from '../../types/types';
import { getRiskLevel, formatVolatility } from '../utils/portfolioHelper';

interface RiskLevelBannerProps {
  performance: PerformanceData | undefined;
}

const RiskLevelBanner: React.FC<RiskLevelBannerProps> = ({ performance }) => {
  if (!performance?.volatility) {
    return null;
  }

  const riskInfo = getRiskLevel(performance.volatility);

  const getIcon = (level: string) => {
    switch (level) {
      case '极低风险':
        return <Shield className={`w-6 h-6 ${riskInfo.color}`} />;
      case '低风险':
        return <TrendingUp className={`w-6 h-6 ${riskInfo.color}`} />;
      case '中低风险':
        return <Info className={`w-6 h-6 ${riskInfo.color}`} />;
      case '中等风险':
        return <Info className={`w-6 h-6 ${riskInfo.color}`} />;
      case '中高风险':
        return <AlertTriangle className={`w-6 h-6 ${riskInfo.color}`} />;
      case '高风险':
        return <AlertTriangle className={`w-6 h-6 ${riskInfo.color}`} />;
      case '极高风险':
        return <Zap className={`w-6 h-6 ${riskInfo.color}`} />;
      default:
        return <Info className={`w-6 h-6 ${riskInfo.color}`} />;
    }
  };

  return (
    <div className={`w-full max-w-6xl mx-auto mb-6 p-4 rounded-lg border-2 ${riskInfo.bgColor} shadow-md`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center">
            {getIcon(riskInfo.level)}
          </div>
          <div>
            <h3 className={`text-lg font-bold ${riskInfo.color}`}>
              风险等级：{riskInfo.level}
            </h3>
            <p className="text-sm text-gray-600">
              {riskInfo.description}
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className={`text-xl font-bold ${riskInfo.color}`}>
            波动率：{formatVolatility(performance.volatility)}
          </div>
          <div className="text-xs text-gray-500">
            基于年化波动率计算
          </div>
        </div>
      </div>
    </div>
  );
};

export default RiskLevelBanner; 