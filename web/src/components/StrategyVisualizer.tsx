import React, { useState, useEffect, useCallback, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { Editor } from '@monaco-editor/react';
import FlowLegend from './FlowLegend';

import {
  jsonToFlow,
  flowToJson,
  validateTradeStrategy,
  TradeStrategy,
  StrategyNode,
} from '../utils/strategyVisualizer';
import { loadPrimitivesManifest } from '../utils/primitives';

// 导入 ReactFlow 的类型
import type { Node, Edge } from 'reactflow';

interface StrategyVisualizerProps {
  strategyDsl?: any; // 策略JSON对象
  mode?: 'readonly' | 'edit'; // 模式：只读或编辑
  onStrategyChange?: (strategy: TradeStrategy) => void; // 策略变化回调
}

// 客户端专用的 Flow 组件
const FlowComponent = dynamic(
  () => import('./FlowComponent'),
  { ssr: false }
);

export const StrategyVisualizer: React.FC<StrategyVisualizerProps> = ({
  strategyDsl,
  mode = 'readonly',
  onStrategyChange,
}) => {
  // 编辑器状态
  const [editorValue, setEditorValue] = useState<string>('');
  const [editorError, setEditorError] = useState<string>('');

  // 节点和边的状态（不使用 React Flow hooks）
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  
  // 原语清单状态
  const [primitivesManifest, setPrimitivesManifest] = useState<any>(null);
  const [manifestLoading, setManifestLoading] = useState(false);
  const [manifestError, setManifestError] = useState<string>('');

  // 加载原语清单
  useEffect(() => {
    const loadManifest = async () => {
      setManifestLoading(true);
      try {
        console.log('Loading primitives manifest...');
        const manifest = await loadPrimitivesManifest();
        setPrimitivesManifest(manifest);
        console.log('Primitives manifest loaded successfully:', manifest);
      } catch (error) {
        console.error('Failed to load primitives manifest:', error);
        setManifestError(`加载原语清单失败: ${(error as Error).message}`);
      } finally {
        setManifestLoading(false);
      }
    };

    loadManifest();
  }, []);

  // 从编辑器内容更新图形
  const updateFlowFromEditor = useCallback((jsonString: string) => {
    try {
      console.log('updateFlowFromEditor called with:', jsonString.substring(0, 200));
      const parsed = JSON.parse(jsonString);
      console.log('Parsed JSON:', parsed);
      const tradeStrategy = parsed.trade_strategy || parsed;
      console.log('Extracted trade strategy:', tradeStrategy);

      const isValid = validateTradeStrategy(tradeStrategy);
      console.log('Strategy validation result:', isValid);
      if (!isValid) {
        setEditorError('策略JSON格式不正确');
        console.log('Strategy validation failed');
        return;
      }

      const { nodes: newNodes, edges: newEdges } = jsonToFlow(tradeStrategy);
      console.log('Generated nodes:', newNodes);
      console.log('Generated edges:', newEdges);
      setNodes(newNodes);
      setEdges(newEdges);
      setEditorError('');

      // 通知父组件策略变化
      if (onStrategyChange) {
        onStrategyChange(tradeStrategy);
      }
    } catch (error) {
      console.error('Error in updateFlowFromEditor:', error);
      setEditorError(`JSON解析错误: ${(error as Error).message}`);
    }
  }, [setNodes, setEdges, onStrategyChange]);

  // 初始化编辑器内容
  useEffect(() => {
    if (strategyDsl) {
      const jsonString = JSON.stringify(strategyDsl, null, 2);
      setEditorValue(jsonString);
      updateFlowFromEditor(jsonString);
    } else {
      // 默认策略模板
      const defaultStrategy = {
        trade_strategy: {
          indicators: [],
          signals: [],
        },
      };
      const jsonString = JSON.stringify(defaultStrategy, null, 2);
      setEditorValue(jsonString);
      updateFlowFromEditor(jsonString);
    }
  }, [strategyDsl, updateFlowFromEditor]);

  // 编辑器内容变化处理
  const handleEditorChange = useCallback((value: string | undefined) => {
    if (value !== undefined) {
      setEditorValue(value);
      updateFlowFromEditor(value);
    }
  }, [updateFlowFromEditor]);

  // 从图形更新编辑器内容
  const updateEditorFromFlow = useCallback(() => {
    if (mode === 'readonly') return;
    
    try {
      const tradeStrategy = flowToJson(nodes as StrategyNode[], edges);
      const strategyWrapper = { trade_strategy: tradeStrategy };
      const jsonString = JSON.stringify(strategyWrapper, null, 2);
      setEditorValue(jsonString);
      setEditorError('');
      
      // 通知父组件策略变化
      if (onStrategyChange) {
        onStrategyChange(tradeStrategy);
      }
    } catch (error) {
      setEditorError(`图形转换错误: ${(error as Error).message}`);
    }
  }, [nodes, edges, mode, onStrategyChange]);

  // 节点变化时更新编辑器
  useEffect(() => {
    if (mode === 'edit') {
      updateEditorFromFlow();
    }
  }, [nodes, edges, mode, updateEditorFromFlow]);

  // Monaco编辑器配置
  const editorOptions = useMemo(() => ({
    readOnly: mode === 'readonly',
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    wordWrap: 'on' as const,
    automaticLayout: true,
  }), [mode]);

  return (
    <div className="w-full h-full flex flex-col">
      {/* 状态指示器 */}
      <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              manifestLoading ? 'bg-yellow-400' : 
              manifestError ? 'bg-red-400' : 
              primitivesManifest ? 'bg-green-400' : 'bg-gray-400'
            }`} />
            <span className="text-sm text-gray-600">
              {manifestLoading ? '加载原语清单...' :
               manifestError ? '原语清单加载失败' :
               primitivesManifest ? '原语清单已加载' : '未加载原语清单'}
            </span>
          </div>
          {editorError && (
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-red-400" />
              <span className="text-sm text-red-600">{editorError}</span>
            </div>
          )}
        </div>
        <div className="text-sm text-gray-500">
          模式: {mode === 'readonly' ? '只读' : '编辑'}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex">
        {/* 左侧：Monaco编辑器 */}
        <div className="w-1/2 border-r">
          <div className="h-full">
            <Editor
              height="100%"
              defaultLanguage="json"
              value={editorValue}
              onChange={handleEditorChange}
              options={editorOptions}
              theme="vs-light"
            />
          </div>
        </div>

        {/* 右侧：React Flow画布 */}
        <div className="w-1/2 h-full relative">
          <FlowComponent
            initialNodes={nodes}
            initialEdges={edges}
            onNodesChange={setNodes}
            onEdgesChange={setEdges}
            mode={mode}
          />

          {/* 图例 */}
          <div className="absolute top-4 right-4 z-10">
            <FlowLegend />
          </div>
        </div>
      </div>
    </div>
  );
};
