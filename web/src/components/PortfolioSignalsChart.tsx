import React, { useState, useEffect, useCallback } from 'react';
import ReactECharts, { EChartsOption } from 'echarts-for-react';
import { Database } from 'sql.js';

interface PortfolioSignalsChartProps {
  showSignals?: boolean;
  showNormalization?: boolean;
  signalDatabase?: Database | null;
}

const PortfolioSignalsChart: React.FC<PortfolioSignalsChartProps> = ({
  showSignals = false,
  showNormalization = false,
  signalDatabase = null,
}) => {
  const [chartOptions, setChartOptions] = useState<EChartsOption | null>(null);
  const [normalizePrice, setNormalizePrice] = useState<boolean>(false);
  const [showTradingSignals, setShowTradingSignals] = useState<boolean>(true);

  // Build chart options
  const buildChartOptions = useCallback(() => {
    if (!signalDatabase) return null;

    try {
      // Fetch price and signal data from signal database
      const dataStmt = signalDatabase.prepare(`
        SELECT date, symbol, close, high, low, signal
        FROM trade_signals 
        ORDER BY date, symbol
      `);
      
      const allData = [];
      while (dataStmt.step()) {
        allData.push(dataStmt.getAsObject());
      }
      dataStmt.free();

      if (allData.length === 0) {
        return null;
      }

      // Group data by symbol
      const symbolData: Record<string, any[]> = {};
      allData.forEach(row => {
        const symbol = String(row.symbol || '');
        if (symbol && !symbolData[symbol]) {
          symbolData[symbol] = [];
        }
        if (symbol) {
          symbolData[symbol]?.push(row);
        }
      });

      // Normalize prices if enabled
      const processedData: Record<string, any[]> = {};
      Object.keys(symbolData).forEach(symbol => {
        const data = symbolData[symbol];
        if (!data || data.length === 0) return;
        
        const basePrice = data[0]?.close || 1;
        
        processedData[symbol] = data.map(item => ({
          ...item,
          normalizedClose: normalizePrice ? (item.close / basePrice) * 100 : item.close,
          originalClose: item.close
        }));
      });

      // Build series
      const series: any[] = [];
      const colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b'];
      
      Object.keys(processedData).forEach((symbol, index) => {
        const data = processedData[symbol];
        if (!data || data.length === 0) return;
        
        const color = colors[index % colors.length];
        
        // Price line series
        const lineData = data.map(item => [
          item.date,
          normalizePrice ? item.normalizedClose : item.close
        ]);

        series.push({
          name: symbol,
          type: 'line',
          data: lineData,
          lineStyle: { width: 2 },
          itemStyle: { color },
          symbol: 'none',
          markPoint: showTradingSignals ? {
            data: data
              .filter(item => item.signal && ['B', 'S'].includes(item.signal))
              .map(item => ({
                coord: [item.date, normalizePrice ? item.normalizedClose : item.close],
                value: item.signal === 'B' ? 'B' : 'S',
                symbol: 'triangle',
                symbolSize: 12,
                symbolRotate: item.signal === 'B' ? 0 : 180,
                itemStyle: {
                  color: item.signal === 'B' ? '#22c55e' : '#ef4444',
                  borderColor: '#fff',
                  borderWidth: 1,
                },
                label: {
                  show: true,
                  position: item.signal === 'B' ? 'top' : 'bottom',
                  formatter: () => item.signal === 'B' ? 'B' : 'S',
                  fontSize: 10,
                  color: '#fff',
                  fontWeight: 'bold',
                  backgroundColor: item.signal === 'B' ? '#22c55e' : '#ef4444',
                  borderRadius: 3,
                  padding: [2, 4],
                },
                // 存储信号数据用于tooltip
                signalData: {
                  signal: item.signal,
                  price: item.close,
                  date: item.date,
                  symbol
                }
              }))
          } : undefined
        });
      });

      const option: EChartsOption = {
        title: {
          text: normalizePrice ? '价格标准化图表 (基准=100)' : '价格走势图表',
          left: 'center',
          top: 20,
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
          },
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
          formatter: (params: any) => {
            if (!Array.isArray(params)) return '';
            
            // 格式化日期显示
            const formatDate = (dateValue: any) => {
              const date = new Date(dateValue);
              return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              });
            };
            
            let tooltip = `<div style="margin-bottom: 5px; font-weight: bold;">${formatDate(params[0].axisValue)}</div>`;
            
            // 处理价格线数据
            params.forEach((param: any) => {
              if (param.seriesType === 'line') {
                const value = normalizePrice 
                  ? `${param.value[1].toFixed(2)} (标准化)`
                  : `${param.value[1].toFixed(2)}`;
                tooltip += `
                  <div style="display: flex; align-items: center; margin-bottom: 3px;">
                    <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
                    <span style="flex: 1;">${param.seriesName}: ${value}</span>
                  </div>
                `;
              }
            });
            
            // 修复：从processedData中查找信号数据，避免normalizedClose未定义的bug
            const date = params[0].axisValue;
            const allProcessedData = Object.values(processedData).flat();
            const signalsOnDate = allProcessedData.filter(
              (item: any) => item.date === date && item.signal && ['B', 'S'].includes(item.signal)
            );
            
            signalsOnDate.forEach(dayData => {
              const signalText = dayData.signal === 'B' ? '🔺 买入信号 (B)' : '🔻 卖出信号 (S)';
              const price = normalizePrice ? dayData.normalizedClose : dayData.close;
              tooltip += `
                <div style="margin-top: 8px; padding: 6px 10px; background-color: ${dayData.signal === 'B' ? '#dcfce7' : '#fef2f2'}; border-radius: 6px; border-left: 4px solid ${dayData.signal === 'B' ? '#22c55e' : '#ef4444'};">
                  <div style="font-weight: bold; color: ${dayData.signal === 'B' ? '#16a34a' : '#dc2626'}; margin-bottom: 2px;">${signalText}</div>
                  <div style="font-size: 12px; color: #666;">
                    <span style="margin-right: 10px;">价格: <strong>${price.toFixed(2)}</strong></span>
                    <span>股票: <strong>${dayData.symbol}</strong></span>
                  </div>
                </div>
              `;
            });
            
            return tooltip;
          },
        },
        legend: {
          type: 'scroll',
          top: 50,
          left: 'center',
          itemGap: 20,
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          top: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'time',
          boundaryGap: false,
          axisLine: {
            onZero: false,
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          name: normalizePrice ? '标准化价格' : '价格',
          nameLocation: 'middle',
          nameGap: 50,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              opacity: 0.3,
            },
          },
        },
        series,
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: 0,
            start: 0,
            end: 100,
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            start: 0,
            end: 100,
            height: 30,
            bottom: 20,
          },
        ],
      };

      return option;
    } catch (error) {
      console.error('Error building chart options:', error);
      return null;
    }
  }, [signalDatabase, showTradingSignals, normalizePrice]);

  // Update chart options when dependencies change
  useEffect(() => {
    const options = buildChartOptions();
    setChartOptions(options);
  }, [buildChartOptions]);

  // Handle loading state
  if (!signalDatabase) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载图表数据...</p>
        </div>
      </div>
    );
  }

  // Handle no data
  if (!chartOptions) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="text-center text-gray-500">
          <span className="text-4xl mb-4 block">📊</span>
          <p>暂无图表数据</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Control toggles */}
      {(showNormalization || showSignals) && (
        <div className="flex flex-col items-center space-y-3">
          <div className="flex items-center justify-center space-x-6">
            {/* Price normalization toggle */}
            {showNormalization && (
              <label className="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow border">
                <input
                  type="checkbox"
                  checked={normalizePrice}
                  onChange={(e) => setNormalizePrice(e.target.checked)}
                  className="form-checkbox h-4 w-4 text-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">
                  标准化价格 (便于比较相对表现)
                </span>
              </label>
            )}
            
            {/* Trading signals toggle */}
            {showSignals && (
              <label className="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg shadow border">
                <input
                  type="checkbox"
                  checked={showTradingSignals}
                  onChange={(e) => setShowTradingSignals(e.target.checked)}
                  className="form-checkbox h-4 w-4 text-green-500"
                />
                <span className="text-sm font-medium text-gray-700">
                  显示交易信号 {!showTradingSignals && '(已关闭以提升性能)'}
                </span>
              </label>
            )}
          </div>
          
          {/* Performance warning */}
          {showSignals && showTradingSignals && signalDatabase && (() => {
            try {
              const stmt = signalDatabase.prepare('SELECT COUNT(*) as count FROM trade_signals WHERE signal IN ("B", "S")');
              stmt.step();
              const result = stmt.getAsObject();
              const signalCount = Number(result.count) || 0;
              stmt.free();
              
              if (signalCount > 500) {
                return (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg px-4 py-2 text-center">
                    <span className="text-yellow-700 text-sm">
                      ⚠️ 检测到 {signalCount} 个交易信号，建议关闭信号显示以提升图表性能
                    </span>
                  </div>
                );
              }
            } catch (e) {
              // Ignore errors in signal counting
            }
            return null;
          })()}
        </div>
      )}

      {/* Chart */}
      <ReactECharts
        option={chartOptions}
        style={{ height: 600 }}
      />

      {/* Legend for signals */}
      {showTradingSignals && showSignals && (
        <div className="flex justify-center space-x-8 mt-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="inline-block w-0 h-0 border-l-[6px] border-r-[6px] border-b-[8px] border-l-transparent border-r-transparent border-b-green-500"></div>
            <span className="font-medium">买入信号 (B)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="inline-block w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-red-500"></div>
            <span className="font-medium">卖出信号 (S)</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PortfolioSignalsChart; 