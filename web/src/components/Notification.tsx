import React from 'react';

interface NotificationProps {
  isOpen: boolean;
  message: string;
  type?: 'success' | 'error';
}

const Notification: React.FC<NotificationProps> = ({
  isOpen,
  message,
  type = 'success',
}) => {
  if (!isOpen) return null;

  const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';

  return (
    <div
      className={`fixed bottom-4 right-4 ${bgColor} text-white p-4 rounded shadow-lg`}
    >
      {message}
    </div>
  );
};

export default Notification;
