import React from 'react';

import { motion } from 'framer-motion';

interface PortfolioTimeMachineProps {
  yearRange: string[];
  handleTimeFly: (year: string) => void;
  isLoggedIn: boolean;
}

const PortfolioTimeMachine: React.FC<PortfolioTimeMachineProps> = ({
  yearRange,
  handleTimeFly,
  isLoggedIn,
}) => {
  if (!isLoggedIn) {
    return (
      <motion.div
        className="sm:w-9/12 w-11/12 mb-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-2xl font-bold mb-4 text-main">时光机</h2>
        <p className="text-lg font-medium text-gray-600">
          请点击【我的】菜单登录后使用时光机功能
        </p>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="sm:w-9/12 w-11/12 mb-10"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <h2 className="text-2xl font-bold mb-4 text-main">时光机</h2>
      <div className="flex flex-wrap gap-2">
        {yearRange.map((year, index) => (
          <motion.button
            key={year}
            onClick={() => handleTimeFly(year)}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 10,
              delay: index * 0.1,
            }}
          >
            {year}
          </motion.button>
        ))}
      </div>
    </motion.div>
  );
};

export default PortfolioTimeMachine;
