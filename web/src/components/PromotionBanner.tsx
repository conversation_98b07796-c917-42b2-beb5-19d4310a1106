import React, { useState } from 'react';

const PromotionBanner = () => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) {
    return null;
  }

  return (
    <div className="bg-main text-black py-2.5 px-4 relative">
      <div className="max-w-6xl mx-auto flex items-center justify-center">
        <div className="flex items-center space-x-4">
          <div className="hidden md:flex items-center space-x-4">
            <span className="bg-black bg-opacity-10 text-black px-3 py-1 rounded text-xs font-medium">
              年中优惠
            </span>
            <span className="text-sm font-medium">策引会员限时半价</span>
            <span className="text-black text-opacity-80 text-sm">
              微信 <span className="font-mono font-semibold">improve365_cn</span> 咨询详情
            </span>
          </div>
          {/* 移动端简化版本 */}
          <div className="md:hidden flex items-center space-x-3 text-center">
            <span className="bg-black bg-opacity-10 text-black px-2 py-1 rounded text-xs font-medium">
              限时优惠
            </span>
            <span className="text-sm">
              会员半价 · 微信 <span className="font-mono font-semibold">improve365_cn</span>
            </span>
          </div>
        </div>
        
        {/* 关闭按钮 */}
        <button
          onClick={() => setIsVisible(false)}
          className="ml-6 text-black text-opacity-60 hover:text-opacity-100 transition-colors p-1 absolute right-4"
          aria-label="关闭优惠通知"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default PromotionBanner; 