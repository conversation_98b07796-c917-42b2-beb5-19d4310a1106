import React from 'react';

interface FAQItem {
  question: string;
  answer: React.ReactNode;
}

interface FAQCategoryProps {
  title: string;
  items: FAQItem[];
}

interface FAQSectionProps {
  categories: FAQCategoryProps[];
}

// Individual FAQ item component 
const FAQItemComponent: React.FC<FAQItem> = ({ question, answer }) => {
  return (
    <details className="group">
      <summary className="flex items-center justify-between p-4 rounded-lg cursor-pointer bg-gray-50">
        <h5 className="font-medium text-gray-900">{question}</h5>
        <svg
          className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </summary>
      <div className="px-4 mt-4 leading-relaxed text-gray-700">
        {answer}
      </div>
    </details>
  );
};

// FAQ category component
const FAQCategory: React.FC<FAQCategoryProps> = ({ title, items }) => {
  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <h3 className="font-semibold text-main mb-2">{title}</h3>
      {items.map((item, index) => (
        <FAQItemComponent key={index} question={item.question} answer={item.answer} />
      ))}
    </div>
  );
};

// Main FAQ Section component
const FAQSection: React.FC<FAQSectionProps> = ({ categories }) => {
  return (
    <div className="space-y-4">
      {categories.map((category, index) => (
        <FAQCategory
          key={index}
          title={category.title}
          items={category.items}
        />
      ))}
    </div>
  );
};

export default FAQSection;
