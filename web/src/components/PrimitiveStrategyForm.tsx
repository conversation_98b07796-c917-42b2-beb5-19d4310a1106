import React, { useState, useEffect, useMemo, useCallback } from 'react';

import Ajv from 'ajv';
import { Form } from 'antd';
import { JsonEditor } from 'json-edit-react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import {
  JSONSchema,
  PortfolioSymbol,
  CapitalStrategy,
} from '../../types/types';
import {
  createPortfolio,
  generateStrategyDSL,
  searchSymbols,
  getOfficialPortfolios,
  updatePortfolio,
  getPortfolioDetails,
  getAIStrategyPromptVersionUrl,
  ApiError,
} from '../utils/api';
import SymbolSearch from './SymbolSearch';
import { trackEvent } from '../utils/trackEvent';
import { InfinitySpin } from 'react-loader-spinner';
import { useRouter } from 'next/router';

type ErrorMessage = string;

interface FormErrors {
  loading: ErrorMessage; // 加载错误
  schema: ErrorMessage;
  parsing: ErrorMessage;
  validation: ErrorMessage;
  submission: ErrorMessage;
  form: ErrorMessage; // 表单验证错误
  symbol: ErrorMessage; // 标的选择错误
  parameter: ErrorMessage; // 参数验证错误
  api: ErrorMessage; // API认证错误
}

interface PrimitiveStrategyFormProps {
  mode?: 'create' | 'update';
  initialCode?: string;
  onSubmit?: () => void;
  onValuesChange?: (changedValues: any, allValues: any) => void;
}

const initialErrors: FormErrors = {
  loading: '',
  schema: '',
  parsing: '',
  validation: '',
  submission: '',
  form: '',
  symbol: '',
  parameter: '',
  api: '',
};

const CustomInput = React.forwardRef<
  HTMLInputElement,
  { value?: string; onClick?: () => void }
>(({ value, onClick }, ref) => (
  <input
    ref={ref}
    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors cursor-pointer"
    value={value}
    onClick={onClick}
    readOnly
  />
));

CustomInput.displayName = 'CustomDateInput';

const PrimitiveStrategyForm: React.FC<PrimitiveStrategyFormProps> = ({
  mode = 'create',
  initialCode,
  onSubmit,
  onValuesChange,
}) => {
  const router = useRouter();
  const [form] = Form.useForm();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [capitalStrategy, setCapitalStrategy] =
    useState<CapitalStrategy | null>(null);
  const [dslJson, setDslJson] = useState<Record<string, any>>({});
  const [schema, setSchema] = useState<JSONSchema | null>(null);
  const [errors, setErrors] = useState<FormErrors>(initialErrors);
  const [nlInput, setNlInput] = useState('');
  const [generating, setGenerating] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [isSubmitSuccessful, setIsSubmitSuccessful] = useState(false);
  const [loading, setLoading] = useState(false); // 新增初始加载状态

  // 新增状态
  const [symbols, setSymbols] = useState<(string | PortfolioSymbol)[]>([]);
  const [symbolQuery, setSymbolQuery] = useState('');
  const [symbolSuggestions, setSymbolSuggestions] = useState<PortfolioSymbol[]>(
    []
  );
  const [isComposing, setIsComposing] = useState(false);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [market, setMarket] = useState('');
  const [currency, setCurrency] = useState('');
  const [commissionRate, setCommissionRate] = useState(0.001);
  const [updateTime, setUpdateTime] = useState('01:00');
  const [capitalStrategies, setCapitalStrategies] = useState<CapitalStrategy[]>(
    []
  );
  const [strategyTemplates, setStrategyTemplates] = useState<
    Record<string, any>
  >({});
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [selectedTemplateDescription, setSelectedTemplateDescription] = useState('');
  const [jsonEditorMode, setJsonEditorMode] = useState<'visual' | 'text'>(
    'visual'
  );
  
  // 保存从API加载的资金策略参数
  const [loadedCapitalStrategyParams, setLoadedCapitalStrategyParams] = useState<Record<string, any>>({});
  
  // 单独管理资金策略参数值，确保UI能正确更新
  const [capitalStrategyParamValues, setCapitalStrategyParamValues] = useState<Record<string, any>>({});

  // Memoize Ajv instance for better performance
  const ajv = useMemo(() => new Ajv({ allErrors: true }), []);

  // 保存从API加载的组合数据
  const [loadedPortfolioData, setLoadedPortfolioData] = useState<any>(null);

  const errorMsgWithLoginLink = useMemo(
    () => (
      <span>
        请
        <a
          href={`https://login.myinvestpilot.com/?r=${typeof window !== 'undefined' ? window.location.host : 'www.myinvestpilot.com'}${typeof window !== 'undefined' ? window.location.pathname : '/portfolios/create'}`}
          className="text-blue-600 hover:text-blue-800 underline"
          target="_blank"
          rel="noopener noreferrer"
        >
          【登录】
        </a>
        后再操作
      </span>
    ),
    []
  );

  // 加载初始数据（更新模式）
  useEffect(() => {
    if (mode === 'update' && initialCode && !loadedPortfolioData) {
      const loadPortfolioData = async () => {
        setLoading(true); // 开始加载
        try {
          setErrors((prev) => ({ ...prev, form: '' }));
          const portfolioData = await getPortfolioDetails(initialCode);
          setLoadedPortfolioData(portfolioData);
          
          // 填充基本信息
          setName(portfolioData.name || '');
          setDescription(portfolioData.description || '');
          setMarket(portfolioData.market || '');
          setCurrency(portfolioData.currency || '');
          setCommissionRate(Number(portfolioData.commission) || 0.001);
          setUpdateTime(portfolioData.update_time || '01:00');
          
          // 设置开始日期
          if (portfolioData.start_date) {
            setStartDate(new Date(portfolioData.start_date));
          }
          
          // 设置标的
          if (portfolioData.symbols) {
            setSymbols(portfolioData.symbols);
          }
          
          // 设置交易策略DSL（支持新旧格式），与提交格式保持一致
          if (portfolioData.strategy_definition?.trade_strategy) {
            // 新格式：组装成与提交时一致的结构
            const dslData: any = { 
              trade_strategy: portfolioData.strategy_definition.trade_strategy 
            };
            
            // 检查是否有 market_indicators
            const strategyDef = portfolioData.strategy_definition as any;
            if (strategyDef.market_indicators) {
              dslData.market_indicators = strategyDef.market_indicators;
            }
            
            setDslJson(dslData);
                      } else if ((portfolioData as any).parameters?.trade_strategy_dsl) {
              // 旧格式：parameters.trade_strategy_dsl
              setDslJson({ trade_strategy: (portfolioData as any).parameters.trade_strategy_dsl });
            }
          
        } catch (error) {
          console.error('Error loading portfolio data:', error);
          if ((error as ApiError).status === 401) {
            setErrors((prev) => ({
              ...prev,
              loading: '❌ 请登录后再操作',
              api: errorMsgWithLoginLink as any,
            }));
          } else {
            setErrors((prev) => ({
              ...prev,
              loading: `❌ 加载组合数据失败：${(error as Error).message}`,
            }));
          }
        } finally {
          setLoading(false); // 结束加载
        }
      };
      
      loadPortfolioData();
    }
  }, [mode, initialCode, loadedPortfolioData, errorMsgWithLoginLink]);

  // 当资金策略列表加载完成后，应用从API加载的资金策略
  useEffect(() => {
    if (loadedPortfolioData && capitalStrategies.length > 0) {
      let capitalStrategyData = null;
      let strategyParams = {};

      // 检查新格式：strategy_definition.capital_strategy
      if (loadedPortfolioData.strategy_definition?.capital_strategy) {
        capitalStrategyData = loadedPortfolioData.strategy_definition.capital_strategy;
        strategyParams = capitalStrategyData.params || {};
      } 
      // 检查旧格式：capital_strategy + capital_strategy_params
      else if (loadedPortfolioData.capital_strategy) {
        capitalStrategyData = loadedPortfolioData.capital_strategy;
        strategyParams = (loadedPortfolioData as any).capital_strategy_params || {};
      }

              if (capitalStrategyData) {
          // 查找匹配的资金策略
          const matchedStrategy = capitalStrategies.find(
            s => s.strategy_name === capitalStrategyData.name
          );
        
        if (matchedStrategy) {
          setCapitalStrategy(matchedStrategy);
          // 保存参数，稍后应用
          setLoadedCapitalStrategyParams(typeof strategyParams === 'object' ? strategyParams : {});
        }
      }
    }
  }, [loadedPortfolioData, capitalStrategies]);

  // 加载资金策略列表
  useEffect(() => {
    const loadCapitalStrategies = async () => {
      if (mode === 'create') {
        setLoading(true); // 创建模式下显示加载动画
      }
      try {
        const portfolios = await getOfficialPortfolios('capital_strategy');
        const strategies = portfolios
          .map((p: any) => ({
            strategy_id: p.strategy_id,
            strategy_name: p.strategy_name,
            strategy_description: p.strategy_description,
            strategy_parameters: p.strategy_parameters || {},
          }))
          // 过滤掉 FixedInvestmentStrategy 相关的资金策略
          .filter((strategy: any) => 
            !strategy.strategy_name.includes('FixedInvestmentStrategy') &&
            !strategy.strategy_name.includes('FixedInvestment')
          );
        setCapitalStrategies(strategies);
      } catch (error) {
        console.error('Error loading capital strategies:', error);
        if ((error as ApiError).status === 401) {
          setErrors((prev) => ({
            ...prev,
            form: '❌ 请登录后再操作',
            api: errorMsgWithLoginLink as any,
          }));
        } else {
          setErrors((prev) => ({
            ...prev,
            form: '❌ 加载资金策略失败，请刷新页面重试',
          }));
        }
      } finally {
        if (mode === 'create') {
          setLoading(false); // 创建模式下结束加载
        }
      }
    };
    loadCapitalStrategies();
  }, [mode]);

  // 加载策略模板
  useEffect(() => {
    const loadStrategyTemplates = async () => {
      try {
        // 从本地 JSON 文件加载策略模板
        const templates = await import('../utils/strategyTemplates.json');
        const templateMap: Record<string, any> = {};
        const templateDescriptions: Record<string, string> = {};
        templates.default.forEach((template: any) => {
          templateMap[template.name] = template.dslJson;
          templateDescriptions[template.name] = template.description || '';
        });
        setStrategyTemplates({ templates: templateMap, descriptions: templateDescriptions });
      } catch (error) {
        console.error('Error loading strategy templates:', error);
        setErrors((prev) => ({
          ...prev,
          form: '❌ 加载策略模板失败，请刷新页面重试',
        }));
      }
    };
    loadStrategyTemplates();
  }, []);

  // 日期范围限制
  const getMinDate = () => {
    const date = new Date('2016-01-01');
    return date;
  };

  const getMaxDate = () => {
    const date = new Date();
    date.setDate(date.getDate() - 5);
    return date;
  };

  // 处理日期变化
  const handleDateChange = (date: Date | null) => {
    setStartDate(date);
  };

  // 根据市场设置货币并清空标的池
  const handleMarketChange = (newMarket: string) => {
    setMarket(newMarket);
    // 根据市场自动设置对应的货币
    switch (newMarket) {
      case 'US':
        setCurrency('USD');
        break;
      case 'China':
        setCurrency('CNY');
        break;
      case 'Crypto':
        setCurrency('USDT');
        break;
      default:
        setCurrency('USD');
    }
    // 清空标的池
    setSymbols([]);
    setSymbolQuery('');
    setSymbolSuggestions([]);
  };

  // 处理标的搜索
  const handleSymbolSearch = useCallback(
    async (query: string) => {
      if (!query.trim() || isComposing) return;

      try {
        const results = await searchSymbols(query, currency);
        setSymbolSuggestions(results);
      } catch (error) {
        console.error('Error searching symbols:', error);
        if ((error as ApiError).status === 401) {
          setErrors((prev) => ({
            ...prev,
            api: errorMsgWithLoginLink as any,
          }));
        } else {
          setErrors((prev) => ({
            ...prev,
            symbol: `❌ 搜索标的失败：${(error as Error).message}`,
          }));
        }
      }
    },
    [isComposing, currency]
  );

  useEffect(() => {
    handleSymbolSearch(symbolQuery);
  }, [symbolQuery, handleSymbolSearch]);

  // 处理标的选择
  const handleSymbolSelect = (symbol: PortfolioSymbol) => {
    if (symbols.length >= 10) {
      setErrors((prev) => ({
        ...prev,
        symbol: '❌ 不能选择超过10个标的',
      }));
      return;
    }
    setErrors((prev) => ({ ...prev, symbol: '' })); // 清除错误
    setSymbols([...symbols, symbol]);
    setSymbolQuery('');
    setSymbolSuggestions([]);
  };

  // 处理标的移除
  const handleRemoveSymbol = (symbolToRemove: string | PortfolioSymbol) => {
    setSymbols(
      symbols.filter(
        (s) =>
          (typeof s === 'string' ? s : s.symbol) !==
          (typeof symbolToRemove === 'string'
            ? symbolToRemove
            : symbolToRemove.symbol)
      )
    );
  };

  // 处理资金策略选择
  const handleCapitalStrategyChange = (strategyName: string) => {
    const selectedStrategy = capitalStrategies.find(
      (s) => s.strategy_name === strategyName
    );
    setCapitalStrategy(selectedStrategy || null);
    // 重置参数值状态
    setCapitalStrategyParamValues({});
  };

  // 处理策略模板选择
  const handleStrategyTemplateChange = (templateName: string) => {
    if (!templateName) {
      // 如果选择空值，清空JSON内容
      setDslJson({});
      setSelectedTemplate('');
      setSelectedTemplateDescription('');
      return;
    }

    const template = strategyTemplates?.templates?.[templateName];
    const description = strategyTemplates?.descriptions?.[templateName] || '';
    if (template) {
      setDslJson(template);
      setSelectedTemplate(templateName);
      setSelectedTemplateDescription(description);
    } else {
      setSelectedTemplate('');
      setSelectedTemplateDescription('');
    }
  };

  // 处理资金策略参数变化
  const handleCapitalStrategyParamChange = (
    key: string,
    value: number | string
  ) => {
    if (!capitalStrategy) {
      return;
    }

    const param =
      typeof capitalStrategy.strategy_parameters[key] === 'string'
        ? JSON.parse(capitalStrategy.strategy_parameters[key] as string)
        : capitalStrategy.strategy_parameters[key];

    // 只对数字类型的参数进行范围验证，跳过空字符串
    if (typeof value === 'number') {
      if (param.gt !== undefined && value <= param.gt) {
        setErrors((prev) => ({
          ...prev,
          parameter: `❌ 参数 ${key} 必须大于 ${param.gt}`,
        }));
        return;
      }
      if (param.le !== undefined && value > param.le) {
        setErrors((prev) => ({
          ...prev,
          parameter: `❌ 参数 ${key} 必须小于等于 ${param.le}`,
        }));
        return;
      }
    }
    // 如果 value 是空字符串，跳过验证，允许用户清空输入框

    // 清除参数错误
    setErrors((prev) => ({ ...prev, parameter: '' }));

    // 更新单独的参数值状态，确保UI能正确更新
    setCapitalStrategyParamValues(prev => ({
      ...prev,
      [key]: value
    }));

    // 同时更新capitalStrategy中的参数值（用于提交时获取）
    const updatedParam = {
      ...param,
      default: value,
    };

    const updatedParams = {
      ...capitalStrategy.strategy_parameters,
      [key]: updatedParam,
    };

    // 创建全新的capitalStrategy对象，确保React能检测到变化
    const newCapitalStrategy = {
      strategy_id: capitalStrategy.strategy_id,
      strategy_name: capitalStrategy.strategy_name,
      strategy_description: capitalStrategy.strategy_description,
      strategy_parameters: updatedParams,
    };
    
    // 使用函数式更新确保状态更新
    setCapitalStrategy(() => newCapitalStrategy);
  };

  // 应用加载的资金策略参数
  useEffect(() => {
    if (capitalStrategy && Object.keys(loadedCapitalStrategyParams).length > 0) {
      // 直接设置参数值状态
      setCapitalStrategyParamValues(loadedCapitalStrategyParams);
      
      // 同时更新capitalStrategy中的参数
      Object.entries(loadedCapitalStrategyParams).forEach(([key, value]) => {
        handleCapitalStrategyParamChange(key, value);
      });
      // 清空已应用的参数
      setLoadedCapitalStrategyParams({});
    }
  }, [capitalStrategy, loadedCapitalStrategyParams]);

  // Load schema with proper error handling
  useEffect(() => {
    // 在开发环境或localhost下使用代理，避免CORS问题
    const isLocalhost = typeof window !== 'undefined' && 
      (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
    const isProduction = process.env.NODE_ENV === 'production' && !isLocalhost;
    
    const schemaUrl = isProduction
      ? 'https://media.i365.tech/myinvestpilot/primitives_schema.json'
      : '/api/proxy-schema/primitives_schema.json';

    const loadSchema = async () => {
      try {
        const res = await fetch(schemaUrl);

        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }

        const data = await res.json();

        if (!data || typeof data !== 'object') {
          throw new Error('Invalid schema format');
        }

        if (!data.$schema || !data.definitions) {
          throw new Error('Invalid schema format: missing required fields');
        }

        setSchema(data as JSONSchema);
        setErrors(initialErrors);
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Failed to load strategy schema';
        setErrors({
          ...initialErrors,
          schema: errorMessage,
        });
      }
    };

    loadSchema();
  }, []);

    // Validate DSL JSON against schema
  useEffect(() => {
    if (!schema || !ajv || !Object.keys(dslJson).length) {
      setErrors((prev) => ({ ...prev, validation: '' }));
      return;
    }

    try {
      // 使用 StrategyDefinitionForAI 作为根 Schema
      const rootSchema = {
        ...schema,
        ...schema.definitions.StrategyDefinitionForAI,
      };
      const validate = ajv.compile(rootSchema);
      const valid = validate(dslJson);

      if (!valid) {
        const errorMessage = ajv.errorsText(validate.errors);
        setErrors((prev) => ({
          ...prev,
          validation: errorMessage,
        }));
      } else {
        setErrors((prev) => ({ ...prev, validation: '' }));
      }
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        validation:
          'Schema validation failed. Please check your strategy definition.',
      }));
    }
  }, [dslJson, schema, ajv]);

    // JSON Schema 验证函数
  const validateJsonSchema = (data: any) => {
    if (!schema || !ajv || !Object.keys(data).length) {
      return true; // 如果没有 schema 或数据为空，跳过验证
    }

    try {
      // 使用 StrategyDefinitionForAI 作为根 Schema
      const rootSchema = {
        ...schema,
        ...schema.definitions.StrategyDefinitionForAI,
      };
      const validate = ajv.compile(rootSchema);
      const valid = validate(data);

      if (!valid) {
        const errorMessage = ajv.errorsText(validate.errors);
        setErrors((prev) => ({
          ...prev,
          validation: errorMessage,
        }));
        return false;
      }
      setErrors((prev) => ({ ...prev, validation: '' }));
      return true;
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        validation:
          'Schema validation failed. Please check your strategy definition.',
      }));
      return false;
    }
  };

    // 处理 JSON 编辑器数据更新
  const handleJsonUpdate = (data: any) => {
    try {
      // 清除之前的解析错误
      setErrors((prev) => ({ ...prev, parsing: '' }));

      // 验证 JSON Schema
      const isValid = validateJsonSchema(data);

      if (isValid) {
        setDslJson(data);
        setErrors((prev) => ({ ...prev, parsing: '', validation: '' }));
      }
      // 如果验证失败，错误信息已经在 validateJsonSchema 中设置了
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        parsing: '处理 JSON 数据时发生错误，请检查数据格式',
      }));
    }
  };

  // 处理文本模式的 JSON 编辑
  const handleTextJsonChange = (value: string) => {
    try {
      // 清除之前的解析错误
      setErrors((prev) => ({ ...prev, parsing: '' }));

      if (!value.trim()) {
        return;
      }

      const parsedData = JSON.parse(value);
      handleJsonUpdate(parsedData);
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        parsing: '❌ JSON 格式错误，请检查语法是否正确',
      }));
    }
  };

  const handleAiGenerate = async () => {
    if (!nlInput.trim()) return;

    setGenerating(true);
    setErrors((prev) => ({ ...prev, submission: '' }));

    try {
      const response = await generateStrategyDSL({ userInput: nlInput });
      
      // 检查响应格式并提取tradeStrategy
      if (response.success && response.tradeStrategy) {
        // 将AI返回的tradeStrategy包装成正确的schema格式
        // 只包含AI实际返回的字段，不设置空值
        const wrappedStrategy: any = {};
        
        // 如果AI返回了market_indicators，则包含它
        if (response.tradeStrategy.market_indicators) {
          wrappedStrategy.market_indicators = response.tradeStrategy.market_indicators;
        }
        
        // 构建trade_strategy部分，只包含存在的字段
        const tradeStrategy: any = {};
        if (response.tradeStrategy.indicators) {
          tradeStrategy.indicators = response.tradeStrategy.indicators;
        }
        if (response.tradeStrategy.signals) {
          tradeStrategy.signals = response.tradeStrategy.signals;
        }
        if (response.tradeStrategy.outputs) {
          tradeStrategy.outputs = response.tradeStrategy.outputs;
        }
        
        // 只有当trade_strategy有内容时才添加
        if (Object.keys(tradeStrategy).length > 0) {
          wrappedStrategy.trade_strategy = tradeStrategy;
        }
        
        setDslJson(wrappedStrategy);
        // 清除之前的错误
        setErrors((prev) => ({ ...prev, submission: '', parsing: '', validation: '' }));
      } else {
        throw new Error('AI生成的策略格式不正确');
      }
    } catch (error) {
      if ((error as ApiError).status === 401) {
        setErrors((prev) => ({
          ...prev,
          submission: '❌ 请登录后再操作',
          api: errorMsgWithLoginLink as any,
        }));
      } else {
        setErrors((prev) => ({
          ...prev,
          submission: `❌ AI策略生成失败：${(error as Error).message}。请重试或手动定义策略。`,
        }));
      }
    } finally {
      setGenerating(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (errors.validation || errors.parsing) return;

    // 表单验证
    let formError = '';
    if (!name.trim()) {
      formError = '❌ 请输入组合名称';
    } else if (!startDate) {
      formError = '❌ 请选择建仓时间';
    } else if (!market) {
      formError = '❌ 请选择市场';
    } else if (!capitalStrategy) {
      formError = '❌ 请选择资金策略';
    } else {
      // 验证资金策略参数
      const paramEntries = Object.entries(capitalStrategy.strategy_parameters);
      const invalidParam = paramEntries.find(([key, paramConfig]) => {
        const param = typeof paramConfig === 'string' ? JSON.parse(paramConfig) : paramConfig;
        const currentValue = capitalStrategyParamValues[key] ?? param.default;
        
        // 检查是否为空
        if (currentValue === '' || currentValue === null || currentValue === undefined) {
          formError = `❌ 资金策略参数 ${key} 不能为空`;
          return true;
        }
        
        // 数值范围验证
        if (typeof currentValue === 'number') {
          if (param.gt !== undefined && currentValue <= param.gt) {
            formError = `❌ 参数 ${key} 必须大于 ${param.gt}`;
            return true;
          }
          if (param.le !== undefined && currentValue > param.le) {
            formError = `❌ 参数 ${key} 必须小于等于 ${param.le}`;
            return true;
          }
        }
        return false;
      });
      
      if (invalidParam) {
        // formError 已在上面设置
      }
    }
    
    if (formError) {
      setErrors((prev) => ({ ...prev, form: formError }));
      return;
    }
    
    if (!updateTime) {
      formError = '❌ 请设置组合数据更新时间';
    } else if (symbols.length === 0) {
      formError = '❌ 请选择至少一个标的';
    } else if (!Object.keys(dslJson).length) {
      formError = '❌ 请选择策略模板或定义交易策略';
    } else {
      // 验证交易策略不能为空
      const tradeStrategy = dslJson.trade_strategy || dslJson;
      if (!tradeStrategy || Object.keys(tradeStrategy).length === 0) {
        formError = '❌ 交易策略不能为空对象，请定义有效的交易策略';
      }
    }

    if (formError) {
      setErrors((prev) => ({ ...prev, form: formError }));
      return;
    }

    // 清除表单错误
    setErrors((prev) => ({ ...prev, form: '' }));

    setSubmitting(true);
    setErrors((prev) => ({ ...prev, submission: '' }));

    try {
      const startDateStr = startDate!.toISOString().split('T')[0] || '';

      // Convert strategy parameters to the expected format
      const convertedParams: Record<string, string | number> = {};
      Object.entries(capitalStrategy!.strategy_parameters).forEach(([key, param]) => {
        convertedParams[key] = param.default;
      });

      // dslJson 已经通过 schema 校验，提取正确的结构
      const strategyDefinition: any = {
        trade_strategy: dslJson.trade_strategy || dslJson,
        capital_strategy: {
          name: capitalStrategy!.strategy_name,
          params: convertedParams,
        },
      };

      // 处理可选的 market_indicators
      if (dslJson.market_indicators) {
        strategyDefinition.market_indicators = dslJson.market_indicators;
      }

      const portfolioData = {
        name,
        description,
        strategy_definition: strategyDefinition,
        symbols: symbols.map((s) => (typeof s === 'string' ? s : s.symbol)),
        start_date: startDateStr,
        currency,
        market,
        commission: commissionRate,
        update_time: updateTime,
      };

      if (mode === 'update' && initialCode) {
        await updatePortfolio(initialCode, portfolioData);
        await trackEvent('UserUpdatePortfolio', { portfolio: initialCode });
      } else {
        await createPortfolio(portfolioData);
        await trackEvent('UserCreatePortfolio', {});
      }

      setIsSubmitSuccessful(true);
      
      // Call onSubmit callback
      if (onSubmit) {
        onSubmit();
      }

      // 2秒后跳转到我的页面
      setTimeout(() => {
        router.push('/me');
      }, 2000);

      // 如果不是更新模式，重置表单
      if (mode !== 'update') {
        setName('');
        setDescription('');
        setCapitalStrategy(null);
        setDslJson({});
        setNlInput('');
        setSymbols([]);
        setStartDate(null);
        setSelectedTemplate('');
        setErrors(initialErrors);
      }
    } catch (error) {
      if ((error as ApiError).status === 401) {
        setErrors((prev) => ({
          ...prev,
          submission: '❌ 请登录后再操作',
          api: errorMsgWithLoginLink as any,
        }));
      } else {
        setErrors((prev) => ({
          ...prev,
          submission: `❌ ${mode === 'update' ? '更新' : '创建'}投资组合失败：${(error as Error).message}`,
        }));
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleValuesChange = (changedValues: any, allValues: any) => {
    if (onValuesChange) {
      onValuesChange(changedValues, allValues);
    }
  };

  // 显示初始加载动画（更新模式下加载数据时）
  if (loading && !isSubmitSuccessful) {
    return (
      <div className="flex justify-center items-center h-screen">
        <InfinitySpin color="#4F46E5" />
      </div>
    );
  }

  // 显示提交中动画
  if (submitting && !isSubmitSuccessful) {
    return (
      <div className="flex justify-center items-center h-screen">
        <InfinitySpin color="#4F46E5" />
      </div>
    );
  }

  if (isSubmitSuccessful) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md text-center">
        <h2 className="text-2xl font-bold text-green-600 mb-4">提交成功</h2>
        <p className="text-gray-600">
          您的投资组合已成功{mode === 'update' ? '更新' : '创建'}
          。正在跳转到我的页面...
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-50 rounded-xl shadow-lg border border-gray-200">
      <Form form={form} layout="vertical" onValuesChange={handleValuesChange}>
        {/* Show schema loading error */}
        {errors.schema && (
          <div className="text-red-500 mb-4">{errors.schema}</div>
        )}

        {/* Show schema loading state */}
        {!schema && !errors.schema && (
          <div className="text-gray-500 mb-4">Loading schema...</div>
        )}

        {/* Show form when schema is loaded */}
        {schema && (
          <div className="space-y-8">
            {/* 显示加载错误 */}
            {errors.loading && (
              <div className="mb-4 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
                <div className="flex items-start space-x-2">
                  <div className="text-red-500 text-lg">⚠️</div>
                  <div>
                    <div className="font-bold text-red-800 mb-1">加载错误</div>
                    <div className="text-red-700">{errors.loading}</div>
                  </div>
                </div>
              </div>
            )}

            {/* 通用错误显示区域 */}
            {(errors.form || errors.symbol || errors.parameter) && (
              <div className="mb-4 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
                <div className="flex items-start space-x-2">
                  <div className="text-red-500 text-lg">⚠️</div>
                  <div>
                    <div className="font-bold text-red-800 mb-1">
                      表单验证错误
                    </div>
                    {errors.form && (
                      <div className="text-red-700 mb-1">{errors.form}</div>
                    )}
                    {errors.symbol && (
                      <div className="text-red-700 mb-1">{errors.symbol}</div>
                    )}
                    {errors.parameter && (
                      <div className="text-red-700 mb-1">
                        {errors.parameter}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 显示API错误 */}
            {errors.api && (
              <div className="mb-4 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
                <div className="flex items-start space-x-2">
                  <div className="text-red-500 text-lg">🔐</div>
                  <div>
                    <div className="font-bold text-red-800 mb-1">认证错误</div>
                    <div className="text-red-700">{errors.api}</div>
                  </div>
                </div>
              </div>
            )}

            {/* 显示提交错误 */}
            {errors.submission && (
              <div className="mb-4 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
                <div className="flex items-start space-x-2">
                  <div className="text-red-500 text-lg">❌</div>
                  <div>
                    <div className="font-bold text-red-800 mb-1">提交失败</div>
                    <div className="text-red-700">{errors.submission}</div>
                  </div>
                </div>
              </div>
            )}

            {/* 基本信息分组 */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  📝 基本信息
                </h3>
                <p className="text-sm text-green-600">
                  设置投资组合的基本信息和描述
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    组合名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                    placeholder="请输入投资组合名称"
                    required
                  />
                  {!name && (
                    <div className="mt-1 text-sm text-gray-500">
                      <span className="text-red-500">⚠️</span> 请输入组合名称
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    组合描述
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                    placeholder="描述您的投资组合策略和目标（可选）"
                  />
                </div>
              </div>
            </div>

            {/* 市场配置分组 */}
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-purple-800 mb-2">
                  🌍 市场配置
                </h3>
                <p className="text-sm text-purple-600">
                  选择交易市场、建仓时间和交易费用设置
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    建仓时间 <span className="text-red-500">*</span>
                  </label>
                  <DatePicker
                    selected={startDate}
                    onChange={handleDateChange}
                    maxDate={getMaxDate()}
                    minDate={getMinDate()}
                    dateFormat="yyyy-MM-dd"
                    customInput={<CustomInput />}
                    placeholderText="选择建仓日期"
                    required
                    showYearDropdown
                    scrollableYearDropdown
                    yearDropdownItemNumber={15}
                    showMonthDropdown
                    dropdownMode="select"
                  />
                  {!startDate && (
                    <div className="mt-1 text-sm text-gray-500">
                      <span className="text-red-500">⚠️</span> 请选择建仓时间
                    </div>
                  )}
                  <p className="mt-1 text-sm text-gray-500">
                    请选择建仓时间，可选范围为2016年1月1日至5天前
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    交易市场 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={market}
                    onChange={(e) => handleMarketChange(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                    required
                  >
                    <option value="">请选择交易市场</option>
                    <option value="US">🇺🇸 美国市场</option>
                    <option value="China">🇨🇳 中国市场</option>
                    <option value="Crypto">₿ 加密货币</option>
                  </select>
                  {!market && (
                    <div className="mt-1 text-sm text-gray-500">
                      <span className="text-red-500">⚠️</span> 请选择交易市场
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    基础货币
                  </label>
                  <input
                    type="text"
                    value={currency}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm bg-gray-100 text-gray-700"
                    readOnly
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    基础货币将根据选择的交易市场自动设置
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    交易佣金费率 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    value={commissionRate}
                    onChange={(e) => setCommissionRate(Number(e.target.value))}
                    step="0.001"
                    min="0"
                    max="1"
                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                    placeholder="0.001"
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    设置每笔交易的佣金费率，例如 0.001 表示 0.1%
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    组合数据更新时间 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="time"
                    value={updateTime}
                    onChange={(e) => setUpdateTime(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                    required
                  />
                  {!updateTime && (
                    <div className="mt-1 text-sm text-gray-500">
                      <span className="text-red-500">⚠️</span> 请设置组合数据更新时间
                    </div>
                  )}
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="text-sm text-blue-800">
                      <div className="font-medium mb-1">⏰ 时区说明</div>
                      <p className="text-blue-700">
                        • 更新时间采用 <strong>UTC标准时间</strong>（格林威治时间）<br/>
                        • 中国时间 = UTC时间 + 8小时<br/>
                        • 例如：若希望北京时间早上8点更新并收到邮件，请设置为 <strong>00:00</strong>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 投资标的分组 */}
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-orange-800 mb-2">
                  📊 投资标的池 <span className="text-red-500">*</span>
                </h3>
                <p className="text-sm text-orange-600">
                  选择要投资的股票、基金或其他金融产品（最多10个）
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    搜索并添加标的
                  </label>
                  <SymbolSearch
                    symbolQuery={symbolQuery}
                    setSymbolQuery={setSymbolQuery}
                    onCompositionStart={() => setIsComposing(true)}
                    onCompositionEnd={() => setIsComposing(false)}
                  />
                  {symbolSuggestions.length > 0 && (
                    <ul className="mt-2 border border-gray-300 bg-white rounded-md shadow-sm max-h-40 overflow-y-auto">
                      {symbolSuggestions.map((symbol) => (
                        <li
                          key={symbol.symbol}
                          onClick={() => handleSymbolSelect(symbol)}
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        >
                          {symbol.name} ({symbol.symbol})
                          {symbol.market && (
                            <span className="text-gray-500 ml-2">
                              {symbol.market}
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    已选择的标的 ({symbols.length}/10)
                  </label>
                  {symbols.length > 0 ? (
                    <div className="space-y-2">
                      {symbols.map((symbol) => (
                        <div
                          key={
                            typeof symbol === 'string' ? symbol : symbol.symbol
                          }
                          className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md shadow-sm"
                        >
                          <span className="font-medium">
                            {typeof symbol === 'string'
                              ? symbol
                              : `${symbol.name} (${symbol.symbol})`}
                          </span>
                          <button
                            type="button"
                            onClick={() => handleRemoveSymbol(symbol)}
                            className="text-red-500 hover:text-red-700 px-2 py-1 rounded-md hover:bg-red-50 transition-colors"
                          >
                            移除
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg">
                      <div className="text-gray-400 text-lg mb-2">📈</div>
                      <p className="text-gray-500 text-sm">
                        还未选择任何投资标的，请在上方搜索并添加
                      </p>
                    </div>
                  )}
                  {symbols.length === 0 && (
                    <div className="mt-1 text-sm text-gray-500">
                      <span className="text-red-500">⚠️</span>{' '}
                      请选择至少一个投资标的
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 资金策略分组 */}
            <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">
                  💰 资金管理策略 <span className="text-red-500">*</span>
                </h3>
                <p className="text-sm text-indigo-600">
                  选择资金分配和管理方式，控制投资风险
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    策略类型
                  </label>
                  <select
                    value={capitalStrategy?.strategy_name || ''}
                    onChange={(e) =>
                      handleCapitalStrategyChange(e.target.value)
                    }
                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                    required
                  >
                    <option value="">请选择资金管理策略</option>
                    {capitalStrategies.map((strategy) => (
                      <option
                        key={strategy.strategy_id}
                        value={strategy.strategy_name}
                      >
                        {strategy.strategy_name}
                      </option>
                    ))}
                  </select>
                  {!capitalStrategy && (
                    <div className="mt-1 text-sm text-gray-500">
                      <span className="text-red-500">⚠️</span>{' '}
                      请选择资金管理策略
                    </div>
                  )}
                </div>

                {capitalStrategy && (
                  <div className="mt-4 p-4 bg-white rounded-md border border-gray-200 shadow-sm">
                    <h4 className="font-medium mb-2 text-indigo-700">
                      策略说明
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">
                      {capitalStrategy.strategy_description}
                    </p>
                    <h4 className="font-medium mb-3 text-indigo-700">
                      参数配置
                    </h4>
                    <div className="space-y-4">
                      {Object.entries(capitalStrategy.strategy_parameters).map(
                        ([key, value]) => {
                          const param =
                            typeof value === 'string'
                              ? JSON.parse(value)
                              : value;
                          return (
                            <div
                              key={key}
                              className="border-b border-gray-200 pb-4 last:border-0"
                            >
                              <div className="flex items-center justify-between mb-2">
                                <label className="block text-sm font-medium text-gray-700">
                                  {key}
                                </label>
                                <span className="text-sm text-gray-500">
                                  {param.description}
                                </span>
                              </div>
                              <div className="flex items-center space-x-4">
                                {key === 'investment_frequency' ? (
                                  <select
                                    value={capitalStrategyParamValues[key] ?? param.default}
                                    onChange={(e) =>
                                      handleCapitalStrategyParamChange(
                                        key,
                                        e.target.value
                                      )
                                    }
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                                  >
                                    <option value="y">年</option>
                                    <option value="m">月</option>
                                  </select>
                                ) : (
                                  <input
                                    type="number"
                                    value={capitalStrategyParamValues[key] ?? param.default}
                                    onChange={(e) =>
                                      handleCapitalStrategyParamChange(
                                        key,
                                        e.target.value === '' ? '' : Number(e.target.value)
                                      )
                                    }
                                    onBlur={(e) => {
                                      // 失去焦点时，如果是空值，设为默认值或最小值
                                      if (e.target.value === '') {
                                        let defaultValue = param.default;
                                        if (defaultValue === undefined) {
                                          defaultValue = param.gt !== undefined ? param.gt + 0.001 : 0;
                                        }
                                        handleCapitalStrategyParamChange(key, defaultValue);
                                      }
                                    }}
                                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                                  />
                                )}
                                <div className="text-sm text-gray-500 whitespace-nowrap">
                                  {(() => {
                                    if (
                                      param.gt !== undefined &&
                                      param.le !== undefined
                                    ) {
                                      return (
                                        <span>
                                          {param.gt} &lt; x ≤ {param.le}
                                        </span>
                                      );
                                    }
                                    if (param.gt !== undefined) {
                                      return <span>x &gt; {param.gt}</span>;
                                    }
                                    if (param.le !== undefined) {
                                      return <span>x ≤ {param.le}</span>;
                                    }
                                    return null;
                                  })()}
                                </div>
                              </div>
                            </div>
                          );
                        }
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">
                  交易策略定义 <span className="text-red-500">*</span>
                </h3>
                <p className="text-sm text-blue-600 mb-3">
                  定义您的交易策略逻辑，包括指标计算、信号生成和买卖条件
                </p>
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                  <div className="flex items-start space-x-2">
                    <div className="text-amber-500 text-lg">💡</div>
                    <div className="text-sm text-amber-700">
                      <div className="font-medium mb-1">策略编写说明</div>
                      <p>
                        原语策略采用结构化的DSL（领域特定语言）定义，编写相对复杂。
                        建议通过<strong>策略模板</strong>或<strong>AI生成</strong>的方式快速创建策略，
                        详细的语法文档和高级用法请参考<a href="https://docs.myinvestpilot.com/docs/primitives/getting-started" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:text-blue-700 underline">原语策略文档</a>。
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    📋 策略模板
                  </label>
                  <select
                    value={selectedTemplate}
                    onChange={(e) =>
                      handleStrategyTemplateChange(e.target.value)
                    }
                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                  >
                    <option value="">请选择交易策略模板</option>
                    {Object.keys(strategyTemplates?.templates || {}).map((templateName) => (
                      <option key={templateName} value={templateName}>
                        {templateName}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-sm text-gray-500">
                    选择一个预设的策略模板快速开始
                  </p>
                  {selectedTemplateDescription && (
                    <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="text-sm text-blue-800">
                        <strong>策略说明：</strong>
                      </div>
                      <div className="mt-1 text-sm text-blue-700">
                        {selectedTemplateDescription}
                      </div>
                    </div>
                  )}
                </div>

                <div className="border-t border-blue-200 pt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    🤖 AI 策略生成
                  </label>
                  <textarea
                    value={nlInput}
                    onChange={(e) => setNlInput(e.target.value)}
                    rows={3}
                    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                    placeholder="用自然语言描述您的交易策略，例如：当股价突破20日均线时买入，跌破时卖出..."
                  />
                  <button
                    type="button"
                    onClick={handleAiGenerate}
                    disabled={generating || !nlInput.trim()}
                    className="mt-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                  >
                    {generating ? '生成中…' : '🚀 AI 生成策略'}
                  </button>
                  <p className="mt-1 text-sm text-gray-500">
                    使用 AI 根据自然语言描述自动生成策略定义（
                    <a
                      href={getAIStrategyPromptVersionUrl('v1.0')}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:text-blue-700 underline"
                    >
                      查看AI提示词
                    </a>
                    ）
                  </p>
                </div>

                <div className="border-t border-blue-200 pt-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      ⚙️ 高级自定义
                    </label>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">编辑模式:</span>
                      <button
                        type="button"
                        onClick={() => setJsonEditorMode('visual')}
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          jsonEditorMode === 'visual'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        可视化
                      </button>
                      <button
                        type="button"
                        onClick={() => setJsonEditorMode('text')}
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          jsonEditorMode === 'text'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        文本
                      </button>
                    </div>
                  </div>

                  <div className="mt-1">
                    {jsonEditorMode === 'visual' ? (
                      <JsonEditor
                        data={dslJson}
                        setData={handleJsonUpdate}
                        rootName="strategy"
                        enableClipboard={true}
                        showArrayIndices={true}
                        showCollectionCount={true}
                        showStringQuotes={true}
                        stringTruncate={100}
                        minWidth="100%"
                        maxWidth="100%"
                        onError={(error) => {
                          console.error('JSON Editor Error:', error);
                          // 处理 JSON 编辑器的错误
                          if (error.error?.code === 'INVALID_JSON') {
                            setErrors((prev) => ({
                              ...prev,
                              parsing:
                                '❌ 无效的 JSON 格式！请检查语法是否正确（如括号匹配、逗号使用、引号配对等）',
                            }));
                          } else if (error.error?.code === 'UPDATE_ERROR') {
                            setErrors((prev) => ({
                              ...prev,
                              parsing:
                                '❌ 更新失败！请检查输入的数据格式是否正确',
                            }));
                          } else {
                            setErrors((prev) => ({
                              ...prev,
                              parsing: `❌ 编辑器错误：${
                                error.error?.message || '未知错误'
                              }`,
                            }));
                          }
                        }}
                      />
                    ) : (
                      <textarea
                        value={JSON.stringify(dslJson, null, 2)}
                        onChange={(e) => handleTextJsonChange(e.target.value)}
                        rows={20}
                        className="w-full p-3 border border-gray-300 rounded-md bg-white font-mono text-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                        placeholder="在此输入 JSON 策略定义..."
                      />
                    )}
                  </div>
                  {errors.validation && (
                    <div className="mt-2 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
                      <div className="flex items-start space-x-2">
                        <div className="text-red-500 text-lg">🚫</div>
                        <div>
                          <div className="font-bold text-red-800 mb-1">
                            Schema 验证错误
                          </div>
                          <div className="text-red-700 whitespace-pre-wrap">
                            {errors.validation}
                          </div>
                          <div className="mt-2 text-xs text-red-600">
                            💡 请确保 JSON
                            结构符合策略定义的要求，检查必需字段和数据类型
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  {errors.parsing && (
                    <div className="mt-2 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
                      <div className="flex items-start space-x-2">
                        <div className="text-red-500 text-lg">⚠️</div>
                        <div>
                          <div className="font-bold text-red-800 mb-1">
                            JSON 编辑错误
                          </div>
                          <div className="text-red-700">{errors.parsing}</div>
                          <div className="mt-2 text-xs text-red-600">
                            💡 请检查 JSON
                            语法：确保所有括号匹配、逗号正确使用、字符串用引号包围
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <p className="mt-2 text-sm text-gray-500">
                    直接编辑策略的 JSON 定义，包括指标、信号和输出配置。 参考{' '}
                    <a
                      href="https://media.i365.tech/myinvestpilot/primitives_schema.json"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:text-blue-700 underline"
                    >
                      JSON Schema
                    </a>{' '}
                    了解完整的配置格式。
                  </p>
                  <div className="mt-2 text-xs text-gray-400">
                    💡 提示：编辑 JSON
                    文本时，请确保语法正确（括号匹配、逗号使用、引号配对等）
                  </div>
                </div>
              </div>
            </div>

            <button
              type="submit"
              onClick={handleSubmit}
              disabled={submitting || generating}
              className="w-full mt-6 bg-blue-500 text-white py-3 px-4 rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
            >
              {(() => {
                if (submitting) {
                  return mode === 'update' ? '更新中...' : '创建中...';
                }
                return mode === 'update' ? '更新组合' : '创建组合';
              })()}
            </button>
          </div>
        )}
      </Form>
    </div>
  );
};

export default PrimitiveStrategyForm;
