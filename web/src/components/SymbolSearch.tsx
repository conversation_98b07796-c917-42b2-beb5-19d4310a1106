import React from 'react';

import { MagnifyingGlassIcon } from '@heroicons/react/24/solid';

interface SymbolSearchProps {
  symbolQuery: string;
  setSymbolQuery: (value: string) => void;
  onCompositionStart: () => void;
  onCompositionEnd: () => void;
}

const SymbolSearch: React.FC<SymbolSearchProps> = ({
  symbolQuery,
  setSymbolQuery,
  onCompositionStart,
  onCompositionEnd,
}) => {
  return (
    <div className="mb-4">
      <label
        htmlFor="symbolSearch"
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        搜索标的
      </label>
      <p className="text-sm text-gray-500 mb-2">
        只能选择当前市场支持的标的，比如在美股市场能选择美股标的（如股票或ETF）与主流加密币标的，在中国市场只能选择A股标的
      </p>
      <div className="relative rounded-md shadow-sm">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon
            className="h-5 w-5 text-gray-400"
            aria-hidden="true"
          />
        </div>
        <input
          type="text"
          id="symbolSearch"
          value={symbolQuery}
          onChange={(e) => setSymbolQuery(e.target.value)}
          onCompositionStart={onCompositionStart}
          onCompositionEnd={onCompositionEnd}
          className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 rounded-md"
          placeholder="输入标的名称或代码"
        />
      </div>
    </div>
  );
};

export default SymbolSearch;
