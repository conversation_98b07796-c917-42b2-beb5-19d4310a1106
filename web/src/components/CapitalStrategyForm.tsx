import React, { useEffect, useState } from "react";
import { Form, InputNumber, Select, Typography, Card } from "antd";
import type { FormInstance } from "antd/es/form";
import type { CapitalStrategy } from "../../types/types";
import { getOfficialPortfolios } from "../utils/api";

const { Text } = Typography;

interface CapitalStrategyFormProps {
  form: FormInstance;
  onValuesChange?: (changedValues: any, allValues: any) => void;
}

const CapitalStrategyForm: React.FC<CapitalStrategyFormProps> = ({
  form,
  onValuesChange,
}) => {
  const [strategies, setStrategies] = useState<CapitalStrategy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStrategies = async () => {
      try {
        const data = await getOfficialPortfolios("capital_strategy");
        // Convert the grouped portfolios to capital strategies
        const strategies = data.map((item: any) => ({
          strategy_id: item.strategy_id,
          strategy_name: item.strategy_name,
          strategy_description: item.strategy_description,
          strategy_parameters: item.strategy_parameters || {},
        }));
        setStrategies(strategies);
        setLoading(false);
      } catch (error) {
        console.error("Failed to fetch capital strategies:", error);
        setError(
          error instanceof Error ? error.message : "Failed to load strategies"
        );
        setLoading(false);
      }
    };

    fetchStrategies();
  }, []);

  if (loading) {
    return <div>Loading capital strategies...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (strategies.length === 0) {
    return <div>No capital strategies found</div>;
  }

  // 使用第一个策略作为默认策略
  const strategy = strategies[0];

  if (!strategy) {
    return <div>No strategy available</div>;
  }

  return (
    <Card title="资金策略配置">
      <Text>{strategy.strategy_description}</Text>

      <Form
        form={form}
        layout="vertical"
        onValuesChange={onValuesChange}
        initialValues={
          strategy.strategy_parameters
            ? Object.entries(strategy.strategy_parameters).reduce(
                (acc, [key, param]) => ({
                  ...acc,
                  [key]: param.default,
                }),
                {}
              )
            : {}
        }
      >
        {strategy.strategy_parameters &&
          Object.entries(strategy.strategy_parameters).map(([key, param]) => {
            if (param.enum) {
              return (
                <Form.Item
                  key={key}
                  name={key}
                  label={param.description}
                  rules={[
                    {
                      required: true,
                      message: `Please input ${param.description}`,
                    },
                  ]}
                >
                  <Select>
                    {param.enum.map((value) => (
                      <Select.Option key={value} value={value}>
                        {value === "y" ? "Yearly" : "Monthly"}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              );
            }

            return (
              <Form.Item
                key={key}
                name={key}
                label={param.description}
                rules={[
                  {
                    required: true,
                    message: `Please input ${param.description}`,
                  },
                  { type: "number", min: param.gt, max: param.le },
                ]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  min={param.gt}
                  max={param.le}
                  step={1000}
                />
              </Form.Item>
            );
          })}
      </Form>
    </Card>
  );
};

export default CapitalStrategyForm;
