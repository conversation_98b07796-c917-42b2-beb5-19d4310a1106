import React from 'react';

import { motion } from 'framer-motion';
import Link from 'next/link';

interface PortfolioTradeSignalsButtonProps {
  portfolioCode: string;
  isLoggedIn: boolean;
}

const PortfolioTradeSignalsButton: React.FC<
  PortfolioTradeSignalsButtonProps
> = ({ portfolioCode, isLoggedIn }) => {
  if (!isLoggedIn) {
    return (
      <motion.div
        className="sm:w-9/12 w-11/12 mb-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-2xl font-bold mb-4 text-main">策略交易信号深度分析</h2>
        <p className="text-lg font-medium text-gray-600">
          请点击【我的】菜单登录后查看该组合的策略交易信号深度分析
        </p>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="sm:w-9/12 w-11/12 mb-10"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <h2 className="text-2xl font-bold mb-4 text-main">策略交易信号深度分析</h2>
      <Link href={`/portfolios/${portfolioCode}/signals`} passHref>
        <motion.button
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          animate={{
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 0.5, // 动画持续0.5秒
            times: [0, 0.5, 1], // 控制动画的时间分布
            repeat: Infinity,
            repeatDelay: 4.5, // 4.5秒的延迟，加上0.5秒的动画时间，总共5秒一个循环
          }}
        >
          查看此组合的策略交易信号深度分析
        </motion.button>
      </Link>
    </motion.div>
  );
};

export default PortfolioTradeSignalsButton;
