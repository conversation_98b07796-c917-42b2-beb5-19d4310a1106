import Link from 'next/link';

interface RiskDisclaimerProps {
  title?: string;
  description?: string;
}

const RiskDisclaimer: React.FC<RiskDisclaimerProps> = ({ 
  title = "投资策略研究平台", 
  description = "模拟组合仅供学习研究，不构成投资建议" 
}) => {
  return (
    <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border-b border-amber-200 py-2">
      <div className="max-w-4xl mx-auto px-4">
        <div className="flex items-center justify-center text-sm text-amber-800">
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span className="font-medium">{title}</span>
          <span className="mx-2">•</span>
          <span>{description}</span>
          <span className="mx-2">•</span>
          <Link href="/eula" className="text-amber-700 hover:text-amber-900 underline font-medium">
            查看完整声明
          </Link>
        </div>
      </div>
    </div>
  );
};

export default RiskDisclaimer; 