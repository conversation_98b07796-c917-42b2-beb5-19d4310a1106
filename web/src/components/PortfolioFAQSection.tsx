import React from 'react';

import { motion } from 'framer-motion';

import { Section } from '../layout/Section';

const FAQSection: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
    },
  };

  return (
    <Section title="重要问题">
      <motion.div
        className="space-y-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.details
          className="group bg-white shadow-lg rounded-lg overflow-hidden"
          open
          variants={itemVariants}
        >
          <summary className="flex items-center justify-between p-4 cursor-pointer bg-gray-50">
            <h5 className="font-medium text-gray-900">
              投资组合驾驶舱中的各项指标是什么意思？
            </h5>
            <svg
              className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </summary>
          <div className="px-4 pt-4 pb-2 text-gray-700">
            <p className="pb-2">
              <strong>最新净值：</strong>
              投资组合当前的净值，反映组合的当前价值。
            </p>
            <p className="pb-2">
              <strong>年复合收益率：</strong>
              反映长期投资回报的年复合收益率，考虑了复利效应。
            </p>
            <p className="pb-2">
              <strong>胜率：</strong>
              盈利交易在总交易中的比例，反映策略的成功率。
            </p>
            <p className="pb-2">
              <strong>盈亏比：</strong>
              平均盈利与平均亏损的比率，反映风险管理能力。
            </p>
            <p className="pb-2">
              <strong>夏普比率：</strong>
              反映风险调整后的收益能力，夏普比率越高，表明单位风险下的收益越高。
            </p>
            <p className="pb-2">
              <strong>当前回撤：</strong>
              当前投资组合的亏损幅度，从最近峰值到当前值的下降比率。
            </p>
            <p className="pb-2">
              <strong>最大回撤：</strong>
              历史最大亏损幅度，衡量潜在亏损的重要指标。
            </p>
            <p className="pb-2">
              <strong>Calmar比率：</strong>
              年化收益率与最大回撤的比值，反映策略的风险调整后收益能力。
            </p>
            <p className="pb-2">
              <strong>SQN（系统质量指数）：</strong>
              衡量交易系统的整体质量，考虑了收益、风险和交易频率。
            </p>
            <p className="pb-2">
              <strong>VWR（波动率加权回报）：</strong>
              考虑波动率后的收益率指标，更好地反映风险调整后的收益。
            </p>
            <p className="pb-2">
              <strong>总交易次数：</strong>
              策略执行的总交易次数，反映交易频率。
            </p>
            <p className="pb-2">
              <strong>运行天数：</strong>策略运行的总天数。
            </p>
            <p className="pb-2">
              <strong>建仓日期：</strong>投资组合开始运作的日期。
            </p>
            <p className="pb-2">
              <strong>更新日期：</strong>数据最后更新的日期。
            </p>
          </div>
        </motion.details>

        <motion.details
          className="group bg-white shadow-lg rounded-lg overflow-hidden"
          variants={itemVariants}
        >
          <summary className="flex items-center justify-between p-4 cursor-pointer bg-gray-50">
            <h5 className="font-medium text-gray-900">时光机是什么？</h5>
            <svg
              className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </summary>
          <div className="px-4 pt-4 pb-2 text-gray-700">
            <p className="pb-2">
              时光机可以让我们很方便的观测在组合建仓后的不同年份里分别建仓的实际收益表现，同时也能了解该交易策略的稳定性：
              <strong className="text-red-600">
                一个优秀的组合它的核心风险指标（年复合收益率、夏普率与历史最大回撤）不应当受到组合建仓时间改变而产生大幅度的变化
              </strong>
              。
            </p>
          </div>
        </motion.details>

        <motion.details
          className="group bg-white shadow-lg rounded-lg overflow-hidden"
          variants={itemVariants}
        >
          <summary className="flex items-center justify-between p-4 cursor-pointer bg-gray-50">
            <h5 className="font-medium text-gray-900">
              走势图中的各指数分别是什么？
            </h5>
            <svg
              className="flex-shrink-0 ml-1.5 w-5 h-5 transition duration-300 group-open:-rotate-180"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </summary>
          <div className="px-4 pt-4 pb-2 text-gray-700">
            <p className="pb-2">
              组合净值按照基金计算净值的方式来计算，同时与全球主流指数进行对比，而不仅限于本市场指数。这样做的目的是使组合能与全球所有主流市场对比，展示交易策略的全球有效性。
            </p>
            <p className="pb-2">
              <strong>沪深300 (000300.SH)：</strong>
              代表沪深市场前300家规模最大且流动性好的股票。
            </p>
            <p className="pb-2">
              <strong>中证500 (000905.SH)：</strong>
              包含沪深市场规模较小的500家公司，反映中型企业的整体情况。
            </p>
            <p className="pb-2">
              <strong>创业板 (399006.SZ)：</strong>
              聚焦中国深圳交易所的高新技术和成长型企业。
            </p>
            <p className="pb-2">
              <strong>恒生指数 (HSI)：</strong>
              包含香港交易所最大的50家公司，是评估香港市场表现的主要指标。
            </p>
            <p className="pb-2">
              <strong>标普500指数 (SPX)：</strong>
              涵盖美国500家最大公司的股票表现，广泛用于评估美国股市的整体健康。
            </p>
            <p className="pb-2">
              <strong>纳斯达克指数 (IXIC)：</strong>
              主要由美国科技股组成，展示科技行业的表现。
            </p>
            <p className="pb-2">
              <strong>德国DAX指数 (GDAXI)：</strong>
              包括在法兰克福交易所上市的30家德国大型上市公司。
            </p>
            <p className="pb-2">
              <strong>日经225指数 (N225)：</strong>
              代表东京证券交易所上市的225家主要公司的表现。
            </p>
            <p className="pb-2">
              <strong>韩国综合指数 (KS11)：</strong>
              涵盖韩国首尔证券交易所主要上市公司的表现。
            </p>
            <p className="pb-2">
              <strong>澳大利亚标普200指数 (AS51)：</strong>
              反映澳大利亚证券交易所前200家主要上市公司的表现。
            </p>
            <p className="pb-2">
              <strong>印度孟买SENSEX指数 (SENSEX)：</strong>
              包括在孟买证券交易所上市的30家主要公司。
            </p>
            <p className="pb-4">
              <strong>15%年复合收益率基准：</strong>
              以15%的年复合收益率作为对比基准，超过此基准的组合表现被视为非常优秀，长期维持这一水平具有挑战性。
            </p>
          </div>
        </motion.details>
      </motion.div>
    </Section>
  );
};

export default FAQSection;
