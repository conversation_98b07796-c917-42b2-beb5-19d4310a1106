import React from 'react';

import { InfoIcon } from 'lucide-react';
import Link from 'next/link';

const EULANotice = () => {
  return (
    <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg mt-6">
      <div className="flex items-center space-x-3">
        <InfoIcon className="h-5 w-5 flex-shrink-0 text-blue-500" />
        <p className="text-gray-900">
          购买会员前请务必阅读
          <Link
            href="/eula"
            className="text-blue-600 hover:text-blue-800 mx-1 underline"
          >
            用户协议与免责声明
          </Link>
          。购买并使用即表示您已完全理解并同意该协议的全部内容。
        </p>
      </div>
    </div>
  );
};

export default EULANotice;
