import React, {
  useState,
  useEffect,
  useCallback,
  forwardRef,
  useMemo,
} from 'react';

import { useRouter } from 'next/router';
import DatePicker from 'react-datepicker';
import { InfinitySpin } from 'react-loader-spinner';
import 'react-datepicker/dist/react-datepicker.css';

import {
  CreatePortfolioRequest,
  Portfolio,
  PortfolioSymbol,
} from '../../types/types';
import {
  getPortfolioDetails,
  updatePortfolio,
  searchSymbols,
  createPortfolio,
  ApiError,
} from '../utils/api';
import { trackEvent } from '../utils/trackEvent';
import SymbolSearch from './SymbolSearch';

// 常量定义：应该保持字符串格式的参数名称
const STRING_PARAMS = [
  'investment_frequency', 
  'rebalance_frequency', 
  'frequency', 
  'signal_type', 
  'mode', 
  'method', 
  'type'
];

type ErrorMessage = string;

interface FormErrors {
  loading: ErrorMessage;
  submission: ErrorMessage;
  form: ErrorMessage; // 表单验证错误
  symbol: ErrorMessage; // 标的选择错误
  api: ErrorMessage; // API调用错误
}

interface PortfolioFormProps {
  mode: 'update' | 'copy';
  initialCode: string;
}

const initialErrors: FormErrors = {
  loading: '',
  submission: '',
  form: '',
  symbol: '',
  api: '',
};

const CustomInput = forwardRef<
  HTMLInputElement,
  { value?: string; onClick?: () => void }
>(({ value, onClick }, ref) => (
  <input
    ref={ref}
    className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors cursor-pointer"
    value={value}
    onClick={onClick}
    readOnly
  />
));

CustomInput.displayName = 'CustomDateInput';

export const PortfolioForm: React.FC<PortfolioFormProps> = ({
  mode,
  initialCode,
}) => {
  const router = useRouter();
  const [portfolio, setPortfolio] = useState<Partial<Portfolio>>({
    name: '',
    description: '',
    symbols: [],
    start_date: '',
    currency: '',
    market: '',
    commission: 0,
    update_time: '',
    strategy: { name: '', params: {} },
    capital_strategy: { name: '', params: {} },
  });
  
  // 记录参数的原始类型意图
  const [paramTypes, setParamTypes] = useState<{
    strategy: Record<string, 'number' | 'string'>;
    capital_strategy: Record<string, 'number' | 'string'>;
  }>({
    strategy: {},
    capital_strategy: {},
  });
  
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>(initialErrors);
  const [symbolQuery, setSymbolQuery] = useState('');
  const [symbolSuggestions, setSymbolSuggestions] = useState<PortfolioSymbol[]>(
    []
  );
  const [isComposing, setIsComposing] = useState(false);
  const [isSubmitSuccessful, setIsSubmitSuccessful] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const errorMsgWithLoginLink = useMemo(
    () => (
      <span>
        请
        <a
          href="https://login.myinvestpilot.com/?r=www.myinvestpilot.com/"
          className="text-blue-600 hover:text-blue-800 underline"
          target="_blank"
          rel="noopener noreferrer"
        >
          【登录】
        </a>
        后再操作
      </span>
    ),
    []
  );

  // 智能转换参数类型并记录类型意图
  const normalizeParams = (
    params: any, 
    strategyType: 'strategy' | 'capital_strategy'
  ): Record<string, string | number> => {
    if (!params || typeof params !== 'object') return {};
    
    const normalized: Record<string, string | number> = {};
    const typeMap: Record<string, 'number' | 'string'> = {};
    
    Object.entries(params).forEach(([key, value]) => {
      if (typeof value === 'string') {
        // 特殊参数始终保持字符串格式
        if (STRING_PARAMS.includes(key)) {
          normalized[key] = value;
          typeMap[key] = 'string';
        } else {
          // 如果字符串可以转换为有效数字，则转换
          const num = Number(value);
          if (!Number.isNaN(num) && Number.isFinite(num) && value.trim() !== '') {
            normalized[key] = num;
            typeMap[key] = 'number'; // 记录这个参数应该是数字类型
          } else {
            normalized[key] = value;
            typeMap[key] = 'string';
          }
        }
      } else {
        normalized[key] = value as string | number;
        typeMap[key] = typeof value === 'number' ? 'number' : 'string';
      }
    });
    
    // 更新参数类型记录
    setParamTypes(prev => ({
      ...prev,
      [strategyType]: { ...prev[strategyType], ...typeMap }
    }));
    
    return normalized;
  };

  useEffect(() => {
    if (initialCode && initialCode !== '') {
      const fetchInitialData = async () => {
        setLoading(true);
        setErrors(initialErrors);
        try {
          const portfolioData = await getPortfolioDetails(initialCode);
          
          // 转换参数类型
          const normalizedPortfolio = {
            ...portfolioData,
            strategy: portfolioData.strategy ? {
              ...portfolioData.strategy,
              params: normalizeParams(portfolioData.strategy.params, 'strategy')
            } : undefined,
            capital_strategy: portfolioData.capital_strategy ? {
              ...portfolioData.capital_strategy,
              params: normalizeParams(portfolioData.capital_strategy.params, 'capital_strategy')
            } : undefined,
          };
          
          setPortfolio(normalizedPortfolio);
        } catch (err) {
          if ((err as ApiError).status === 401) {
            setErrors((prev) => ({
              ...prev,
              loading: '请登录后再操作',
              api: errorMsgWithLoginLink as any,
            }));
          } else {
            setErrors((prev) => ({
              ...prev,
              loading: `❌ 加载组合数据失败：${(err as ApiError).message}`,
            }));
          }
        } finally {
          setLoading(false);
        }
      };
      fetchInitialData();
    }
  }, [initialCode, errorMsgWithLoginLink]);

  const fetchSymbols = useCallback(async () => {
    if (symbolQuery.length > 0 && !isComposing) {
      try {
        const results = await searchSymbols(
          symbolQuery,
          portfolio.currency || ''
        );
        setSymbolSuggestions(results);
        // 清除之前的符号搜索错误
        setErrors((prev) => ({ ...prev, symbol: '' }));
      } catch (err) {
        if ((err as ApiError).status === 401) {
          setErrors((prev) => ({
            ...prev,
            api: errorMsgWithLoginLink as any,
          }));
        } else {
          setErrors((prev) => ({
            ...prev,
            symbol: `❌ 搜索标的失败：${(err as ApiError).message}`,
          }));
        }
      }
    } else {
      setSymbolSuggestions([]);
    }
  }, [symbolQuery, isComposing, portfolio.currency, errorMsgWithLoginLink]);

  useEffect(() => {
    fetchSymbols();
  }, [fetchSymbols]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setPortfolio((prev) => ({ ...prev, [name]: value }));
    
    // 清除相关的表单错误
    if (name === 'name') {
      setErrors((prev) => ({ ...prev, form: '' }));
    }
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      setPortfolio((prev) => ({
        ...prev,
        start_date: date.toISOString().split('T')[0],
      }));
      // 清除日期相关错误
      setErrors((prev) => ({ ...prev, form: '' }));
    }
  };

  const getMaxDate = () => {
    const date = new Date();
    date.setDate(date.getDate() - 5);
    return date;
  };

  const getMinDate = () => {
    return new Date('2016-01-01');
  };

  const handleStrategyParamChange = (
    strategyType: 'strategy' | 'capital_strategy',
    paramName: string,
    value: string | number
  ) => {
    setPortfolio((prev) => {
      const currentParams =
        typeof prev[strategyType]?.params === 'string'
          ? JSON.parse(prev[strategyType]?.params as string)
          : prev[strategyType]?.params || {};

      return {
        ...prev,
        [strategyType]: {
          ...prev[strategyType],
          params: { ...currentParams, [paramName]: value },
        },
      } as Portfolio;
    });
  };

  // 判断参数值是否应该使用数字输入框
  const shouldUseNumberInput = (
    strategyType: 'strategy' | 'capital_strategy',
    paramName: string,
    value: any
  ): boolean => {
    // 首先检查参数类型记录，如果记录说这是数字类型，就使用数字输入框
    const recordedType = paramTypes[strategyType]?.[paramName];
    if (recordedType) {
      return recordedType === 'number';
    }
    
    // 如果没有记录，回退到基于当前值的判断
    return typeof value === 'number';
  };

  // 渲染参数输入框
  const renderParamInput = (
    strategyType: 'strategy' | 'capital_strategy',
    paramName: string,
    value: any
  ) => {
    // 特殊处理：FixedInvestmentStrategy 的 investment_frequency 参数
    if (strategyType === 'capital_strategy' && 
        paramName === 'investment_frequency' &&
        portfolio.capital_strategy?.name === 'FixedInvestmentStrategy') {
      return (
        <select
          value={value || 'm'}
          onChange={(e) =>
            handleStrategyParamChange(strategyType, paramName, e.target.value)
          }
          className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
        >
          <option value="m">月度投资 (m)</option>
          <option value="y">年度投资 (y)</option>
        </select>
      );
    }

    // 根据参数类型记录和值的类型决定输入框类型
    if (shouldUseNumberInput(strategyType, paramName, value)) {
      // 数字参数使用 number input
      return (
        <input
          type="number"
          value={value ?? ''}
          onChange={(e) =>
            handleStrategyParamChange(
              strategyType,
              paramName,
              e.target.value === '' ? '' : Number(e.target.value)
            )
          }
          onBlur={(e) => {
            // 失去焦点时，如果是空值且原来有值，恢复原值
            if (e.target.value === '' && value !== '' && value !== undefined) {
              handleStrategyParamChange(strategyType, paramName, value);
            }
          }}
          className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
        />
      );
    } 
      // 字符串参数使用 text input
      return (
        <input
          type="text"
          value={value || ''}
          onChange={(e) =>
            handleStrategyParamChange(strategyType, paramName, e.target.value)
          }
          className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
        />
      );
    
  };

  const handleSymbolSelect = (symbol: PortfolioSymbol) => {
    if ((portfolio.symbols?.length ?? 0) >= 10) {
      setErrors((prev) => ({
        ...prev,
        symbol: '❌ 不能选择超过10个标的',
      }));
      return;
    }
    
    // 检查是否已经存在
    const symbolExists = portfolio.symbols?.some(s => 
      (typeof s === 'string' ? s : s.symbol) === symbol.symbol
    );
    
    if (symbolExists) {
      setErrors((prev) => ({
        ...prev,
        symbol: '❌ 该标的已经添加过了',
      }));
      return;
    }

    setErrors((prev) => ({ ...prev, symbol: '' })); // 清除错误
    setPortfolio((prev) => ({
      ...prev,
      symbols: [...(prev.symbols || []), symbol],
    }));
    setSymbolQuery('');
    setSymbolSuggestions([]);
  };

  const handleRemoveSymbol = (symbolToRemove: string | PortfolioSymbol) => {
    setPortfolio((prev) => ({
      ...prev,
      symbols: prev.symbols?.filter(
        (s) =>
          (typeof s === 'string' ? s : s.symbol) !==
          (typeof symbolToRemove === 'string'
            ? symbolToRemove
            : symbolToRemove.symbol)
      ),
    }));
    // 清除符号相关错误
    setErrors((prev) => ({ ...prev, symbol: '' }));
  };

  const validateForm = (): boolean => {
    let formError = '';
    
    if (!portfolio.name?.trim()) {
      formError = '❌ 请输入组合名称';
    } else if (!portfolio.start_date) {
      formError = '❌ 请选择建仓时间';
    } else if (!portfolio.commission || portfolio.commission < 0) {
      formError = '❌ 请设置有效的交易佣金费率';
    } else if (!portfolio.update_time) {
      formError = '❌ 请设置组合数据更新时间';
    } else if (!portfolio.symbols || portfolio.symbols.length === 0) {
      formError = '❌ 请选择至少一个投资标的';
    }

    if (formError) {
      setErrors((prev) => ({ ...prev, form: formError }));
      return false;
    }

    // 清除表单错误
    setErrors((prev) => ({ ...prev, form: '' }));
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 表单验证
    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    setErrors((prev) => ({ ...prev, submission: '' }));

    try {
      const processParams = (
        rawParams: unknown
      ): Record<string, string | number> => {
        if (typeof rawParams === 'string') {
          try {
            return JSON.parse(rawParams);
          } catch {
            return {};
          }
        }
        if (typeof rawParams === 'object' && rawParams !== null) {
          return rawParams as Record<string, string | number>;
        }
        return {};
      };

      const portfolioData: CreatePortfolioRequest = {
        name: portfolio.name || '',
        description: portfolio.description || '',
        strategy: {
          name: portfolio.strategy?.name || '',
          params: processParams(portfolio.strategy?.params),
        },
        capital_strategy: {
          name: portfolio.capital_strategy?.name || '',
          params: processParams(portfolio.capital_strategy?.params),
        },
        symbols: (portfolio.symbols || []).map((s) =>
          typeof s === 'string' ? s : s.symbol
        ),
        start_date: portfolio.start_date || '',
        currency: portfolio.currency || '',
        market: portfolio.market || '',
        commission: portfolio.commission || 0,
        update_time: portfolio.update_time || '',
      };

      if (mode === 'update' && initialCode) {
        await updatePortfolio(initialCode, portfolioData);
        await trackEvent('UserUpdatePortfolio', { portfolio: initialCode });
      } else {
        await createPortfolio(portfolioData);
        await trackEvent('UserCreatePortfolio', {});
      }

      setIsSubmitSuccessful(true);
      setTimeout(() => {
        router.push('/me');
      }, 2000);
    } catch (err) {
      if ((err as ApiError).status === 401) {
        setErrors((prev) => ({
          ...prev,
          submission: '❌ 请登录后再操作',
          api: errorMsgWithLoginLink as any,
        }));
      } else {
        setErrors((prev) => ({
          ...prev,
          submission: `❌ ${mode === 'update' ? '更新' : '创建'}投资组合失败：${(err as ApiError).message}`,
        }));
      }
    } finally {
      setSubmitting(false);
    }
  };

  const getSymbolDisplayText = (symbol: string | PortfolioSymbol) => {
    if (typeof symbol === 'string') {
      return symbol;
    }
    return `${symbol.name} (${symbol.symbol})`;
  };

  const getSubmitButtonText = () => {
    if (submitting) {
      return '处理中...';
    }
    return mode === 'update' ? '更新投资组合' : '定制投资组合';
  };

  if (loading && !isSubmitSuccessful) {
    return (
      <div className="flex justify-center items-center h-screen">
        <InfinitySpin color="#4F46E5" />
      </div>
    );
  }

  if (isSubmitSuccessful) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md text-center">
        <h2 className="text-2xl font-bold text-green-600 mb-4">提交成功</h2>
        <p className="text-gray-600">
          您的投资组合已成功{mode === 'update' ? '更新' : '创建'}
          。正在跳转到我的页面...
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-50 rounded-xl shadow-lg border border-gray-200">
      {/* 显示加载错误 */}
      {errors.loading && (
        <div className="mb-4 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
          <div className="flex items-start space-x-2">
            <div className="text-red-500 text-lg">⚠️</div>
            <div>
              <div className="font-bold text-red-800 mb-1">加载错误</div>
              <div className="text-red-700">{errors.loading}</div>
            </div>
          </div>
        </div>
      )}

      {/* 显示API错误 */}
      {errors.api && (
        <div className="mb-4 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
          <div className="flex items-start space-x-2">
            <div className="text-red-500 text-lg">🔐</div>
            <div>
              <div className="font-bold text-red-800 mb-1">认证错误</div>
              <div className="text-red-700">{errors.api}</div>
            </div>
          </div>
        </div>
      )}

      {/* 显示提交错误 */}
      {errors.submission && (
        <div className="mb-4 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
          <div className="flex items-start space-x-2">
            <div className="text-red-500 text-lg">❌</div>
            <div>
              <div className="font-bold text-red-800 mb-1">提交失败</div>
              <div className="text-red-700">{errors.submission}</div>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 通用错误显示区域 */}
        {(errors.form || errors.symbol) && (
          <div className="mb-4 text-sm text-red-700 bg-red-100 border-2 border-red-300 rounded-lg p-4 shadow-md">
            <div className="flex items-start space-x-2">
              <div className="text-red-500 text-lg">⚠️</div>
              <div>
                <div className="font-bold text-red-800 mb-1">
                  表单验证错误
                </div>
                {errors.form && (
                  <div className="text-red-700 mb-1">{errors.form}</div>
                )}
                {errors.symbol && (
                  <div className="text-red-700 mb-1">{errors.symbol}</div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 基本信息分组 */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              📝 基本信息
            </h3>
            <p className="text-sm text-green-600">
              设置投资组合的基本信息和描述
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                组合名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="name"
                value={portfolio.name}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                placeholder="请输入投资组合名称"
                required
              />
              {!portfolio.name && (
                <div className="mt-1 text-sm text-gray-500">
                  <span className="text-red-500">⚠️</span> 请输入组合名称
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                组合描述
              </label>
              <textarea
                name="description"
                value={portfolio.description}
                onChange={handleInputChange}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                placeholder="描述您的投资组合策略和目标（可选）"
              />
            </div>
          </div>
        </div>

        {/* 市场配置分组 */}
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-purple-800 mb-2">
              🌍 市场配置
            </h3>
            <p className="text-sm text-purple-600">
              配置交易市场、建仓时间和交易费用设置
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                建仓时间 <span className="text-red-500">*</span>
              </label>
              <DatePicker
                selected={
                  portfolio.start_date ? new Date(portfolio.start_date) : null
                }
                onChange={handleDateChange}
                maxDate={getMaxDate()}
                minDate={getMinDate()}
                dateFormat="yyyy-MM-dd"
                customInput={<CustomInput />}
                placeholderText="选择建仓日期"
                required
                showYearDropdown
                scrollableYearDropdown
                yearDropdownItemNumber={15}
                showMonthDropdown
                dropdownMode="select"
              />
              {!portfolio.start_date && (
                <div className="mt-1 text-sm text-gray-500">
                  <span className="text-red-500">⚠️</span> 请选择建仓时间
                </div>
              )}
              <p className="mt-1 text-sm text-gray-500">
                请选择建仓时间，可选范围为2016年1月1日至5天前
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                基础货币
              </label>
              <input
                type="text"
                name="currency"
                value={portfolio.currency}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm bg-gray-100 text-gray-700"
                readOnly
              />
              <p className="mt-1 text-sm text-gray-500">
                基础货币由原组合设定，无法修改
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                交易市场
              </label>
              <input
                type="text"
                name="market"
                value={portfolio.market}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm bg-gray-100 text-gray-700"
                readOnly
              />
              <p className="mt-1 text-sm text-gray-500">
                交易市场由原组合设定，无法修改
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                交易佣金费率 <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="commission"
                value={portfolio.commission}
                onChange={handleInputChange}
                step="0.0001"
                min="0"
                max="1"
                className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                placeholder="0.001"
                required
              />
              {(!portfolio.commission || portfolio.commission < 0) && (
                <div className="mt-1 text-sm text-gray-500">
                  <span className="text-red-500">⚠️</span> 请设置有效的交易佣金费率
                </div>
              )}
              <p className="mt-1 text-sm text-gray-500">
                设置每笔交易的佣金费率，例如 0.001 表示 0.1%
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                组合数据更新时间 <span className="text-red-500">*</span>
              </label>
              <input
                type="time"
                name="update_time"
                value={portfolio.update_time}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 bg-white shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors"
                required
              />
              {!portfolio.update_time && (
                <div className="mt-1 text-sm text-gray-500">
                  <span className="text-red-500">⚠️</span> 请设置组合数据更新时间
                </div>
              )}
              <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="text-sm text-blue-800">
                  <div className="font-medium mb-1">⏰ 时区说明</div>
                  <p className="text-blue-700">
                    • 更新时间采用 <strong>UTC标准时间</strong>（格林威治时间）<br/>
                    • 中国时间 = UTC时间 + 8小时<br/>
                    • 例如：若希望北京时间早上8点更新并收到邮件，请设置为 <strong>00:00</strong>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 投资标的分组 */}
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-orange-800 mb-2">
              📊 投资标的池 <span className="text-red-500">*</span>
            </h3>
            <p className="text-sm text-orange-600">
              管理投资标的，支持搜索添加新标的（最多10个）
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索并添加标的
              </label>
              <SymbolSearch
                symbolQuery={symbolQuery}
                setSymbolQuery={setSymbolQuery}
                onCompositionStart={() => setIsComposing(true)}
                onCompositionEnd={() => setIsComposing(false)}
              />
              {symbolSuggestions.length > 0 && (
                <ul className="mt-2 border border-gray-300 bg-white rounded-md shadow-sm max-h-40 overflow-y-auto">
                  {symbolSuggestions.map((symbol) => (
                    <li
                      key={symbol.symbol}
                      onClick={() => handleSymbolSelect(symbol)}
                      className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    >
                      {symbol.name} ({symbol.symbol})
                      {symbol.market && (
                        <span className="text-gray-500 ml-2">
                          {symbol.market}
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                已选择的标的 ({portfolio.symbols?.length || 0}/10)
              </label>
              {portfolio.symbols && portfolio.symbols.length > 0 ? (
                <div className="space-y-2">
                  {portfolio.symbols.map((symbol, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md shadow-sm"
                    >
                      <span className="font-medium">
                        {getSymbolDisplayText(symbol)}
                      </span>
                      <button
                        type="button"
                        onClick={() => handleRemoveSymbol(symbol)}
                        className="text-red-500 hover:text-red-700 px-2 py-1 rounded-md hover:bg-red-50 transition-colors"
                      >
                        移除
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg">
                  <div className="text-gray-400 text-lg mb-2">📈</div>
                  <p className="text-gray-500 text-sm">
                    还未选择任何投资标的，请在上方搜索并添加
                  </p>
                </div>
              )}
              {(!portfolio.symbols || portfolio.symbols.length === 0) && (
                <div className="mt-1 text-sm text-gray-500">
                  <span className="text-red-500">⚠️</span>{' '}
                  请选择至少一个投资标的
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 交易策略参数分组 */}
        {portfolio.strategy?.name &&
          portfolio.strategy.params &&
          typeof portfolio.strategy.params === 'object' &&
          Object.keys(portfolio.strategy.params).length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">
                  ⚙️ 交易策略参数
                </h3>
                <p className="text-sm text-blue-600">
                  调整交易策略的具体参数配置
                </p>
              </div>

              <div className="bg-white rounded-md border border-gray-200 shadow-sm p-4">
                <h4 className="font-medium mb-3 text-blue-700">
                  策略：{portfolio.strategy.name}
                </h4>
                
                {/* 策略描述 */}
                {portfolio.strategy.description && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="text-sm text-blue-800 leading-relaxed">
                      {portfolio.strategy.description}
                    </div>
                  </div>
                )}
                
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {Object.entries(portfolio.strategy.params).map(
                    ([key, value]) => (
                      <div key={key}>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {key}
                        </label>
                        {renderParamInput('strategy', key, value)}
                      </div>
                    )
                  )}
                </div>
              </div>
            </div>
          )}

        {/* 资金策略参数分组 */}
        {portfolio.capital_strategy?.name &&
          portfolio.capital_strategy.params &&
          typeof portfolio.capital_strategy.params === 'object' &&
          Object.keys(portfolio.capital_strategy.params).length > 0 && (
            <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">
                  💰 资金管理策略参数
                </h3>
                <p className="text-sm text-indigo-600">
                  调整资金分配和管理策略的参数配置
                </p>
              </div>

              <div className="bg-white rounded-md border border-gray-200 shadow-sm p-4">
                <h4 className="font-medium mb-3 text-indigo-700">
                  策略：{portfolio.capital_strategy.name}
                </h4>
                
                {/* 资金策略描述 */}
                {portfolio.capital_strategy.description && (
                  <div className="mb-4 p-3 bg-indigo-50 border border-indigo-200 rounded-md">
                    <div className="text-sm text-indigo-800 leading-relaxed">
                      {portfolio.capital_strategy.description}
                    </div>
                  </div>
                )}
                
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {Object.entries(portfolio.capital_strategy.params).map(
                    ([key, value]) => (
                      <div key={key}>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {key}
                        </label>
                        {renderParamInput('capital_strategy', key, value)}
                      </div>
                    )
                  )}
                </div>
              </div>
            </div>
          )}

        {/* 提交按钮和说明 */}
        <div className="space-y-4">
          <button
            type="submit"
            disabled={submitting || loading}
            className="w-full bg-blue-500 text-white py-3 px-4 rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
          >
            {getSubmitButtonText()}
          </button>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <div className="text-yellow-500 text-lg">💡</div>
              <div>
                <div className="font-medium text-yellow-800 mb-1">重要提醒</div>
                <p className="text-sm text-yellow-700">
                  创建组合后请手动更新组合数据并订阅邮件提醒，否则组合将会在两天后被删除。组合自动更新数据的时间为设定的更新时间，如果有问题请联系管理员。
                </p>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PortfolioForm;
