import React from 'react';
import dynamic from 'next/dynamic';
import type { Node, Edge } from 'reactflow';

interface FlowWrapperProps {
  initialNodes: Node[];
  initialEdges: Edge[];
  onNodesChange?: (nodes: Node[]) => void;
  onEdgesChange?: (edges: Edge[]) => void;
  mode?: 'readonly' | 'edit';
}

// 动态导入 FlowComponent 以避免 SSR 问题
const DynamicFlowComponent = dynamic(
  () => import('./FlowComponent'),
  {
    ssr: false,
    loading: () => (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-gray-500">加载中...</div>
      </div>
    ),
  }
);

const FlowWrapper: React.FC<FlowWrapperProps> = (props) => {
  return <DynamicFlowComponent {...props} />;
};

export default FlowWrapper;
