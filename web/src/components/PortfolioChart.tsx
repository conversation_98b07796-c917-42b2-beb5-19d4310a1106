import React from 'react';

import ReactECharts, { EChartsOption } from 'echarts-for-react';

interface PortfolioChartProps {
  chartKey: number;
  options: EChartsOption;
  onChartReady: () => void;
}

const PortfolioChart: React.FC<PortfolioChartProps> = ({
  chartKey,
  options,
  onChartReady,
}) => {
  return (
    <ReactECharts
      key={chartKey}
      option={options}
      style={{ height: 800 }}
      onChartReady={onChartReady}
    />
  );
};

export default PortfolioChart;
