import React from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  isConfirmDisabled?: boolean;
  showSpinner?: boolean;
}

const Modal = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  isConfirmDisabled,
  showSpinner,
}: ModalProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <div className="mt-2 text-left">
          <p className="text-sm text-gray-500">{message}</p>
          {showSpinner && (
            <div className="mt-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            </div>
          )}
        </div>
        <div className="mt-4 flex justify-center">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 text-base font-medium rounded-md w-24 mr-2"
          >
            取消
          </button>
          <button
            onClick={onConfirm}
            disabled={isConfirmDisabled}
            className={`px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 ${
              isConfirmDisabled
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-red-600'
            }`}
          >
            确认
          </button>
        </div>
      </div>
    </div>
  );
};

export default Modal;
