// eslint-disable-next-line @next/next/no-server-import-in-page
import { NextResponse } from 'next/server';
// eslint-disable-next-line @next/next/no-server-import-in-page
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // 认证检查逻辑保持不变
  const jwt = request.cookies.get('jwt');

  if (
    request.nextUrl.pathname.startsWith('/post/') ||
    request.nextUrl.pathname.startsWith('/me') ||
    request.nextUrl.pathname.startsWith('/portfolio')
  ) {
    console.log(
      'middleware.ts: request.nextUrl.pathname:',
      request.nextUrl.pathname
    );
    if (!jwt || !jwt.value) {
      const loginUrl = new URL(
        `https://login.myinvestpilot.com/?r=${request.nextUrl.host}${request.nextUrl.pathname}`
      );
      return NextResponse.redirect(loginUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/post/:path*', '/me/:path*', '/api/myinvestpilot/:path*'],
};
