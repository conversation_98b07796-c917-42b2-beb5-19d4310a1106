import { useEffect, useRef } from 'react';

import mermaid from 'mermaid';
import Link from 'next/link';

import { AppConfig } from '../utils/AppConfig';

const HeroOneButton = () => {
  const mermaidRef = useRef(null);

  useEffect(() => {
    if (mermaidRef.current) {
      const mermaidChart = `
      mindmap
        root((策引理念))
            让投资更省心
                信号通知
                示例投资组合
                AI分析助手
                知识社区
            让投资更简单
                交易系统
                    交易策略
                    资金策略
                投资组合
                    回测表现
                    风险评估
                市场分析
                个股分析
      `;
      (mermaidRef.current as HTMLDivElement).textContent = mermaidChart;
      mermaid.init(undefined, mermaidRef.current);
    }
  }, []);

  return (
    <header className="text-center">
      <h1 className="text-5xl text-gray-900 font-bold whitespace-pre-line leading-hero">
        <span className="text-main">我的</span>
        <span className="shiny-ai font-semibold">AI</span>
        <span className="text-main">分析助手</span>
      </h1>
      <div className="text-2xl mt-4 mb-8">{AppConfig.description}</div>

      <Link
        href="https://jinshuju.net/f/kXG4yz"
        target="_blank"
        rel="noopener noreferrer"
        className="inline-block px-6 py-3 bg-main text-white font-semibold text-lg leading-snug uppercase rounded shadow-md hover:shadow-lg transition duration-150 ease-in-out"
      >
        申请测试资格
      </Link>

      <div className="flex justify-center mt-8">
        <div className="mermaid w-3/4 text-center" ref={mermaidRef}></div>
      </div>
    </header>
  );
};

export { HeroOneButton };
