type EventType =
  | 'SubscribePortfolioNotification'
  | 'UnsubscribePortfolioNotification'
  | 'SubscribePortfolioNotificationFailed'
  | 'UserPortfolioDataUpdate'
  | 'UserCreatePortfolio'
  | 'UserUpdatePortfolio'
  | 'UserDeletePortfolio'
  | 'UserTimeFlyPortfolio'
  | 'UserPortfolioStrategySignals'
  | 'IWantThisFeature'
  | 'MarketAIAnalysis';

interface EventProperties {
  [key: string]: any;
}

export const trackEvent = async (
  eventType: EventType,
  properties: EventProperties
) => {
  if (typeof (window as any).zaraz !== 'undefined') {
    await (window as any).zaraz.track(eventType, properties);
  }
};
