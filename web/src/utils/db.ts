export async function getDbUrl(portfolioCode: string): Promise<string> {
  const isProduction = process.env.NODE_ENV === 'production';
  const dbFileName = `${portfolioCode}_portfolio.db`;

  if (isProduction) {
    // 生产环境：直接返回 R2 URL
    return `https://media.i365.tech/myinvestpilot/portfolios/${portfolioCode}/${dbFileName}`;
  }
  // 开发环境：使用本地 API 路由
  return `/api/proxy-db/${portfolioCode}/${dbFileName}`;
}
