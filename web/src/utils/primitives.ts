/**
 * 根据环境获取原语清单的正确 URL
 * @param fileName 文件名 (默认: 'primitives-manifest.json')
 * @returns 完整的原语清单 URL
 */
export async function getPrimitivesManifestUrl(
  fileName: string = 'primitives-manifest.json'
): Promise<string> {
  const isProduction = process.env.NODE_ENV === 'production';

  if (isProduction) {
    // 生产环境：直接返回 R2 URL
    return `https://media.i365.tech/myinvestpilot/${fileName}`;
  }

  // 开发环境：使用本地 API 路由
  return `/api/proxy-primitives/${fileName}`;
}

/**
 * 获取原语架构的 URL
 * @returns 原语架构 URL
 */
export async function getPrimitivesSchemaUrl(): Promise<string> {
  return getPrimitivesManifestUrl('primitives_schema.json');
}

/**
 * 加载原语清单
 * @returns 原语清单对象
 */
export async function loadPrimitivesManifest(): Promise<any> {
  const url = await getPrimitivesManifestUrl();
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * 加载原语架构
 * @returns 原语架构对象
 */
export async function loadPrimitivesSchema(): Promise<any> {
  const url = await getPrimitivesSchemaUrl();
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
}
