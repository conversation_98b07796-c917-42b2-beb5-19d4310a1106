/**
 * 根据环境获取原语清单的正确 URL
 * @param fileName 文件名 (默认: 'primitives-manifest.json')
 * @returns 完整的原语清单 URL
 */
export async function getPrimitivesManifestUrl(
  fileName: string = 'primitives-manifest.json'
): Promise<string> {
  const isProduction = process.env.NODE_ENV === 'production';

  if (isProduction) {
    // 生产环境：直接返回 R2 URL
    return `https://media.i365.tech/myinvestpilot/${fileName}`;
  }

  // 开发环境：使用本地 API 路由
  return `/api/proxy-primitives/${fileName}`;
}

/**
 * 获取原语架构的 URL
 * @returns 原语架构 URL
 */
export async function getPrimitivesSchemaUrl(): Promise<string> {
  return getPrimitivesManifestUrl('primitives_schema.json');
}

/**
 * 加载原语清单
 * @returns 原语清单对象
 */
export async function loadPrimitivesManifest(): Promise<any> {
  const url = await getPrimitivesManifestUrl();
  const response = await fetch(url);

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 从完整的JSON Schema中提取组件定义
 * @returns 组件定义对象，格式与原语清单兼容
 */
export async function loadComponentsFromSchema(): Promise<any> {
  const schema = await loadPrimitivesSchema();

  if (!schema.definitions) {
    throw new Error('Schema does not contain definitions');
  }

  // 提取trade strategy组件
  const tradeStrategyComponents: any = {};

  // 提取indicators
  if (schema.definitions.IndicatorType?.enum) {
    tradeStrategyComponents.indicators = {};
    schema.definitions.IndicatorType.enum.forEach((type: string) => {
      const indicatorDef = schema.definitions[type];
      if (indicatorDef) {
        tradeStrategyComponents.indicators[type] = {
          type,
          description: indicatorDef.description || `${type} indicator`,
          parameters: extractParametersFromSchema(indicatorDef),
        };
      }
    });
  }

  // 提取signals
  if (schema.definitions.SignalType?.enum) {
    tradeStrategyComponents.signals = {};
    schema.definitions.SignalType.enum.forEach((type: string) => {
      const signalDef = schema.definitions[type];
      if (signalDef) {
        tradeStrategyComponents.signals[type] = {
          type,
          description: signalDef.description || `${type} signal`,
          parameters: extractParametersFromSchema(signalDef),
        };
      }
    });
  }

  // 提取market transformers
  const marketTransformers: any = {};
  if (schema.definitions.TransformerType?.enum) {
    schema.definitions.TransformerType.enum.forEach((type: string) => {
      const transformerDef = schema.definitions[type];
      if (transformerDef) {
        marketTransformers[type] = {
          type,
          description: transformerDef.description || `${type} transformer`,
          parameters: extractParametersFromSchema(transformerDef),
        };
      }
    });
  }

  // 提取outputs schema定义
  const outputsSchema = schema.definitions?.TradeStrategyBody?.properties?.outputs;

  return {
    trade_strategy: tradeStrategyComponents,
    market_transformers: marketTransformers,
    outputs_schema: outputsSchema,
    schema_version: schema.version || '1.0.0',
  };
}

/**
 * 从schema定义中提取参数信息
 */
function extractParametersFromSchema(definition: any): Record<string, any> {
  const parameters: Record<string, any> = {};

  if (definition.properties?.params?.properties) {
    const paramProps = definition.properties.params.properties;
    Object.entries(paramProps).forEach(([key, value]: [string, any]) => {
      parameters[key] = {
        type: value.type || 'any',
        description: value.description || '',
        default: value.default,
        enum: value.enum,
        minimum: value.minimum,
        maximum: value.maximum,
        required: definition.properties.params.required?.includes(key) || false,
      };
    });
  }

  return parameters;
}

/**
 * 加载原语架构
 * @returns 原语架构对象
 */
export async function loadPrimitivesSchema(): Promise<any> {
  const url = await getPrimitivesSchemaUrl();
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
}
