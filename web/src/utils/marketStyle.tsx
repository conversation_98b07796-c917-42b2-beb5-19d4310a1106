import React from 'react';

import {
  ChartBarSquareIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';

import { MarketRiskLevel } from '../../types/market';

export const getZoneColor = (zone: MarketRiskLevel): string => {
  const colors: Record<MarketRiskLevel, string> = {
    极低: 'bg-green-50 border-green-500',
    较低: 'bg-emerald-50 border-emerald-500',
    低: 'bg-green-50 border-green-500',
    中性: 'bg-blue-50 border-blue-500',
    高: 'bg-yellow-50 border-yellow-500',
    较高: 'bg-orange-50 border-orange-500',
    极高: 'bg-red-50 border-red-500',
  };
  return colors[zone];
};

export const getZoneIcon = (zone: MarketRiskLevel) => {
  if (zone === '极低')
    return <ArrowTrendingUpIcon className="h-8 w-8 text-green-500" />;
  if (zone === '较低')
    return <ArrowTrendingUpIcon className="h-8 w-8 text-emerald-500" />;
  if (zone === '低')
    return <ArrowTrendingUpIcon className="h-8 w-8 text-green-500" />;
  if (zone === '中性')
    return <ChartBarSquareIcon className="h-8 w-8 text-blue-500" />;
  if (zone === '高')
    return <ChartBarSquareIcon className="h-8 w-8 text-yellow-500" />;
  if (zone === '较高')
    return <ExclamationTriangleIcon className="h-8 w-8 text-orange-500" />;
  if (zone === '极高')
    return <ArrowTrendingDownIcon className="h-8 w-8 text-red-500" />;
  return <ChartBarSquareIcon className="h-8 w-8 text-gray-500" />;
};
