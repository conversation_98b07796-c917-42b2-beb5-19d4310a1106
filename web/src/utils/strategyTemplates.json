[{"name": "VIX市场过滤趋势跟踪策略", "description": "使用VIX波动率指标过滤市场环境，结合250日均线和吊灯止损的趋势跟踪策略。买入条件：价格高于均线和吊灯止损，且VIX百分位低于75或VIX下降；卖出条件：价格低于均线和吊灯止损，且市场环境恶化。(美股用VIX，其他市场需调整)", "dslJson": {"market_indicators": {"indicators": [{"code": "VIX"}], "transformers": [{"name": "vix_raw", "type": "IdentityTransformer", "params": {"indicator": "VIX", "field": "Close"}}, {"name": "vix_percentile", "type": "PercentileRankTransformer", "params": {"indicator": "VIX", "lookback": 252, "field": "Close"}}, {"name": "vix_ma", "type": "MovingAverageTransformer", "params": {"indicator": "VIX", "window": 20, "method": "simple", "field": "Close"}}]}, "trade_strategy": {"indicators": [{"id": "ma_indicator", "type": "SMA", "params": {"period": 250, "column": "Close"}}, {"id": "atr_indicator", "type": "ATR", "params": {"period": 60}}, {"id": "chandelier_exit_indicator", "type": "ChandelierExit", "params": {"period": 60, "multiplier": 4.0}}, {"id": "constant_75", "type": "Constant", "params": {"value": 75}}], "signals": [{"id": "price_gt_ma", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "ma_indicator"}]}, {"id": "price_gt_ce", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "chandelier_exit_indicator"}]}, {"id": "market_volatility_low", "type": "<PERSON><PERSON><PERSON>", "epsilon": 0.5, "inputs": [{"market": "VIX", "transformer": "vix_percentile"}, {"ref": "constant_75"}]}, {"id": "market_volatility_declining", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"market": "VIX", "transformer": "vix_raw"}, {"market": "VIX", "transformer": "vix_ma"}]}, {"id": "price_lt_ma", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"ref": "ma_indicator"}, {"column": "Close"}]}, {"id": "price_lt_ce", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"ref": "chandelier_exit_indicator"}, {"column": "Close"}]}, {"id": "market_condition_good", "type": "Or", "inputs": [{"ref": "market_volatility_low"}, {"ref": "market_volatility_declining"}]}, {"id": "price_conditions", "type": "And", "inputs": [{"ref": "price_gt_ma"}, {"ref": "price_gt_ce"}]}, {"id": "technical_buy_conditions", "type": "And", "inputs": [{"ref": "price_conditions"}, {"ref": "price_gt_ma"}]}, {"id": "buy_signal_condition", "type": "And", "inputs": [{"ref": "technical_buy_conditions"}, {"ref": "market_condition_good"}]}, {"id": "price_conditions_sell", "type": "And", "inputs": [{"ref": "price_lt_ma"}, {"ref": "price_lt_ce"}]}, {"id": "sell_signal_condition", "type": "And", "inputs": [{"ref": "price_conditions_sell"}, {"type": "Not", "inputs": [{"ref": "market_condition_good"}]}]}], "outputs": {"buy_signal": "buy_signal_condition", "sell_signal": "sell_signal_condition", "indicators": [{"id": "ma_indicator", "output_name": "ma"}, {"id": "atr_indicator", "output_name": "atr"}, {"id": "chandelier_exit_indicator", "output_name": "chandelier_stop"}, {"id": "market_volatility_low", "output_name": "vix_percentile_low"}, {"id": "market_volatility_declining", "output_name": "vix_declining"}, {"id": "market_condition_good", "output_name": "market_ok"}, {"id": "price_conditions", "output_name": "price_conditions"}, {"id": "technical_buy_conditions", "output_name": "tech_buy"}, {"id": "buy_signal_condition", "output_name": "buy_condition"}, {"id": "sell_signal_condition", "output_name": "sell_condition"}], "market_indicators": [{"market": "VIX", "transformer": "vix_raw", "output_name": "vix_raw"}, {"market": "VIX", "transformer": "vix_percentile", "output_name": "vix_percentile"}, {"market": "VIX", "transformer": "vix_ma", "output_name": "vix_ma"}]}}}}, {"name": "吊灯止损均线趋势策略", "description": "经典趋势跟踪策略，使用250日移动平均线判断长期趋势，结合60期ATR吊灯止损进行风险控制。买入条件：价格同时高于均线和吊灯止损；卖出条件：价格同时低于均线和吊灯止损", "dslJson": {"trade_strategy": {"indicators": [{"id": "ma_indicator", "type": "SMA", "params": {"period": 250, "column": "Close"}}, {"id": "atr_indicator", "type": "ATR", "params": {"period": 60}}, {"id": "chandelier_exit_indicator", "type": "ChandelierExit", "params": {"period": 60, "multiplier": 4.0}}], "signals": [{"id": "price_gt_ma", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "ma_indicator"}]}, {"id": "price_gt_ce", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "chandelier_exit_indicator"}]}, {"id": "price_lt_ma", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"column": "Close"}, {"ref": "ma_indicator"}]}, {"id": "price_lt_ce", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"column": "Close"}, {"ref": "chandelier_exit_indicator"}]}, {"id": "buy_signal_condition", "type": "And", "inputs": [{"ref": "price_gt_ma"}, {"ref": "price_gt_ce"}]}, {"id": "sell_signal_condition", "type": "And", "inputs": [{"ref": "price_lt_ma"}, {"ref": "price_lt_ce"}]}], "outputs": {"buy_signal": "buy_signal_condition", "sell_signal": "sell_signal_condition", "indicators": [{"id": "ma_indicator", "output_name": "ma"}, {"id": "atr_indicator", "output_name": "atr"}, {"id": "chandelier_exit_indicator", "output_name": "chandelier_stop"}, {"id": "price_gt_ma", "output_name": "price_gt_ma"}, {"id": "price_gt_ce", "output_name": "price_gt_ce"}, {"id": "price_lt_ma", "output_name": "price_lt_ma"}, {"id": "price_lt_ce", "output_name": "price_lt_ce"}, {"id": "buy_signal_condition", "output_name": "buy_condition"}, {"id": "sell_signal_condition", "output_name": "sell_condition"}]}}}}, {"name": "RSI超买超卖双均线确认策略", "description": "结合RSI超买超卖信号与双均线趋势确认的复合策略。买入条件：RSI超卖(≤30)或20日均线上穿50日均线；卖出条件：RSI超买(≥70)或价格跌破20日均线", "dslJson": {"trade_strategy": {"indicators": [{"id": "rsi", "type": "RSI", "params": {"period": 14, "column": "Close"}}, {"id": "sma20", "type": "SMA", "params": {"period": 20, "column": "Close"}}, {"id": "sma50", "type": "SMA", "params": {"period": 50, "column": "Close"}}, {"id": "threshold_30", "type": "Constant", "params": {"value": 30}}, {"id": "threshold_70", "type": "Constant", "params": {"value": 70}}], "signals": [{"id": "rsi_oversold", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"ref": "rsi"}, {"ref": "threshold_30"}]}, {"id": "rsi_overbought", "type": "GreaterThan", "inputs": [{"ref": "rsi"}, {"ref": "threshold_70"}]}, {"id": "price_above_sma20", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "sma20"}]}, {"id": "sma20_above_sma50", "type": "GreaterThan", "inputs": [{"ref": "sma20"}, {"ref": "sma50"}]}, {"id": "buy_condition", "type": "Or", "inputs": [{"ref": "rsi_oversold"}, {"ref": "sma20_above_sma50"}]}, {"id": "price_not_above_sma20", "type": "Not", "inputs": [{"ref": "price_above_sma20"}]}, {"id": "sell_condition", "type": "Or", "inputs": [{"ref": "rsi_overbought"}, {"ref": "price_not_above_sma20"}]}], "outputs": {"buy_signal": "buy_condition", "sell_signal": "sell_condition", "indicators": [{"id": "rsi", "output_name": "rsi"}, {"id": "sma20", "output_name": "sma20"}, {"id": "sma50", "output_name": "sma50"}, {"id": "threshold_30", "output_name": "rsi_threshold_30"}, {"id": "threshold_70", "output_name": "rsi_threshold_70"}, {"id": "rsi_oversold", "output_name": "rsi_oversold"}, {"id": "rsi_overbought", "output_name": "rsi_overbought"}, {"id": "price_above_sma20", "output_name": "price_above_sma20"}, {"id": "sma20_above_sma50", "output_name": "sma20_above_sma50"}, {"id": "buy_condition", "output_name": "buy_condition"}, {"id": "price_not_above_sma20", "output_name": "price_not_above_sma20"}, {"id": "sell_condition", "output_name": "sell_condition"}]}}}}, {"name": "MACD金叉成交量确认策略", "description": "多重确认的动量策略，要求MACD金叉、成交量放大、高波动率和MACD位于零线上方四个条件同时满足。买入条件：MACD金叉且成交量超过20日均线1.5倍且价格区间扩大且MACD>0；卖出条件：MACD死叉或MACD跌破零线", "dslJson": {"trade_strategy": {"indicators": [{"id": "macd_indicator", "type": "MACD", "params": {"fast_period": 12, "slow_period": 26, "signal_period": 9, "column": "Close"}}, {"id": "volume_ma", "type": "SMA", "params": {"period": 20, "column": "Volume"}}, {"id": "highest_high", "type": "HighestValue", "params": {"period": 20, "column": "High"}}, {"id": "lowest_low", "type": "LowestValue", "params": {"period": 20, "column": "Low"}}, {"id": "zero_line", "type": "Constant", "params": {"value": 0}}, {"id": "volatility_threshold", "type": "Constant", "params": {"value": 0.03}}, {"id": "price_range_threshold", "type": "Constant", "params": {"value": 1.5}}], "signals": [{"id": "macd_cross_signal", "type": "Crossover", "params": {"mode": "simple"}, "inputs": [{"ref": "macd_indicator.macd"}, {"ref": "macd_indicator.signal"}]}, {"id": "macd_below_zero", "type": "<PERSON><PERSON><PERSON>", "params": {"threshold": 0}, "inputs": [{"ref": "macd_indicator.macd"}, {"ref": "zero_line"}]}, {"id": "not_macd_below_zero", "type": "Not", "inputs": [{"ref": "macd_below_zero"}]}, {"id": "volume_surge", "type": "GreaterThan", "params": {"threshold": 1.5}, "inputs": [{"column": "Volume"}, {"ref": "volume_ma"}]}, {"id": "price_range", "type": "GreaterThan", "params": {"threshold": 10}, "inputs": [{"ref": "highest_high"}, {"ref": "lowest_low"}]}, {"id": "high_volatility", "type": "GreaterThan", "params": {"threshold": 0}, "inputs": [{"ref": "price_range"}, {"ref": "zero_line"}]}, {"id": "buy_condition_part1", "type": "And", "inputs": [{"ref": "macd_cross_signal"}, {"ref": "volume_surge"}]}, {"id": "buy_condition_part2", "type": "And", "inputs": [{"ref": "high_volatility"}, {"ref": "not_macd_below_zero"}]}, {"id": "buy_condition", "type": "And", "inputs": [{"ref": "buy_condition_part1"}, {"ref": "buy_condition_part2"}]}, {"id": "macd_cross_below_signal", "type": "<PERSON><PERSON><PERSON>", "params": {"mode": "simple"}, "inputs": [{"ref": "macd_indicator.macd"}, {"ref": "macd_indicator.signal"}]}, {"id": "sell_condition", "type": "Or", "inputs": [{"ref": "macd_cross_below_signal"}, {"ref": "macd_below_zero"}]}], "outputs": {"buy_signal": "buy_condition", "sell_signal": "sell_condition", "indicators": [{"id": "macd_indicator", "output_name": "macd"}, {"id": "macd_indicator.macd", "output_name": "macd_line"}, {"id": "macd_indicator.signal", "output_name": "macd_signal"}, {"id": "macd_indicator.histogram", "output_name": "macd_histogram"}, {"id": "volume_ma", "output_name": "volume_ma"}, {"id": "highest_high", "output_name": "highest_high"}, {"id": "lowest_low", "output_name": "lowest_low"}, {"id": "zero_line", "output_name": "zero_line"}, {"id": "macd_cross_signal", "output_name": "macd_golden_cross"}, {"id": "macd_below_zero", "output_name": "macd_below_zero"}, {"id": "not_macd_below_zero", "output_name": "macd_above_zero"}, {"id": "volume_surge", "output_name": "volume_surge"}, {"id": "price_range", "output_name": "price_range"}, {"id": "high_volatility", "output_name": "high_volatility"}, {"id": "buy_condition_part1", "output_name": "buy_part1_macd_volume"}, {"id": "buy_condition_part2", "output_name": "buy_part2_volatility"}, {"id": "buy_condition", "output_name": "buy_condition"}, {"id": "macd_cross_below_signal", "output_name": "macd_death_cross"}, {"id": "sell_condition", "output_name": "sell_condition"}]}}}}, {"name": "市场指数趋势轮动策略", "description": "基于大盘指数趋势的资产轮动策略，使用120日均线判断市场趋势，并要求连续3日确认减少假信号。指数价格高于均线3日则持有股票ETF，否则切换到债券ETF。(中国用沪深300，其他市场需替换指数)", "dslJson": {"market_indicators": {"indicators": [{"code": "000300.SH"}], "transformers": [{"name": "hs300_raw", "type": "IdentityTransformer", "params": {"indicator": "000300.SH", "field": "Close"}}, {"name": "hs300_ma120", "type": "MovingAverageTransformer", "params": {"indicator": "000300.SH", "window": 120, "method": "simple", "field": "Close"}}]}, "trade_strategy": {"indicators": [{"id": "constant_one", "type": "Constant", "params": {"value": 1}}], "signals": [{"id": "market_trend_up_raw", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "hs300_raw"}, {"market": "000300.SH", "transformer": "hs300_ma120"}]}, {"id": "market_trend_up_confirmed", "type": "Streak", "params": {"condition": "true", "min_length": 3}, "inputs": [{"ref": "market_trend_up_raw"}]}, {"id": "stock_bond_buy", "type": "StockBondSwitch", "params": {"default_to_stock": true}, "inputs": [{"ref": "market_trend_up_confirmed"}]}, {"id": "stock_bond_sell", "type": "Not", "inputs": [{"ref": "stock_bond_buy"}]}], "outputs": {"buy_signal": "stock_bond_buy", "sell_signal": "stock_bond_sell", "indicators": [{"id": "market_trend_up_raw", "output_name": "trend_signal_raw"}, {"id": "market_trend_up_confirmed", "output_name": "trend_signal"}], "market_indicators": [{"market": "000300.SH", "transformer": "hs300_raw", "output_name": "hs300_price"}, {"market": "000300.SH", "transformer": "hs300_ma120", "output_name": "hs300_ma120"}]}}}}, {"name": "布林带RSI均值回归策略", "description": "经典均值回归策略，在价格触及布林带极值时结合RSI确认进行反向交易。买入条件：价格跌破布林带下轨且RSI超卖(≤30)；卖出条件：价格突破布林带上轨或RSI超买(≥70)", "dslJson": {"trade_strategy": {"indicators": [{"id": "bb", "type": "BollingerBands", "params": {"period": 20, "std_dev": 2.0, "column": "Close", "method": "sma"}}, {"id": "rsi", "type": "RSI", "params": {"period": 14, "column": "Close"}}, {"id": "threshold_30", "type": "Constant", "params": {"value": 30}}, {"id": "threshold_70", "type": "Constant", "params": {"value": 70}}], "signals": [{"id": "price_below_lower", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"column": "Close"}, {"ref": "bb.lower"}]}, {"id": "price_above_upper", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "bb.upper"}]}, {"id": "rsi_oversold", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"ref": "rsi"}, {"ref": "threshold_30"}]}, {"id": "rsi_overbought", "type": "GreaterThan", "inputs": [{"ref": "rsi"}, {"ref": "threshold_70"}]}, {"id": "buy_signal", "type": "And", "inputs": [{"ref": "price_below_lower"}, {"ref": "rsi_oversold"}]}, {"id": "sell_signal", "type": "Or", "inputs": [{"ref": "price_above_upper"}, {"ref": "rsi_overbought"}]}], "outputs": {"buy_signal": "buy_signal", "sell_signal": "sell_signal", "indicators": [{"id": "bb", "output_name": "bb_middle"}, {"id": "bb.upper", "output_name": "bb_upper"}, {"id": "bb.lower", "output_name": "bb_lower"}, {"id": "rsi", "output_name": "rsi"}, {"id": "threshold_30", "output_name": "rsi_oversold_threshold"}, {"id": "threshold_70", "output_name": "rsi_overbought_threshold"}, {"id": "price_below_lower", "output_name": "price_below_bb_lower"}, {"id": "price_above_upper", "output_name": "price_above_bb_upper"}, {"id": "rsi_oversold", "output_name": "rsi_is_oversold"}, {"id": "rsi_overbought", "output_name": "rsi_is_overbought"}, {"id": "buy_signal", "output_name": "buy_condition"}, {"id": "sell_signal", "output_name": "sell_condition"}]}}}}, {"name": "短长期均线交叉策略", "description": "最经典的趋势跟踪策略，使用短期和长期移动平均线的交叉信号进行买卖决策。买入条件：短期均线上穿长期均线；卖出条件：短期均线下穿长期均线", "dslJson": {"trade_strategy": {"indicators": [{"id": "shortMA", "type": "SMA", "params": {"period": 11, "column": "Close"}}, {"id": "longMA", "type": "SMA", "params": {"period": 22, "column": "Close"}}], "signals": [{"id": "buy_signal", "type": "Crossover", "params": {"mode": "simple"}, "inputs": [{"ref": "shortMA"}, {"ref": "longMA"}]}, {"id": "sell_signal", "type": "<PERSON><PERSON><PERSON>", "params": {"mode": "simple"}, "inputs": [{"ref": "shortMA"}, {"ref": "longMA"}]}], "outputs": {"buy_signal": "buy_signal", "sell_signal": "sell_signal"}}}}]