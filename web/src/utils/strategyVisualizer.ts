/**
 * 策略可视化转换器
 * 负责在策略JSON和React Flow图形表示之间进行转换
 */

import { Node, Edge, Position } from 'reactflow';

// 策略JSON结构接口
export interface StrategyIndicator {
  id: string;
  type: string;
  params: Record<string, any>;
}

export interface StrategySignal {
  id: string;
  type: string;
  params?: Record<string, any>;
  inputs: Array<{
    ref?: string;
    column?: string;
    market?: string;
    transformer?: string;
  }>;
}

export interface TradeStrategy {
  indicators: StrategyIndicator[];
  signals: StrategySignal[];
  outputs?: {
    buy_signal?: string;
    sell_signal?: string;
    indicators?: Array<{ id: string; output_name: string }>;
  };
}

// React Flow节点类型
export interface StrategyNode extends Node {
  data: {
    label: string;
    type: 'indicator' | 'signal';
    originalType: string;
    params: Record<string, any>;
    description?: string;
  };
}

// 节点布局配置
const LAYOUT_CONFIG = {
  NODE_WIDTH: 200,
  NODE_HEIGHT: 80,
  HORIZONTAL_SPACING: 300,
  VERTICAL_SPACING: 120,
  INDICATOR_COLUMN: 0,
  SIGNAL_COLUMN: 1,
};

/**
 * 将策略JSON转换为React Flow的nodes和edges
 */
export function jsonToFlow(tradeStrategy: TradeStrategy): {
  nodes: StrategyNode[];
  edges: Edge[];
} {
  console.log('🔄 jsonToFlow 开始转换:', tradeStrategy);
  const nodes: StrategyNode[] = [];
  const edges: Edge[] = [];

  // 分析节点类型：输入节点、中间节点、输出节点
  const allNodeIds = new Set<string>();
  const referencedNodeIds = new Set<string>(); // 被其他节点引用的节点
  const referencingNodeIds = new Set<string>(); // 引用其他节点的节点

  // 收集所有节点ID
  if (tradeStrategy.indicators) {
    tradeStrategy.indicators.forEach(indicator => allNodeIds.add(indicator.id));
  }
  if (tradeStrategy.signals) {
    tradeStrategy.signals.forEach(signal => allNodeIds.add(signal.id));
  }

  // 分析引用关系
  if (tradeStrategy.signals) {
    tradeStrategy.signals.forEach(signal => {
      if (signal.inputs && Array.isArray(signal.inputs)) {
        signal.inputs.forEach(input => {
          if (input.ref && allNodeIds.has(input.ref)) {
            referencedNodeIds.add(input.ref);
            referencingNodeIds.add(signal.id);
          }
        });
      } else if (signal.inputs && typeof signal.inputs === 'object') {
        // 处理对象格式的inputs（向后兼容）
        Object.values(signal.inputs).forEach(inputValue => {
          if (typeof inputValue === 'string' && allNodeIds.has(inputValue)) {
            referencedNodeIds.add(inputValue);
            referencingNodeIds.add(signal.id);
          }
        });
      }
    });
  }

  // 确定节点类型
  const inputNodeIds = new Set<string>(); // 输入节点：基础指标（indicators）
  const outputNodeIds = new Set<string>(); // 输出节点：不引用任何节点的信号

  // 所有指标都是输入节点（基础数据源）
  if (tradeStrategy.indicators) {
    tradeStrategy.indicators.forEach(indicator => {
      inputNodeIds.add(indicator.id);
    });
  }

  // 输出节点：不被任何其他节点引用的信号（最终结果）
  if (tradeStrategy.signals) {
    tradeStrategy.signals.forEach(signal => {
      if (!referencedNodeIds.has(signal.id)) {
        outputNodeIds.add(signal.id);
      }
    });
  }

  console.log('📊 节点分析结果:', {
    allNodes: Array.from(allNodeIds),
    inputNodes: Array.from(inputNodeIds),
    outputNodes: Array.from(outputNodeIds),
    referencedNodes: Array.from(referencedNodeIds),
    referencingNodes: Array.from(referencingNodeIds)
  });

  // 详细分析每个节点的引用关系
  console.log('🔍 详细引用关系分析:');
  if (tradeStrategy.signals) {
    tradeStrategy.signals.forEach(signal => {
      if (signal.inputs && Array.isArray(signal.inputs)) {
        const refs = signal.inputs.filter(input => input.ref).map(input => input.ref);
        if (refs.length > 0) {
          console.log(`  ${signal.id} 引用了: [${refs.join(', ')}]`);
        }
      }
    });
  }

  // 处理指标节点
  if (tradeStrategy.indicators) {
    tradeStrategy.indicators.forEach((indicator, index) => {
      // 确定节点类型和样式
      const isInputNode = inputNodeIds.has(indicator.id);
      const isOutputNode = outputNodeIds.has(indicator.id);

      let nodeStyle = {};
      let nodeClass = '';

      if (isInputNode) {
        // 输入节点 - 绿色
        nodeStyle = {
          background: '#10B981', // emerald-500
          color: 'white',
          border: '2px solid #059669', // emerald-600
        };
        nodeClass = 'input-node';
      } else if (isOutputNode) {
        // 输出节点 - 蓝色
        nodeStyle = {
          background: '#3B82F6', // blue-500
          color: 'white',
          border: '2px solid #2563EB', // blue-600
        };
        nodeClass = 'output-node';
      } else {
        // 中间节点 - 默认灰色
        nodeStyle = {
          background: '#6B7280', // gray-500
          color: 'white',
          border: '2px solid #4B5563', // gray-600
        };
        nodeClass = 'intermediate-node';
      }

      const node: StrategyNode = {
        id: indicator.id,
        type: 'default',
        position: {
          x: LAYOUT_CONFIG.INDICATOR_COLUMN * LAYOUT_CONFIG.HORIZONTAL_SPACING,
          y: index * LAYOUT_CONFIG.VERTICAL_SPACING,
        },
        data: {
          label: `${indicator.type}\n(${indicator.id})`,
          type: 'indicator',
          originalType: indicator.type,
          params: indicator.params,
          description: `指标: ${indicator.type}`,
        },
        style: nodeStyle,
        className: nodeClass,
        sourcePosition: Position.Right,
        targetPosition: Position.Left,
      };
      nodes.push(node);
    });
  }

  // 处理信号节点
  if (tradeStrategy.signals) {
    tradeStrategy.signals.forEach((signal, index) => {
      // 确定节点类型和样式
      const isInputNode = inputNodeIds.has(signal.id);
      const isOutputNode = outputNodeIds.has(signal.id);

      let nodeStyle = {};
      let nodeClass = '';

      if (isInputNode) {
        // 输入节点 - 绿色
        nodeStyle = {
          background: '#10B981', // emerald-500
          color: 'white',
          border: '2px solid #059669', // emerald-600
        };
        nodeClass = 'input-node';
      } else if (isOutputNode) {
        // 输出节点 - 蓝色
        nodeStyle = {
          background: '#3B82F6', // blue-500
          color: 'white',
          border: '2px solid #2563EB', // blue-600
        };
        nodeClass = 'output-node';
      } else {
        // 中间节点 - 默认灰色
        nodeStyle = {
          background: '#6B7280', // gray-500
          color: 'white',
          border: '2px solid #4B5563', // gray-600
        };
        nodeClass = 'intermediate-node';
      }

      const node: StrategyNode = {
        id: signal.id,
        type: 'default',
        position: {
          x: LAYOUT_CONFIG.SIGNAL_COLUMN * LAYOUT_CONFIG.HORIZONTAL_SPACING,
          y: index * LAYOUT_CONFIG.VERTICAL_SPACING,
        },
        data: {
          label: `${signal.type}\n(${signal.id})`,
          type: 'signal',
          originalType: signal.type,
          params: signal.params || {},
          description: `信号: ${signal.type}`,
        },
        style: nodeStyle,
        className: nodeClass,
        sourcePosition: Position.Right,
        targetPosition: Position.Left,
      };
      nodes.push(node);

      // 创建连接边
      if (signal.inputs) {
        if (Array.isArray(signal.inputs)) {
          // 处理数组格式的inputs
          console.log(`📊 处理信号 ${signal.id} 的数组格式inputs:`, signal.inputs);
          signal.inputs.forEach((input, inputIndex) => {
            if (input.ref) {
              // 连接到其他节点（指标或信号ID）
              const edge: Edge = {
                id: `${input.ref}-${signal.id}-${inputIndex}`,
                source: input.ref,
                target: signal.id,
                type: 'default',
                animated: false,
                label: `input_${inputIndex}`,
              };
              console.log(`🔗 创建连接边:`, edge);
              edges.push(edge);
            }
            // 注意：column、market、transformer等类型的输入暂时不创建可视化连接
          });
        } else {
          // 处理对象格式的inputs（向后兼容）
          Object.entries(signal.inputs).forEach(([inputName, inputValue], inputIndex) => {
            if (typeof inputValue === 'string') {
              // 连接到其他节点（指标ID）
              const edge: Edge = {
                id: `${inputValue}-${signal.id}-${inputIndex}`,
                source: inputValue,
                target: signal.id,
                type: 'default',
                animated: false,
                label: inputName, // 显示输入参数名称
              };
              edges.push(edge);
            }
            // 注意：其他类型的输入（如数值）暂时不创建可视化连接
          });
        }
      }
    });
  }

  console.log('✅ jsonToFlow 转换完成:', {
    nodeCount: nodes.length,
    edgeCount: edges.length,
    inputNodeCount: inputNodeIds.size,
    outputNodeCount: outputNodeIds.size,
    nodes: nodes.map(n => ({
      id: n.id,
      type: n.data.type,
      className: n.className,
      isInput: inputNodeIds.has(n.id),
      isOutput: outputNodeIds.has(n.id)
    })),
    edges: edges.map(e => ({ id: e.id, source: e.source, target: e.target }))
  });
  return { nodes, edges };
}

/**
 * 将React Flow的nodes和edges转换回策略JSON
 */
export function flowToJson(nodes: StrategyNode[], edges: Edge[]): TradeStrategy {
  const indicators: StrategyIndicator[] = [];
  const signals: StrategySignal[] = [];

  // 按类型分离节点
  nodes.forEach((node) => {
    if (node.data.type === 'indicator') {
      indicators.push({
        id: node.id,
        type: node.data.originalType,
        params: node.data.params,
      });
    } else if (node.data.type === 'signal') {
      // 重建inputs数组
      const inputs: StrategySignal['inputs'] = [];
      
      // 查找指向此节点的边
      const incomingEdges = edges.filter((edge) => edge.target === node.id);
      incomingEdges.forEach((edge) => {
        inputs.push({ ref: edge.source });
      });

      signals.push({
        id: node.id,
        type: node.data.originalType,
        params: node.data.params,
        inputs,
      });
    }
  });

  return {
    indicators,
    signals,
  };
}

/**
 * 自动布局算法 - 改进版本，避免节点重叠
 */
export function autoLayout(nodes: StrategyNode[]): StrategyNode[] {
  const indicatorNodes = nodes.filter((n) => n.data.type === 'indicator');
  const signalNodes = nodes.filter((n) => n.data.type === 'signal');

  // 重新布局指标节点
  indicatorNodes.forEach((node, index) => {
    node.position = {
      x: LAYOUT_CONFIG.INDICATOR_COLUMN * LAYOUT_CONFIG.HORIZONTAL_SPACING,
      y: index * LAYOUT_CONFIG.VERTICAL_SPACING,
    };
  });

  // 重新布局信号节点
  signalNodes.forEach((node, index) => {
    node.position = {
      x: LAYOUT_CONFIG.SIGNAL_COLUMN * LAYOUT_CONFIG.HORIZONTAL_SPACING,
      y: index * LAYOUT_CONFIG.VERTICAL_SPACING,
    };
  });

  return [...indicatorNodes, ...signalNodes];
}

/**
 * 验证策略JSON的基本结构
 */
export function validateTradeStrategy(strategy: any): boolean {
  if (!strategy || typeof strategy !== 'object') {
    return false;
  }

  // 检查必要字段
  if (!Array.isArray(strategy.indicators) && !Array.isArray(strategy.signals)) {
    return false;
  }

  // 验证指标结构
  if (strategy.indicators) {
    for (const indicator of strategy.indicators) {
      if (!indicator.id || !indicator.type || !indicator.params) {
        return false;
      }
    }
  }

  // 验证信号结构
  if (strategy.signals) {
    for (const signal of strategy.signals) {
      if (!signal.id || !signal.type) {
        return false;
      }
      // inputs 可以是数组或对象，或者不存在
      if (signal.inputs && !Array.isArray(signal.inputs) && typeof signal.inputs !== 'object') {
        return false;
      }
    }
  }

  return true;
}
