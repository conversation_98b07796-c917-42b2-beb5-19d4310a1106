/**
 * 策略可视化转换器
 * 负责在策略JSON和React Flow图形表示之间进行转换
 * 支持原始策略格式和可视化格式的双向转换
 */

import { Node, Edge, Position, MarkerType } from 'reactflow';

// 策略JSON结构接口
export interface StrategyIndicator {
  id: string;
  type: string;
  params: Record<string, any>;
}

export interface StrategySignal {
  id: string;
  type: string;
  params?: Record<string, any>;
  inputs: Array<{
    ref?: string;
    column?: string;
    market?: string;
    transformer?: string;
  }>;
}

// Market Indicators结构接口
export interface MarketTransformer {
  name: string;
  type: string;
  params: Record<string, any>;
}

export interface MarketIndicators {
  indicators: Array<{ code: string }>;
  transformers: MarketTransformer[];
}

export interface TradeStrategy {
  indicators: StrategyIndicator[];
  signals: StrategySignal[];
  outputs?: {
    buy_signal?: string;
    sell_signal?: string;
    indicators?: Array<{ id: string; output_name: string }>;
    market_indicators?: Array<{ market: string; transformer: string; output_name: string }>;
  };
}

// 完整策略定义接口
export interface StrategyDefinitionForAI {
  market_indicators?: MarketIndicators;
  trade_strategy: TradeStrategy;
}

// React Flow节点类型
export interface StrategyNode extends Node {
  data: {
    label: string;
    type: 'indicator' | 'signal' | 'market_transformer';
    originalType: string;
    params: Record<string, any>;
    description?: string;
    marketCode?: string; // 用于market transformer节点
  };
}

// 节点布局配置
const LAYOUT_CONFIG = {
  NODE_WIDTH: 200,
  NODE_HEIGHT: 80,
  HORIZONTAL_SPACING: 300,
  VERTICAL_SPACING: 120,
  INDICATOR_COLUMN: 0,
  SIGNAL_COLUMN: 1,
};

// 格式化参数显示
function formatParams(params: any): string {
  if (!params || Object.keys(params).length === 0) {
    return '';
  }

  const paramStrings = Object.entries(params).map(([key, value]) => {
    if (typeof value === 'string') {
      return `${key}: "${value}"`;
    }
    return `${key}: ${value}`;
  });

  return `\n${paramStrings.join('\n')}`;
}

/**
 * 确定节点类型
 */
function getNodeType(
  nodeId: string,
  inputNodeIds: Set<string>,
  outputNodeIds: Set<string>,
  unusedNodeIds: Set<string>
): 'input' | 'output' | 'unused' | 'intermediate' {
  if (unusedNodeIds.has(nodeId)) return 'unused';
  if (inputNodeIds.has(nodeId)) return 'input';
  if (outputNodeIds.has(nodeId)) return 'output';
  return 'intermediate';
}

/**
 * 根据节点类型获取颜色
 */
function getNodeColor(nodeType: 'input' | 'output' | 'unused' | 'intermediate'): string {
  switch (nodeType) {
    case 'unused':
      return '#F59E0B'; // amber-500 - 未使用节点
    case 'input':
      return '#3B82F6'; // blue-500 - 输入节点
    case 'output':
      return '#10B981'; // emerald-500 - 输出节点
    case 'intermediate':
    default:
      return '#6B7280'; // gray-500 - 中间节点
  }
}

/**
 * 将完整策略定义转换为React Flow的nodes和edges
 */
export function strategyToFlow(strategy: StrategyDefinitionForAI): {
  nodes: StrategyNode[];
  edges: Edge[];
} {
  console.log('🔄 strategyToFlow 开始转换完整策略:', strategy);
  const nodes: StrategyNode[] = [];
  const edges: Edge[] = [];

  // 分析节点类型：输入节点、中间节点、输出节点
  const allNodeIds = new Set<string>();
  const referencedNodeIds = new Set<string>(); // 被其他节点引用的节点
  const referencingNodeIds = new Set<string>(); // 引用其他节点的节点

  // 收集所有节点ID（包括market transformers）
  if (strategy.market_indicators?.transformers) {
    strategy.market_indicators.transformers.forEach(transformer =>
      allNodeIds.add(`market_${transformer.name}`)
    );
  }

  if (strategy.trade_strategy.indicators) {
    strategy.trade_strategy.indicators.forEach(indicator => allNodeIds.add(indicator.id));
  }

  if (strategy.trade_strategy.signals) {
    strategy.trade_strategy.signals.forEach(signal => allNodeIds.add(signal.id));
  }

  // 分析引用关系（包括market transformer引用）
  if (strategy.trade_strategy.signals) {
    strategy.trade_strategy.signals.forEach(signal => {
      if (signal.inputs && Array.isArray(signal.inputs)) {
        signal.inputs.forEach(input => {
          // 处理对trade strategy节点的引用
          if (input.ref && allNodeIds.has(input.ref)) {
            referencedNodeIds.add(input.ref);
            referencingNodeIds.add(signal.id);
          }
          // 处理对market transformer的引用
          if (input.market && input.transformer) {
            const marketNodeId = `market_${input.transformer}`;
            if (allNodeIds.has(marketNodeId)) {
              referencedNodeIds.add(marketNodeId);
              referencingNodeIds.add(signal.id);
            }
          }
        });
      }
    });
  }

  // 确定节点类型
  const inputNodeIds = new Set<string>(); // 输入节点：不被任何节点引用的节点（数据源）
  const outputNodeIds = new Set<string>(); // 输出节点：不引用任何节点的节点（最终结果）
  const unusedNodeIds = new Set<string>(); // 未使用节点：既不被引用也不引用其他节点

  allNodeIds.forEach(nodeId => {
    const isReferenced = referencedNodeIds.has(nodeId);
    const isReferencing = referencingNodeIds.has(nodeId);

    if (!isReferenced && !isReferencing) {
      unusedNodeIds.add(nodeId);
    } else if (!isReferenced) {
      inputNodeIds.add(nodeId);
    } else if (!isReferencing) {
      outputNodeIds.add(nodeId);
    }
  });

  console.log('📊 完整策略节点分析结果:', {
    allNodes: Array.from(allNodeIds),
    inputNodes: Array.from(inputNodeIds),
    outputNodes: Array.from(outputNodeIds),
    unusedNodes: Array.from(unusedNodeIds)
  });

  // 创建market transformer节点
  if (strategy.market_indicators?.transformers) {
    strategy.market_indicators.transformers.forEach((transformer, index) => {
      const nodeId = `market_${transformer.name}`;
      const nodeType = getNodeType(nodeId, inputNodeIds, outputNodeIds, unusedNodeIds);

      nodes.push({
        id: nodeId,
        type: 'default',
        position: { x: 100, y: 100 + index * 120 },
        data: {
          label: createMarketTransformerLabel(transformer),
          type: 'market_transformer',
          originalType: transformer.type,
          params: transformer.params,
          marketCode: transformer.params.indicator,
          description: `Market ${transformer.type}`,
        },
        style: {
          background: getNodeColor(nodeType),
          border: '2px solid #1a192b',
          borderRadius: '8px',
          padding: '10px',
          minWidth: '200px',
        },
      });
    });
  }

  // 创建trade strategy节点（indicators和signals）
  const tradeStrategyResult = jsonToFlow(strategy.trade_strategy);

  // 调整trade strategy节点位置，避免与market transformer重叠
  const offsetX = 400; // market transformers在左侧，trade strategy在右侧

  tradeStrategyResult.nodes.forEach(node => {
    node.position.x += offsetX;
    nodes.push(node);
  });

  // 添加trade strategy内部的edges
  edges.push(...tradeStrategyResult.edges);

  // 创建market transformer到trade strategy的连接
  if (strategy.trade_strategy.signals) {
    strategy.trade_strategy.signals.forEach(signal => {
      if (signal.inputs && Array.isArray(signal.inputs)) {
        signal.inputs.forEach(input => {
          if (input.market && input.transformer) {
            const sourceId = `market_${input.transformer}`;
            const targetId = signal.id;

            if (allNodeIds.has(sourceId) && allNodeIds.has(targetId)) {
              edges.push({
                id: `${sourceId}-${targetId}`,
                source: sourceId,
                target: targetId,
                type: 'smoothstep',
                animated: true,
                style: { stroke: '#8b5cf6', strokeWidth: 2 },
                markerEnd: { type: MarkerType.ArrowClosed, color: '#8b5cf6' },
                label: `${input.market}`,
                labelStyle: { fontSize: '12px', fontWeight: 'bold' },
              });
            }
          }
        });
      }
    });
  }

  console.log('✅ strategyToFlow 转换完成:', { nodes: nodes.length, edges: edges.length });
  return { nodes, edges };
}

/**
 * 创建market transformer节点标签
 */
function createMarketTransformerLabel(transformer: MarketTransformer): string {
  const paramsList = Object.entries(transformer.params)
    .filter(([key]) => key !== 'indicator') // indicator已经在标题中显示
    .map(([key, value]) => `${key}: ${value}`)
    .join(', ');

  return `${transformer.type}\n${transformer.params.indicator}\n${transformer.name}${paramsList ? `\n(${paramsList})` : ''}`;
}

/**
 * 将策略JSON转换为React Flow的nodes和edges（仅trade strategy部分）
 */
export function jsonToFlow(tradeStrategy: TradeStrategy): {
  nodes: StrategyNode[];
  edges: Edge[];
} {
  console.log('🔄 jsonToFlow 开始转换:', tradeStrategy);
  const nodes: StrategyNode[] = [];
  const edges: Edge[] = [];

  // 分析节点类型：输入节点、中间节点、输出节点
  const allNodeIds = new Set<string>();
  const referencedNodeIds = new Set<string>(); // 被其他节点引用的节点
  const referencingNodeIds = new Set<string>(); // 引用其他节点的节点

  // 收集所有节点ID
  if (tradeStrategy.indicators) {
    tradeStrategy.indicators.forEach(indicator => allNodeIds.add(indicator.id));
  }
  if (tradeStrategy.signals) {
    tradeStrategy.signals.forEach(signal => allNodeIds.add(signal.id));
  }

  // 分析引用关系
  if (tradeStrategy.signals) {
    tradeStrategy.signals.forEach(signal => {
      if (signal.inputs && Array.isArray(signal.inputs)) {
        signal.inputs.forEach(input => {
          if (input.ref && allNodeIds.has(input.ref)) {
            referencedNodeIds.add(input.ref);
            referencingNodeIds.add(signal.id);
          }
        });
      } else if (signal.inputs && typeof signal.inputs === 'object') {
        // 处理对象格式的inputs（向后兼容）
        Object.values(signal.inputs).forEach(inputValue => {
          if (typeof inputValue === 'string' && allNodeIds.has(inputValue)) {
            referencedNodeIds.add(inputValue);
            referencingNodeIds.add(signal.id);
          }
        });
      }
    });
  }

  // 确定节点类型
  const inputNodeIds = new Set<string>(); // 输入节点：不被任何节点引用的节点（数据源）
  const outputNodeIds = new Set<string>(); // 输出节点：不引用任何节点的节点（最终结果）
  const unusedNodeIds = new Set<string>(); // 未使用节点：既不被引用也不引用其他节点

  allNodeIds.forEach(nodeId => {
    const isReferenced = referencedNodeIds.has(nodeId);
    const isReferencing = referencingNodeIds.has(nodeId);

    if (!isReferenced && !isReferencing) {
      // 既不被引用也不引用其他节点 = 未使用的组件
      unusedNodeIds.add(nodeId);
    } else if (!isReferenced) {
      // 不被任何其他节点引用（数据流的起点）
      inputNodeIds.add(nodeId);
    } else if (!isReferencing) {
      // 不引用任何其他节点（数据流的终点）
      outputNodeIds.add(nodeId);
    }
  });

  console.log('📊 节点分析结果:', {
    allNodes: Array.from(allNodeIds),
    inputNodes: Array.from(inputNodeIds),
    outputNodes: Array.from(outputNodeIds),
    referencedNodes: Array.from(referencedNodeIds),
    referencingNodes: Array.from(referencingNodeIds)
  });

  // 详细分析每个节点的引用关系
  console.log('🔍 详细引用关系分析:');
  if (tradeStrategy.signals) {
    tradeStrategy.signals.forEach(signal => {
      if (signal.inputs && Array.isArray(signal.inputs)) {
        const refs = signal.inputs.filter(input => input.ref).map(input => input.ref);
        if (refs.length > 0) {
          console.log(`  ${signal.id} 引用了: [${refs.join(', ')}]`);
        }
      }
    });
  }

  // 处理指标节点
  if (tradeStrategy.indicators) {
    tradeStrategy.indicators.forEach((indicator, index) => {
      // 确定节点类型和样式
      const isInputNode = inputNodeIds.has(indicator.id);
      const isOutputNode = outputNodeIds.has(indicator.id);
      const isUnusedNode = unusedNodeIds.has(indicator.id);

      let nodeStyle = {};
      let nodeClass = '';

      if (isUnusedNode) {
        // 未使用节点 - 橙色警告
        nodeStyle = {
          background: '#F59E0B', // amber-500
          color: 'white',
          border: '2px solid #D97706', // amber-600
        };
        nodeClass = 'unused-node';
      } else if (isInputNode) {
        // 输入节点 - 蓝色
        nodeStyle = {
          background: '#3B82F6', // blue-500
          color: 'white',
          border: '2px solid #2563EB', // blue-600
        };
        nodeClass = 'input-node';
      } else if (isOutputNode) {
        // 输出节点 - 绿色
        nodeStyle = {
          background: '#10B981', // emerald-500
          color: 'white',
          border: '2px solid #059669', // emerald-600
        };
        nodeClass = 'output-node';
      } else {
        // 中间节点 - 默认灰色
        nodeStyle = {
          background: '#6B7280', // gray-500
          color: 'white',
          border: '2px solid #4B5563', // gray-600
        };
        nodeClass = 'intermediate-node';
      }

      const node: StrategyNode = {
        id: indicator.id,
        type: 'default',
        position: {
          x: LAYOUT_CONFIG.INDICATOR_COLUMN * LAYOUT_CONFIG.HORIZONTAL_SPACING,
          y: index * LAYOUT_CONFIG.VERTICAL_SPACING,
        },
        data: {
          label: `${indicator.type}\n(${indicator.id})${formatParams(indicator.params)}`,
          type: 'indicator',
          originalType: indicator.type,
          params: indicator.params,
          description: `指标: ${indicator.type}`,
        },
        style: nodeStyle,
        className: nodeClass,
        sourcePosition: Position.Right,
        targetPosition: Position.Left,
      };
      nodes.push(node);
    });
  }

  // 处理信号节点
  if (tradeStrategy.signals) {
    tradeStrategy.signals.forEach((signal, index) => {
      // 确定节点类型和样式
      const isInputNode = inputNodeIds.has(signal.id);
      const isOutputNode = outputNodeIds.has(signal.id);
      const isUnusedNode = unusedNodeIds.has(signal.id);

      let nodeStyle = {};
      let nodeClass = '';

      if (isUnusedNode) {
        // 未使用节点 - 橙色警告
        nodeStyle = {
          background: '#F59E0B', // amber-500
          color: 'white',
          border: '2px solid #D97706', // amber-600
        };
        nodeClass = 'unused-node';
      } else if (isInputNode) {
        // 输入节点 - 蓝色
        nodeStyle = {
          background: '#3B82F6', // blue-500
          color: 'white',
          border: '2px solid #2563EB', // blue-600
        };
        nodeClass = 'input-node';
      } else if (isOutputNode) {
        // 输出节点 - 绿色
        nodeStyle = {
          background: '#10B981', // emerald-500
          color: 'white',
          border: '2px solid #059669', // emerald-600
        };
        nodeClass = 'output-node';
      } else {
        // 中间节点 - 默认灰色
        nodeStyle = {
          background: '#6B7280', // gray-500
          color: 'white',
          border: '2px solid #4B5563', // gray-600
        };
        nodeClass = 'intermediate-node';
      }

      const node: StrategyNode = {
        id: signal.id,
        type: 'default',
        position: {
          x: LAYOUT_CONFIG.SIGNAL_COLUMN * LAYOUT_CONFIG.HORIZONTAL_SPACING,
          y: index * LAYOUT_CONFIG.VERTICAL_SPACING,
        },
        data: {
          label: `${signal.type}\n(${signal.id})${formatParams(signal.params)}`,
          type: 'signal',
          originalType: signal.type,
          params: signal.params || {},
          description: `信号: ${signal.type}`,
        },
        style: nodeStyle,
        className: nodeClass,
        sourcePosition: Position.Right,
        targetPosition: Position.Left,
      };
      nodes.push(node);

      // 创建连接边
      if (signal.inputs) {
        if (Array.isArray(signal.inputs)) {
          // 处理数组格式的inputs
          console.log(`📊 处理信号 ${signal.id} 的数组格式inputs:`, signal.inputs);
          signal.inputs.forEach((input, inputIndex) => {
            if (input.ref) {
              // 连接到其他节点（指标或信号ID）
              const edge: Edge = {
                id: `${input.ref}-${signal.id}-${inputIndex}`,
                source: input.ref,
                target: signal.id,
                type: 'smoothstep',
                animated: true,
                label: `input_${inputIndex}`,
                style: { stroke: '#999', strokeWidth: 2 },
                markerEnd: {
                  type: MarkerType.ArrowClosed,
                  color: '#999',
                },
              };
              console.log(`🔗 创建连接边:`, edge);
              edges.push(edge);
            }
            // 注意：column、market、transformer等类型的输入暂时不创建可视化连接
          });
        } else {
          // 处理对象格式的inputs（向后兼容）
          Object.entries(signal.inputs).forEach(([inputName, inputValue], inputIndex) => {
            if (typeof inputValue === 'string') {
              // 连接到其他节点（指标ID）
              const edge: Edge = {
                id: `${inputValue}-${signal.id}-${inputIndex}`,
                source: inputValue,
                target: signal.id,
                type: 'smoothstep',
                animated: true,
                label: inputName, // 显示输入参数名称
                style: { stroke: '#999', strokeWidth: 2 },
                markerEnd: {
                  type: MarkerType.ArrowClosed,
                  color: '#999',
                },
              };
              edges.push(edge);
            }
            // 注意：其他类型的输入（如数值）暂时不创建可视化连接
          });
        }
      }
    });
  }

  console.log('✅ jsonToFlow 转换完成:', {
    nodeCount: nodes.length,
    edgeCount: edges.length,
    inputNodeCount: inputNodeIds.size,
    outputNodeCount: outputNodeIds.size,
    unusedNodeCount: unusedNodeIds.size,
    nodes: nodes.map(n => ({
      id: n.id,
      type: n.data.type,
      className: n.className,
      isInput: inputNodeIds.has(n.id),
      isOutput: outputNodeIds.has(n.id),
      isUnused: unusedNodeIds.has(n.id)
    })),
    edges: edges.map(e => ({ id: e.id, source: e.source, target: e.target }))
  });

  // 输出节点分类信息
  if (inputNodeIds.size > 0) {
    console.log('🟢 输入节点:', Array.from(inputNodeIds));
  }
  if (outputNodeIds.size > 0) {
    console.log('🔵 输出节点:', Array.from(outputNodeIds));
  }
  if (unusedNodeIds.size > 0) {
    console.warn('🟠 未使用节点:', Array.from(unusedNodeIds));
  }

  // 如果没有边，输出警告
  if (edges.length === 0) {
    console.warn('⚠️ 没有创建任何连接边！检查信号的inputs配置。');
  }

  return { nodes, edges };
}

/**
 * 将React Flow的nodes和edges转换回策略JSON
 */
export function flowToJson(nodes: StrategyNode[], edges: Edge[]): TradeStrategy {
  const indicators: StrategyIndicator[] = [];
  const signals: StrategySignal[] = [];

  // 按类型分离节点
  nodes.forEach((node) => {
    if (node.data.type === 'indicator') {
      indicators.push({
        id: node.id,
        type: node.data.originalType,
        params: node.data.params,
      });
    } else if (node.data.type === 'signal') {
      // 重建inputs数组
      const inputs: StrategySignal['inputs'] = [];
      
      // 查找指向此节点的边
      const incomingEdges = edges.filter((edge) => edge.target === node.id);
      incomingEdges.forEach((edge) => {
        inputs.push({ ref: edge.source });
      });

      signals.push({
        id: node.id,
        type: node.data.originalType,
        params: node.data.params,
        inputs,
      });
    }
  });

  return {
    indicators,
    signals,
  };
}

/**
 * 自动布局算法 - 改进版本，避免节点重叠
 */
export function autoLayout(nodes: StrategyNode[]): StrategyNode[] {
  const indicatorNodes = nodes.filter((n) => n.data.type === 'indicator');
  const signalNodes = nodes.filter((n) => n.data.type === 'signal');

  // 重新布局指标节点
  indicatorNodes.forEach((node, index) => {
    node.position = {
      x: LAYOUT_CONFIG.INDICATOR_COLUMN * LAYOUT_CONFIG.HORIZONTAL_SPACING,
      y: index * LAYOUT_CONFIG.VERTICAL_SPACING,
    };
  });

  // 重新布局信号节点
  signalNodes.forEach((node, index) => {
    node.position = {
      x: LAYOUT_CONFIG.SIGNAL_COLUMN * LAYOUT_CONFIG.HORIZONTAL_SPACING,
      y: index * LAYOUT_CONFIG.VERTICAL_SPACING,
    };
  });

  return [...indicatorNodes, ...signalNodes];
}

/**
 * 验证策略JSON的基本结构
 */
export function validateTradeStrategy(strategy: any): boolean {
  if (!strategy || typeof strategy !== 'object') {
    return false;
  }

  // 检查必要字段
  if (!Array.isArray(strategy.indicators) && !Array.isArray(strategy.signals)) {
    return false;
  }

  // 验证指标结构
  if (strategy.indicators) {
    for (const indicator of strategy.indicators) {
      if (!indicator.id || !indicator.type || !indicator.params) {
        return false;
      }
    }
  }

  // 验证信号结构
  if (strategy.signals) {
    for (const signal of strategy.signals) {
      if (!signal.id || !signal.type) {
        return false;
      }
      // inputs 可以是数组或对象，或者不存在
      if (signal.inputs && !Array.isArray(signal.inputs) && typeof signal.inputs !== 'object') {
        return false;
      }
    }
  }

  return true;
}
