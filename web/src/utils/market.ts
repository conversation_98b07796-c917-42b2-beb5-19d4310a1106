/**
 * 根据环境获取市场数据的正确 URL
 * @param market 市场类型 ('cn' | 'us' | 'crypto')
 * @param fileName 文件名
 * @returns 完整的数据 URL
 */
export async function getMarketDataUrl(
  market: string,
  fileName: string
): Promise<string> {
  const isProduction = process.env.NODE_ENV === 'production';

  if (isProduction) {
    // 生产环境：直接返回 R2 URL
    return `https://media.i365.tech/myinvestpilot/markets/${market}/${fileName}`;
  }

  // 开发环境：使用本地 API 路由
  return `/api/proxy-market/${market}/${fileName}`;
}

/**
 * 获取市场数据
 * @param market 市场类型
 * @param fileName 文件名
 * @returns 解析后的 JSON 数据
 */
export async function fetchMarketData<T>(
  market: string,
  fileName: string
): Promise<T> {
  const url = await getMarketDataUrl(market, fileName);
  const response = await fetch(url);

  if (!response.ok) {
    throw new Error(`Failed to fetch market data: ${response.statusText}`);
  }

  return response.json();
}
