import { EChartsOption } from 'echarts';

import { ChartData } from '../../types/types';

export const generateChartOptions = (data: ChartData): EChartsOption => {
  return {
    grid: {
      top: 100,
      right: 10,
      bottom: 24,
      left: 40,
    },
    legend: {
      data: [
        '组合净值',
        '中证500',
        '沪深300',
        '创业板',
        '恒生指数',
        '标普500',
        '纳斯达克',
        '德国DAX',
        '日经225',
        '韩国综合',
        '澳大利亚标普200',
        '印度孟买',
        '15%基准',
      ],
      bottom: 'auto',
    },
    xAxis: {
      type: 'category',
      data: data.dates,
    },
    yAxis: {
      type: 'value',
      min: 'dataMin',
      max: 'dataMax',
    },
    series: [
      {
        name: '组合净值',
        data: data.netValues,
        type: 'line',
        smooth: true,
        lineStyle: { width: 2 },
      },
      {
        name: '中证500',
        data: data.ZZ500,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '沪深300',
        data: data.HS300,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '创业板',
        data: data.CYB,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '恒生指数',
        data: data.HSI,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '标普500',
        data: data.SPX,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '纳斯达克',
        data: data.IXIC,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '德国DAX',
        data: data.GDAXI,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '日经225',
        data: data.N225,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '韩国综合',
        data: data.KS11,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '澳大利亚标普200',
        data: data.AS51,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '印度孟买',
        data: data.SENSEX,
        type: 'line',
        smooth: true,
        lineStyle: { width: 1 },
        showSymbol: false,
      },
      {
        name: '15%基准',
        data: data.annualReturn15,
        type: 'line',
        smooth: true,
        lineStyle: { width: 0.5, type: 'dotted' },
        showSymbol: false,
      },
    ],
    tooltip: {
      trigger: 'axis',
      order: 'valueDesc',
    },
  };
};
