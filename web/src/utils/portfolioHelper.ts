/**
 * Portfolio Helper Functions
 * 用于判断组合类型和处理组合相关的逻辑
 */

// 组合类型枚举
export enum PortfolioType {
  CODE_STRATEGY = 'CODE_STRATEGY',
  DSL_STRATEGY = 'DSL_STRATEGY'
}

// 判断组合是否为代码策略类型
export function isCodeStrategyPortfolio(portfolio: any): boolean {
  // 代码策略：有strategy和capital_strategy字段，且没有DSL相关字段
  return !!(
    portfolio.strategy && 
    portfolio.capital_strategy && 
    !portfolio.strategy_definition &&
    !portfolio.parameters?.trade_strategy_dsl
  );
}

// 判断组合是否为原语DSL策略类型
export function isDslStrategyPortfolio(portfolio: any): boolean {
  // 原语策略：有strategy_definition字段或者有parameters.trade_strategy_dsl字段
  return !!(
    portfolio.strategy_definition || 
    portfolio.parameters?.trade_strategy_dsl
  );
}

// 获取组合类型
export function getPortfolioType(portfolio: any): PortfolioType {
  if (isCodeStrategyPortfolio(portfolio)) {
    return PortfolioType.CODE_STRATEGY;
  }
  if (isDslStrategyPortfolio(portfolio)) {
    return PortfolioType.DSL_STRATEGY;
  }
  throw new Error('无法识别的组合类型');
}

// 获取更新页面的路径
export function getUpdatePagePath(portfolioCode: string, portfolio: any): string {
  const portfolioType = getPortfolioType(portfolio);
  
  switch (portfolioType) {
    case PortfolioType.CODE_STRATEGY:
      return `/portfolio?mode=update&code=${portfolioCode}`;
    case PortfolioType.DSL_STRATEGY:
      return `/portfolios/create?mode=update&code=${portfolioCode}`;
    default:
      throw new Error('无法确定更新页面路径');
  }
}

// 获取复制页面的路径
export function getCopyPagePath(portfolioCode: string, portfolio: any): string {
  const portfolioType = getPortfolioType(portfolio);
  
  switch (portfolioType) {
    case PortfolioType.CODE_STRATEGY:
      return `/portfolio?mode=copy&code=${portfolioCode}`;
    case PortfolioType.DSL_STRATEGY:
      // 原语策略不支持复制模式，跳转到创建页面
      return `/portfolios/create`;
    default:
      throw new Error('无法确定复制页面路径');
  }
}

// 获取组合类型的显示名称
export function getPortfolioTypeDisplayName(portfolio: any): string {
  const portfolioType = getPortfolioType(portfolio);
  
  switch (portfolioType) {
    case PortfolioType.CODE_STRATEGY:
      return '代码策略组合';
    case PortfolioType.DSL_STRATEGY:
      return '原语策略组合';
    default:
      return '未知类型组合';
  }
}

// 验证组合数据结构
export function validatePortfolioStructure(portfolio: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 基础字段验证
  if (!portfolio.name) errors.push('组合名称不能为空');
  if (!portfolio.code) errors.push('组合代码不能为空');
  if (!portfolio.symbols || !Array.isArray(portfolio.symbols) || portfolio.symbols.length === 0) {
    errors.push('投资标的不能为空');
  }
  if (!portfolio.start_date) errors.push('建仓时间不能为空');
  if (!portfolio.currency) errors.push('基础货币不能为空');
  if (!portfolio.market) errors.push('交易市场不能为空');
  
  // 策略结构验证
  const hasCodeStrategy = portfolio.strategy && portfolio.capital_strategy;
  const hasDslStrategy = portfolio.strategy_definition || portfolio.parameters?.trade_strategy_dsl;
  
  if (!hasCodeStrategy && !hasDslStrategy) {
    errors.push('必须包含策略定义（代码策略或DSL策略）');
  }
  if (hasCodeStrategy && hasDslStrategy) {
    errors.push('不能同时包含代码策略和DSL策略定义');
  }
  
  // 代码策略验证
  if (hasCodeStrategy) {
    if (!portfolio.strategy.name) errors.push('交易策略名称不能为空');
    if (!portfolio.capital_strategy.name) errors.push('资金策略名称不能为空');
  }
  
  // DSL策略验证
  if (hasDslStrategy) {
    // 检查新格式（strategy_definition）
    if (portfolio.strategy_definition) {
      if (!portfolio.strategy_definition.trade_strategy) {
        errors.push('DSL策略必须包含交易策略定义');
      }
      if (!portfolio.strategy_definition.capital_strategy) {
        errors.push('DSL策略必须包含资金策略定义');
      }
    }
    if (portfolio.parameters?.trade_strategy_dsl) {
      // 检查旧格式（parameters.trade_strategy_dsl）
      if (!portfolio.parameters.trade_strategy_dsl) {
        errors.push('DSL策略必须包含交易策略定义');
      }
      if (!portfolio.parameters?.capital_strategy_params) {
        errors.push('DSL策略必须包含资金策略参数');
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// 根据波动率计算风险等级
export const getRiskLevel = (volatility: number): {
  level: string;
  color: string;
  description: string;
  bgColor: string;
} => {
  // 基于金融业界标准的波动率分类，更细化处理高波动率
  
  if (volatility <= 0.10) {
    return {
      level: '极低风险',
      color: 'text-green-700',
      description: '波动率极低，适合极保守投资者',
      bgColor: 'bg-green-50 border-green-400'
    };
  }
  
  if (volatility <= 0.20) {
    return {
      level: '低风险',
      color: 'text-green-600',
      description: '波动率较低，适合保守投资者',
      bgColor: 'bg-green-100 border-green-300'
    };
  }
  
  if (volatility <= 0.35) {
    return {
      level: '中低风险',
      color: 'text-blue-600',
      description: '波动率中等偏低，适合稳健投资者',
      bgColor: 'bg-blue-100 border-blue-300'
    };
  }
  
  if (volatility <= 0.50) {
    return {
      level: '中等风险',
      color: 'text-yellow-600',
      description: '波动率适中，适合平衡型投资者',
      bgColor: 'bg-yellow-100 border-yellow-300'
    };
  }
  
  if (volatility <= 0.80) {
    return {
      level: '中高风险',
      color: 'text-orange-600',
      description: '波动率偏高，适合积极投资者',
      bgColor: 'bg-orange-100 border-orange-300'
    };
  }
  
  if (volatility <= 1.20) {
    return {
      level: '高风险',
      color: 'text-red-600',
      description: '波动率很高，适合激进投资者',
      bgColor: 'bg-red-100 border-red-300'
    };
  }
  
  return {
    level: '极高风险',
    color: 'text-red-800',
    description: '波动率极高，仅适合专业高风险投资者',
    bgColor: 'bg-red-200 border-red-500'
  };
};

export const formatVolatility = (volatility: number): string => {
  return `${(volatility * 100).toFixed(2)}%`;
}; 