import {
  Portfolio,
  Profile,
  PublicPortfolioInfo,
  GroupedPortfolios,
  CreatePortfolioRequest,
  UpdatePortfolioRequest,
  PortfolioSymbol,
  StrategyDSLResponse,
  StrategyGenerationRequest,
} from '../../types/types';

// API 基础 URL
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

interface FetchOptions extends Omit<RequestInit, 'body'> {
  body?: any;
}

export class ApiError extends Error {
  status: number;

  statusText: string;

  constructor(status: number, statusText: string, message: string) {
    super(message);
    this.status = status;
    this.statusText = statusText;
    this.name = 'ApiError';
  }
}

async function fetchApi<T>(
  endpoint: string,
  options: FetchOptions = {},
  is_public = false,
  serverCookie?: string
): Promise<T> {
  const { method = 'GET', headers, body, ...restOptions } = options;

  const url = new URL(endpoint, API_BASE_URL);

  const fetchOptions: RequestInit = {
    method,
    ...restOptions,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  };

  if (body) {
    fetchOptions.body = JSON.stringify(body);
  }

  if (!is_public) {
    if (typeof window !== 'undefined') {
      // 客户端请求
      fetchOptions.credentials = 'include';
    } else if (serverCookie) {
      // 服务器端请求
      fetchOptions.headers = {
        ...fetchOptions.headers,
        Cookie: serverCookie,
      };
    }
  }

  const response = await fetch(url.toString(), fetchOptions);

  if (response.status >= 400 && response.status <= 499) {
    const data = await response.json().catch(() => ({}));
    throw new ApiError(
      response.status,
      response.statusText,
      data.error || 'Client error'
    );
  } else if (response.status >= 500) {
    throw new ApiError(response.status, response.statusText, 'Server error');
  }

  // 检查内容类型，如果是JSON则解析，否则返回原始响应
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  }
  return response.text() as unknown as T;
}

// 获取用户投资组合
export async function getUserPortfolios(
  serverCookie?: string
): Promise<Portfolio[]> {
  const data = await fetchApi<{ portfolios: Portfolio[] }>(
    '/strategy_portfolio/user_portfolios',
    {},
    false,
    serverCookie
  );
  return data.portfolios;
}

// 获取用户个人资料
export async function getProfile(serverCookie?: string): Promise<Profile> {
  return fetchApi<Profile>('/profile', {}, false, serverCookie);
}

// 获取组合订阅状态

export async function getPortfolioSubscriptionStatus(
  portfolioCode: string
): Promise<boolean> {
  return fetchApi<{ status: 'active' | 'inactive' }>(
    `/portfolios/check_subscription/${portfolioCode}`
  ).then((data) => data.status === 'active');
}

// 订阅组合

export async function subscribePortfolio(
  portfolioCode: string,
  status: 'active' | 'inactive'
): Promise<void> {
  return fetchApi<void>('/portfolios/subscribe_signal', {
    method: 'POST',
    body: {
      portfolio_code: portfolioCode,
      status,
      language: 'cn',
    },
  });
}

export async function timeFlyPortfolio(
  portfolioCode: string,
  year: string
): Promise<{ portfolio_db_url: string }> {
  return fetchApi<{ data: { portfolio_db_url: string } }>(
    '/strategy_portfolio/time_fly',
    {
      method: 'POST',
      body: { code: portfolioCode, year },
    }
  ).then((response) => response.data);
}

export async function getPublicPortfolioInfo(
  code: string
): Promise<PublicPortfolioInfo> {
  return fetchApi<PublicPortfolioInfo>(
    `/strategy_portfolio/portfolios/public/${code}`,
    {},
    true
  );
}

export async function updatePortfolioData(
  code: string
): Promise<{ portfolio_db_url: string }> {
  return fetchApi<{ data: { portfolio_db_url: string } }>(
    '/strategy_portfolio/update_portfolio',
    {
      method: 'POST',
      body: {
        code,
      },
    },
    false
  ).then((response) => response.data);
}

export const getOfficialPortfolios = async (
  groupBy: 'market' | 'strategy' | 'capital_strategy' = 'market',
  serverCookie?: string
): Promise<GroupedPortfolios[]> => {
  return fetchApi<GroupedPortfolios[]>(
    `/strategy_portfolio/official_portfolios?groupBy=${groupBy}`,
    {},
    true,
    serverCookie
  );
};

export async function getPortfolioDetails(
  portfolioId: string,
  serverCookie?: string
): Promise<Portfolio> {
  return fetchApi<Portfolio>(
    `/strategy_portfolio/portfolios/${portfolioId}`,
    {},
    false,
    serverCookie
  );
}

export async function createPortfolio(
  portfolioData: CreatePortfolioRequest,
  serverCookie?: string
): Promise<Portfolio> {
  return fetchApi<Portfolio>(
    '/strategy_portfolio/portfolios',
    {
      method: 'POST',
      body: portfolioData,
    },
    false,
    serverCookie
  );
}

export async function updatePortfolio(
  portfolioId: string,
  portfolioData: UpdatePortfolioRequest,
  serverCookie?: string
): Promise<Portfolio> {
  return fetchApi<Portfolio>(
    `/strategy_portfolio/portfolios/${portfolioId}`,
    {
      method: 'PUT',
      body: portfolioData,
    },
    false,
    serverCookie
  );
}

export async function deletePortfolio(
  portfolioId: string,
  serverCookie?: string
): Promise<void> {
  return fetchApi<void>(
    `/strategy_portfolio/portfolios/${portfolioId}`,
    {
      method: 'DELETE',
    },
    false,
    serverCookie
  );
}

export async function searchSymbols(
  query: string,
  currency: string
): Promise<PortfolioSymbol[]> {
  const params = new URLSearchParams({
    q: query,
    cur: currency,
  });

  return fetchApi<{ results: PortfolioSymbol[] }>(
    `/strategy_portfolio/search_symbols?${params.toString()}`,
    {},
    false
  ).then((data) => data.results);
}

export async function getSubscribedPortfolios(
  serverCookie?: string
): Promise<{ portfolio_code: string }[]> {
  const response = await fetchApi<{
    portfolios: { portfolio_code: string }[];
  }>('/get_subscribed_portfolios/', {}, false, serverCookie);
  return response.portfolios;
}

export async function getPortfolioSignalDatabase(
  slug: string
): Promise<ArrayBuffer> {
  const url = new URL(
    `/strategy_portfolio/portfolios/signals/${slug}`,
    API_BASE_URL
  );

  // Note: we need to use fetch API because fetchApi does not support arrayBuffer response type
  const response = await fetch(url.toString(), {
    method: 'GET',
    headers: {
      Accept: 'application/octet-stream',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.arrayBuffer();
}

export async function getSignalAnalysisUrl(
  portfolioCode: string,
  serverCookie?: string
): Promise<{ presignedUrl: string }> {
  return fetchApi<{ presignedUrl: string }>(
    `/strategy_portfolio/portfolios/signals_url/${portfolioCode}`,
    {},
    false,
    serverCookie
  );
}

export async function generateStrategyDSL(
  payload: StrategyGenerationRequest
): Promise<StrategyDSLResponse> {
  return fetchApi<StrategyDSLResponse>('/strategy_portfolio/ai/strategy/generate', {
    method: 'POST',
    body: payload,
  });
}

export function getAIStrategyPromptVersionUrl(version: string): string {
  return `${API_BASE_URL}/strategy_portfolio/ai/strategy/prompt_versions/${version}`;
}
