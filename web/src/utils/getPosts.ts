import { IPostResponse } from '../../types/types';

export const getPost = async (
  id?: string,
  cookies?: string
): Promise<IPostResponse> => {
  if (!id) {
    return {
      slug: '',
      cover_image: '',
      meta: '',
      extra: '',
      post_date: 0,
      title: '',
      markdown: '',
      html: '',
      type: '',
    };
  }
  const headers = new Headers();
  if (cookies) {
    headers.set('Cookie', cookies);
  }
  const res = await fetch(`https://api.myinvestpilot.com/posts/${id}`, {
    headers,
  });
  if (!res.ok) {
    const errorMessage = await res.text();
    throw new Error(errorMessage);
  }
  const post = await res.json();
  return { ...post, slug: id };
};
